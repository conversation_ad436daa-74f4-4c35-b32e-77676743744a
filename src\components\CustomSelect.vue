<template>
  <div class="custom-select-container position-relative">
    <div
      class="custom-select-badge d-flex align-items-center justify-content-between"
      :class="[badgeClass, { 'disabled': disabled }]"
      @click="toggleDropdown"
      :id="`custom-select-${uniqueId}`"
    >
      <span class="select-text">{{ selectedLabel || placeholder }}</span>
      <i class="fas fa-chevron-down select-dropdown-icon" :class="{ 'rotated': isOpen }"></i>
    </div>

    <!-- Dropdown de opções - renderizado via portal para evitar problemas com modais -->
    <Teleport to="body">
      <div
        class="custom-select-dropdown"
        :class="{ 'show': isOpen, 'compact-dropdown': compact }"
        v-if="isOpen"
        :style="dropdownStyle"
        ref="dropdown"
      >
        <div class="custom-select-dropdown-content">
          <div
            v-for="option in options"
            :key="option.value"
            class="custom-select-option"
            :class="{ 'active': modelValue === option.value }"
            @click="selectOption(option)"
          >
            <div class="custom-select-option-badge" :class="getOptionClass(option)">
              <span class="custom-select-option-text">{{ option.label }}</span>
            </div>
          </div>

          <!-- Opção para adicionar novo item (se permitido) -->
          <div
            v-if="allowCustom && showAddNew"
            class="custom-select-option custom-add-option"
            @click="showCustomInput = true"
          >
            <div class="custom-select-option-badge bg-gradient-success">
              <i class="fas fa-plus me-1"></i>
              <span class="custom-select-option-text">Adicionar novo</span>
            </div>
          </div>

          <!-- Input para adicionar novo item -->
          <div v-if="showCustomInput" class="custom-input-container">
            <div class="input-group input-group-sm">
              <input
                type="text"
                class="form-control"
                v-model="newOptionText"
                @keydown.enter="addNewOption"
                @keydown.escape="cancelAddNew"
                placeholder="Digite o novo item..."
                ref="customInput"
              />
              <button class="btn btn-success btn-sm" @click="addNewOption" :disabled="!newOptionText.trim()">
                <i class="fas fa-check"></i>
              </button>
              <button class="btn btn-secondary btn-sm" @click="cancelAddNew">
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script>
export default {
  name: "CustomSelect",
  props: {
    modelValue: {
      type: [String, Number],
      default: null
    },
    options: {
      type: Array,
      required: true,
      // Formato: [{ value: 'valor', label: 'Label', color: 'primary' }]
    },
    placeholder: {
      type: String,
      default: 'Selecione uma opção'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'md', // sm, md, lg
      validator: value => ['sm', 'md', 'lg'].includes(value)
    },
    allowCustom: {
      type: Boolean,
      default: false
    },
    customColorClass: {
      type: String,
      default: 'bg-gradient-primary'
    },
    variant: {
      type: String,
      default: 'filled',
      validator: value => ['filled', 'outline'].includes(value)
    },
    compact: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'option-added'],
  data() {
    return {
      isOpen: false,
      uniqueId: Math.random().toString(36).substring(2, 11),
      showCustomInput: false,
      newOptionText: ''
    };
  },
  computed: {
    selectedOption() {
      return this.options.find(option => option.value === this.modelValue);
    },
    selectedLabel() {
      return this.selectedOption ? this.selectedOption.label : null;
    },
    badgeClass() {
      const baseClass = 'custom-select-badge-clickable';
      const sizeClass = `custom-select-${this.size}`;
      const variantClass = `custom-select-${this.variant}`;
      const compactClass = this.compact ? 'custom-select-compact' : '';

      if (this.selectedOption && this.selectedOption.color) {
        if (this.variant === 'outline') {
          return `${baseClass} ${sizeClass} ${variantClass} ${compactClass} border-${this.selectedOption.color} text-${this.selectedOption.color}`;
        } else {
          return `${baseClass} ${sizeClass} ${variantClass} ${compactClass} bg-gradient-${this.selectedOption.color}`;
        }
      }

      if (this.variant === 'outline') {
        return `${baseClass} ${sizeClass} ${variantClass} ${compactClass} border-secondary text-secondary`;
      } else {
        return `${baseClass} ${sizeClass} ${variantClass} ${compactClass} bg-gradient-secondary`;
      }
    },
    showAddNew() {
      return this.allowCustom && !this.showCustomInput;
    },
    dropdownStyle() {
      if (!this.isOpen) return {};

      // Calcular posição absoluta na viewport
      const element = document.querySelector(`#custom-select-${this.uniqueId}`);

      if (!element) return {};

      const rect = element.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      const spaceBelow = viewportHeight - rect.bottom - 10; // 10px de margem
      const spaceAbove = rect.top - 10; // 10px de margem

      // Definir altura máxima baseada no espaço disponível
      let maxHeight = Math.max(spaceBelow, spaceAbove, 200); // Mínimo 200px
      maxHeight = Math.min(maxHeight, 300); // Máximo 300px

      // Decidir se abre para cima ou para baixo
      const openUpward = spaceBelow < 200 && spaceAbove > spaceBelow;

      // Calcular posição horizontal
      let left = rect.left;
      let width = rect.width;

      // Ajustar se o dropdown sair da viewport
      if (left + width > viewportWidth - 10) {
        left = viewportWidth - width - 10;
      }
      if (left < 10) {
        left = 10;
        width = Math.min(width, viewportWidth - 20);
      }

      return {
        position: 'fixed',
        top: openUpward ? 'auto' : `${rect.bottom + 4}px`,
        bottom: openUpward ? `${viewportHeight - rect.top + 4}px` : 'auto',
        left: `${left}px`,
        width: `${width}px`,
        maxHeight: `${maxHeight}px`,
        overflowY: 'auto',
        zIndex: 9999 // Z-index muito alto para ficar acima de tudo
      };
    }
  },
  mounted() {
    // Adicionar listener para fechar dropdown ao clicar fora
    document.addEventListener('click', this.handleClickOutside);
    // Adicionar listeners para reposicionar dropdown
    window.addEventListener('resize', this.updateDropdownPosition);
    window.addEventListener('scroll', this.updateDropdownPosition, true);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
    window.removeEventListener('resize', this.updateDropdownPosition);
    window.removeEventListener('scroll', this.updateDropdownPosition, true);
  },
  methods: {
    toggleDropdown() {
      if (this.disabled) return;

      this.isOpen = !this.isOpen;

      if (!this.isOpen) {
        this.cancelAddNew();
      } else {
        // Forçar recálculo da posição quando abrir
        this.$nextTick(() => {
          this.updateDropdownPosition();
        });
      }
    },
    updateDropdownPosition() {
      // Forçar recálculo do dropdownStyle
      if (this.isOpen && this.$refs.dropdown) {
        this.$forceUpdate();
      }
    },
    selectOption(option) {
      this.$emit('update:modelValue', option.value);
      this.isOpen = false;
      this.cancelAddNew();
    },
    getOptionClass(option) {
      if (option.color) {
        if (this.variant === 'outline') {
          return `border border-${option.color} text-${option.color} bg-white`;
        } else {
          return `bg-gradient-${option.color}`;
        }
      }

      if (this.variant === 'outline') {
        return 'border border-secondary text-secondary bg-white';
      } else {
        return 'bg-gradient-secondary';
      }
    },
    handleClickOutside(event) {
      const container = this.$el;
      const dropdown = this.$refs.dropdown;

      // Verificar se o clique foi fora do container principal E fora do dropdown
      if (container && !container.contains(event.target) &&
          (!dropdown || !dropdown.contains(event.target))) {
        this.isOpen = false;
        this.cancelAddNew();
      }
    },
    addNewOption() {
      if (!this.newOptionText.trim()) return;
      
      const newOption = {
        value: this.newOptionText.trim().toUpperCase().replace(/\s+/g, '_'),
        label: this.newOptionText.trim(),
        color: this.customColorClass.replace('bg-gradient-', '')
      };
      
      this.$emit('option-added', newOption);
      this.$emit('update:modelValue', newOption.value);
      
      this.cancelAddNew();
      this.isOpen = false;
    },
    cancelAddNew() {
      this.showCustomInput = false;
      this.newOptionText = '';
    }
  },
  watch: {
    showCustomInput(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.$refs.customInput?.focus();
        });
      }
    }
  }
};
</script>

<style scoped>
/* Container principal */
.custom-select-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

/* Badge principal clicável */
.custom-select-badge-clickable {
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  position: relative;
  padding: 0.5rem 0.75rem;
  min-height: 38px;
  border-radius: 6px;
  color: white;
  font-weight: 600;
  width: 100%;
  border: none;
}

.custom-select-badge-clickable.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.custom-select-badge-clickable:not(.disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Tamanhos */
.custom-select-sm {
  padding: 0.25rem 0.5rem;
  min-height: 28px;
  font-size: 0.8rem;
}

.custom-select-md {
  padding: 0.5rem 0.75rem;
  min-height: 38px;
  font-size: 0.9rem;
}

.custom-select-lg {
  padding: 0.75rem 1rem;
  min-height: 48px;
  font-size: 1rem;
}

/* Modo Compacto */
.custom-select-compact {
  padding: 0.15rem 0.4rem !important;
  min-height: 20px !important;
  font-size: 0.65rem !important;
  border-radius: 4px !important;
}

.custom-select-compact .select-text {
  font-size: 0.65rem;
  line-height: 1.1;
}

.custom-select-compact .select-dropdown-icon {
  font-size: 0.55rem;
  margin-left: 0.3rem;
}

/* Variants */
.custom-select-outline {
  background: white !important;
  border: 2px solid;
}

.custom-select-outline:not(.disabled):hover {
  background: rgba(0, 0, 0, 0.05) !important;
}

.custom-select-filled {
  border: none;
  color: white;
}

.custom-select-filled .select-text {
  color: white;
}

.custom-select-filled .select-dropdown-icon {
  color: white;
}

/* Texto e ícone */
.select-text {
  flex: 1;
  text-align: center;
  line-height: 1.2;
}

.select-dropdown-icon {
  font-size: 0.8rem;
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.select-dropdown-icon.rotated {
  transform: rotate(180deg);
}

/* Dropdown */
.custom-select-dropdown {
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Posicionamento será controlado via JavaScript */
}

.custom-select-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.custom-select-dropdown-content {
  padding: 0.5rem 0;
  /* Altura controlada pelo dropdownStyle computed */
}

/* Scroll customizado para o dropdown */
.custom-select-dropdown::-webkit-scrollbar {
  width: 6px;
}

.custom-select-dropdown::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.custom-select-dropdown::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.custom-select-dropdown::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Opções do dropdown */
.custom-select-option {
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.custom-select-option:last-child {
  border-bottom: none;
}

.custom-select-option:hover {
  background-color: rgba(0, 0, 0, 0.05);
  padding-left: 1rem;
}

.custom-select-option.active {
  background-color: rgba(86, 128, 159, 0.1);
}

.custom-select-option.active:hover {
  background-color: rgba(86, 128, 159, 0.15);
}

.custom-select-option-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
  color: white;
}

.custom-select-option-text {
  display: block;
}

/* Opções compactas no dropdown */
.compact-dropdown .custom-select-option {
  padding: 0.35rem 0.5rem;
}

.compact-dropdown .custom-select-option:hover {
  padding-left: 0.65rem;
}

.compact-dropdown .custom-select-option-badge {
  padding: 0.15rem 0.35rem;
  font-size: 0.65rem;
  border-radius: 3px;
}

/* Opção de adicionar novo */
.custom-add-option {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin-top: 0.25rem;
  padding-top: 0.75rem;
}

/* Input customizado */
.custom-input-container {
  padding: 0.5rem 0.75rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background-color: rgba(0, 0, 0, 0.02);
}

/* Classes de cores gradiente */
.bg-gradient-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.bg-gradient-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.bg-gradient-info {
  background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.bg-gradient-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.bg-gradient-dark {
  background: linear-gradient(135deg, #343a40 0%, #23272b 100%);
}

.bg-gradient-orange {
  background: linear-gradient(135deg, #fd7e14 0%, #ff8c00 100%);
}

.bg-gradient-purple {
  background: linear-gradient(135deg, #6f42c1 0%, #8e44ad 100%);
}

.bg-gradient-dark-green {
  background: linear-gradient(135deg, #1e7e34 0%, #28a745 100%);
}
</style>
