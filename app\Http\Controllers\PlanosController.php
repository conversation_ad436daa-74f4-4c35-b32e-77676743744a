<?php

namespace App\Http\Controllers;

use App\Models\Plano;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;

class PlanosController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Plano::query();

        // Filtros opcionais
        if ($request->has('ativo')) {
            $query->where('ativo', $request->boolean('ativo'));
        }

        if ($request->has('modulo_ortodontia')) {
            $query->where('modulo_ortodontia', $request->boolean('modulo_ortodontia'));
        }

        // Ordenação
        $orderBy = $request->get('order_by', 'nome');
        $orderDirection = $request->get('order_direction', 'asc');
        
        $allowedOrderFields = ['nome', 'valor_mensal', 'created_at', 'ativo'];
        if (in_array($orderBy, $allowedOrderFields)) {
            $query->orderBy($orderBy, $orderDirection);
        }

        // Incluir contagem de clínicas
        $query->withCount('clinicas');

        $planos = $query->get();

        return response()->json([
            'success' => true,
            'data' => $planos
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'nome' => 'required|string|max:255|unique:planos,nome',
            'descricao' => 'nullable|string',
            'cor' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'ativo' => 'required|in:true,false,1,0',
            'auto_registro' => 'sometimes|in:true,false,1,0',
            'modulo_clinica' => 'required|in:true,false,1,0',
            'modulo_ortodontia' => 'required|in:true,false,1,0',
            'quantidade_usuarios' => 'nullable|integer|min:1',
            'quantidade_ortodontistas' => 'nullable|integer|min:1',
            'quantidade_agendas' => 'nullable|integer|min:1',
            'quantidade_cadeiras' => 'nullable|integer|min:1',
            'meses_fidelidade_minima' => 'integer|min:0',
            'dias_gratuitos' => 'nullable|integer|min:0',
            'trial_available' => 'sometimes|in:true,false,1,0',
            'quantidade_mentorias_mensais' => 'nullable|integer|min:1',
            'valor_mensal' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dados inválidos',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validações customizadas
        $customValidation = $this->validateCustomRules($request);
        if ($customValidation) {
            return $customValidation;
        }

        // Converter valores booleanos
        $data = $validator->validated();
        $data = $this->convertBooleanFields($data);

        $plano = Plano::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Plano criado com sucesso',
            'data' => $plano
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $plano = Plano::with(['clinicas' => function ($query) {
            $query->select('id', 'nome', 'slug', 'plano_id');
        }])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $plano
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $plano = Plano::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'nome' => ['sometimes', 'string', 'max:255', Rule::unique('planos', 'nome')->ignore($plano->id)],
            'descricao' => 'nullable|string',
            'cor' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'ativo' => 'sometimes|in:true,false,1,0',
            'auto_registro' => 'sometimes|in:true,false,1,0',
            'modulo_clinica' => 'sometimes|in:true,false,1,0',
            'modulo_ortodontia' => 'sometimes|in:true,false,1,0',
            'quantidade_usuarios' => 'nullable|integer|min:1',
            'quantidade_ortodontistas' => 'nullable|integer|min:1',
            'quantidade_agendas' => 'nullable|integer|min:1',
            'quantidade_cadeiras' => 'nullable|integer|min:1',
            'meses_fidelidade_minima' => 'integer|min:0',
            'dias_gratuitos' => 'nullable|integer|min:0',
            'trial_available' => 'sometimes|in:true,false,1,0',
            'quantidade_mentorias_mensais' => 'nullable|integer|min:1',
            'valor_mensal' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Dados inválidos',
                'errors' => $validator->errors()
            ], 422);
        }

        // Validações customizadas
        $customValidation = $this->validateCustomRules($request, $plano);
        if ($customValidation) {
            return $customValidation;
        }

        // Converter valores booleanos
        $data = $validator->validated();
        $data = $this->convertBooleanFields($data);

        $plano->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Plano atualizado com sucesso',
            'data' => $plano->fresh()
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $plano = Plano::findOrFail($id);

        // Verificar se existem clínicas usando este plano
        $clinicasCount = $plano->clinicas()->count();
        
        if ($clinicasCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "Não é possível excluir este plano pois {$clinicasCount} clínica(s) estão utilizando-o"
            ], 422);
        }

        $plano->delete();

        return response()->json([
            'success' => true,
            'message' => 'Plano excluído com sucesso'
        ]);
    }

    /**
     * Toggle the active status of a plan
     */
    public function toggleStatus(string $id): JsonResponse
    {
        $plano = Plano::findOrFail($id);
        
        $plano->update(['ativo' => !$plano->ativo]);

        $status = $plano->ativo ? 'ativado' : 'desativado';

        return response()->json([
            'success' => true,
            'message' => "Plano {$status} com sucesso",
            'data' => $plano
        ]);
    }

    /**
     * Get plans available for assignment to clinics
     */
    public function forClinicas(): JsonResponse
    {
        $planos = Plano::ativo()->orderBy('nome')->get();

        return response()->json([
            'success' => true,
            'data' => $planos
        ]);
    }

    /**
     * Validações customizadas para regras de negócio
     */
    private function validateCustomRules(Request $request, ?Plano $plano = null): ?JsonResponse
    {
        // Validação: mentorias só podem ser definidas se módulo ortodontia estiver ativo
        $moduloOrtodontia = $request->has('modulo_ortodontia')
            ? $request->boolean('modulo_ortodontia')
            : ($plano ? $plano->modulo_ortodontia : false);

        if ($request->filled('quantidade_mentorias_mensais') && !$moduloOrtodontia) {
            return response()->json([
                'success' => false,
                'message' => 'Quantidade de mentorias só pode ser definida se o módulo de ortodontia estiver ativo'
            ], 422);
        }

        // Validação: módulo clínica sempre deve estar ativo
        if ($request->has('modulo_clinica') && !$request->boolean('modulo_clinica')) {
            return response()->json([
                'success' => false,
                'message' => 'O módulo clínica é obrigatório e não pode ser desativado'
            ], 422);
        }

        // Validação: se valor mensal for 0, deve ser null (gratuito)
        if ($request->has('valor_mensal') && $request->get('valor_mensal') == 0) {
            $request->merge(['valor_mensal' => null]);
        }

        return null;
    }

    /**
     * Converter campos booleanos para valores corretos
     */
    private function convertBooleanFields(array $data): array
    {
        $booleanFields = ['ativo', 'auto_registro', 'modulo_clinica', 'modulo_ortodontia', 'trial_available'];

        foreach ($booleanFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = filter_var($data[$field], FILTER_VALIDATE_BOOLEAN);
            }
        }

        return $data;
    }
}
