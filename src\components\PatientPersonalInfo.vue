<template>
  <div>
    <div class="row mt-2">
      <div class="col-sm-6 mb-2">
        <label for="paciente_ortodontista" class="form-control-label">Profissional</label>
        <select
          class="form-select"
          :id="'paciente_ortodontista' + (isMobile ? '_mobile' : '')"
          v-model="localData.dentista_id"
          :disabled="!isEditing"
          @change="updateField('dentista_id', localData.dentista_id)"
        >
          <option hidden>Selecionar...</option>
          <option
            v-for="dentista in dentistas"
            :key="dentista.id"
            :value="dentista.id"
          >
            {{ dentista.nome }}
          </option>
        </select>
      </div>
      <div class="col-sm-6 mb-2">
        <label for="dentista_clinica" class="form-control-label">Clínica</label>
        <select
          v-if="isSystemAdmin"
          class="form-select"
          :id="'dentista_clinica' + (isMobile ? '_mobile' : '')"
          v-model="localData.clinica_id"
          :disabled="!isEditing"
          @change="updateField('clinica.id', localData.clinica_id)"
        >
          <option hidden>Selecionar...</option>
          <option
            v-for="clinica in clinicas"
            :key="clinica.id"
            :value="clinica.id"
          >
            {{ clinica.nome }}
          </option>
        </select>
        <input
          v-else
          readonly
          class="form-control"
          type="text"
          :value="paciente.clinica.nome"
        />
      </div>
      <div class="col-sm-6 mb-2">
        <MaterialInput
          label="Nascimento"
          type="date"
          v-model="localData.data_nascimento"
          :id="'paciente_dataNascimento' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('data_nascimento')"
        />
      </div>
      <div class="col-sm-6 mb-2">
        <MaterialInput
          label="Como conheceu a clínica"
          type="text"
          v-model="localData.como_conheceu"
          :id="'paciente_como_conheceu' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('como_conheceu')"
        />
      </div>
      <div class="col-7 col-sm-6 mb-2">
        <MaterialInput
          label="CPF"
          type="text"
          v-model="localData.cpf"
          :id="'paciente_cpf' + (isMobile ? '_mobile' : '')"
          mask="###.###.###-##"
          :readonly="!isEditing"
          :input="handleFieldInput('cpf')"
        />
      </div>
      <div class="col-5 col-sm-6 mb-2">
        <MaterialInput
          type="text"
          label="RG"
          v-model="localData.rg"
          :id="'paciente_rg' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('rg')"
        />
      </div>
      <div class="col-sm-6 mb-2">
        <MaterialInput
          label="Nome da mãe"
          type="text"
          v-model="localData.nome_mae"
          :input="handleCapitalizeInput('nome_mae')"
          :id="'paciente_nome_mae' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
        />
      </div>
      <div class="col-sm-6 mb-2">
        <MaterialInput
          label="Nome do pai"
          type="text"
          v-model="localData.nome_pai"
          :input="handleCapitalizeInput('nome_pai')"
          :id="'paciente_nome_pai' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
        />
      </div>
      <template v-if="isMobile || !showResponsibleInfo">
        <div class="col-12">
          <p class="text-uppercase text-sm" style="font-weight: 600">
            Observações
          </p>
          <textarea
            class="form-control mt-3"
            :id="'paciente_observacoes' + (isMobile ? '_mobile' : '')"
            rows="3"
            v-model="localData.observacoes"
            :input="handleFieldInput('observacoes')"
          >
          </textarea>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import MaterialInput from "@/components/MaterialInput.vue";
import { capitalizeAll } from "@/helpers/utils.js";

export default {
  name: "PatientPersonalInfo",
  components: {
    MaterialInput
  },
  props: {
    paciente: {
      type: Object,
      required: true
    },
    isEditing: {
      type: Boolean,
      default: false
    },
    isMobile: {
      type: Boolean,
      default: false
    },
    dentistas: {
      type: Array,
      default: () => []
    },
    clinicas: {
      type: Array,
      default: () => []
    },
    isSystemAdmin: {
      type: Boolean,
      default: false
    },
    showResponsibleInfo: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      localData: {
        dentista_id: this.paciente.dentista_id,
        clinica_id: this.paciente.clinica?.id,
        data_nascimento: this.paciente.data_nascimento,
        como_conheceu: this.paciente.como_conheceu,
        cpf: this.paciente.cpf,
        rg: this.paciente.rg,
        nome_mae: this.paciente.nome_mae,
        nome_pai: this.paciente.nome_pai,
        observacoes: this.paciente.observacoes
      }
    };
  },
  watch: {
    paciente: {
      handler(newVal) {
        this.localData = {
          dentista_id: newVal.dentista_id,
          clinica_id: newVal.clinica?.id,
          data_nascimento: newVal.data_nascimento,
          como_conheceu: newVal.como_conheceu,
          cpf: newVal.cpf,
          rg: newVal.rg,
          nome_mae: newVal.nome_mae,
          nome_pai: newVal.nome_pai,
          observacoes: newVal.observacoes
        };
      },
      deep: true
    }
  },
  methods: {
    capitalizeAll,
    updateField(field, value) {
      this.$emit('update:field', { field, value });
    },
    handleCapitalizeInput(fieldName) {
      return (event) => {
        if (event && event.target) {
          capitalizeAll(event);
          this.localData[fieldName] = event.target.value;
          this.updateField(fieldName, this.localData[fieldName]);
        }
      };
    },
    handleFieldInput(fieldName) {
      return (event) => {
        if (event && event.target) {
          this.localData[fieldName] = event.target.value;
          this.updateField(fieldName, this.localData[fieldName]);
        }
      };
    }
  }
};
</script>
