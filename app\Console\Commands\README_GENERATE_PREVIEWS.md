# Comando: Gerar Previews de Imagens

## Descrição

Este comando <PERSON> gera previews (thumbnails) de 200px para todas as imagens existentes no sistema que ainda não possuem preview. É útil para processar retroativamente imagens que foram enviadas antes da implementação do sistema de thumbnails.

## Uso Básico

```bash
php artisan images:generate-previews
```

Este comando irá:
1. <PERSON><PERSON> todas as imagens na tabela `imagens` que não possuem preview (`has_preview = false`)
2. <PERSON><PERSON> todas as fotos de perfil de pacientes que não possuem preview (`profile_picture_has_preview = false`)
3. Gerar previews de 200px (lado maior) mantendo proporção
4. <PERSON>var os previews em `storage/app/images/preview/`
5. Atualizar o banco de dados marcando `has_preview = true`

## Opções Disponíveis

### `--dry-run`
Simula a execução sem fazer alterações no sistema.

```bash
php artisan images:generate-previews --dry-run
```

**Use isso primeiro** para ver quantas imagens serão processadas antes de executar de verdade.

### `--force`
Regenera previews mesmo para imagens que já possuem.

```bash
php artisan images:generate-previews --force
```

⚠️ **Cuidado**: Isso irá sobrescrever todos os previews existentes.

### `--limit=N`
Limita o número de imagens a processar.

```bash
php artisan images:generate-previews --limit=10
```

Útil para testar em produção processando apenas algumas imagens primeiro.

## Exemplos de Uso

### 1. Testar antes de executar (RECOMENDADO)
```bash
php artisan images:generate-previews --dry-run
```

### 2. Processar apenas 10 imagens como teste
```bash
php artisan images:generate-previews --limit=10
```

### 3. Processar todas as imagens sem preview
```bash
php artisan images:generate-previews
```

### 4. Regenerar todos os previews (forçar)
```bash
php artisan images:generate-previews --force
```

### 5. Combinar opções
```bash
php artisan images:generate-previews --force --limit=50
```

## Saída do Comando

O comando exibe:
- 📊 Progresso em tempo real com barra de progresso
- ✅ Número de imagens processadas com sucesso
- ⏭️ Número de imagens ignoradas (já possuem preview ou não encontradas)
- ❌ Número de erros encontrados

Exemplo de saída:
```
🖼️  Iniciando geração de previews de imagens...

📸 Processando imagens da tabela "imagens"...
   Encontradas 62 imagens para processar

 62/62 [============================] 100%

   ✅ Sucesso: 59
   ⏭️  Ignoradas: 3

👤 Processando fotos de perfil de pacientes...
   Encontradas 15 fotos de perfil para processar

 15/15 [============================] 100%

   ✅ Sucesso: 15

✅ Processo concluído!
```

## Como Funciona

1. **Busca imagens**: Consulta o banco de dados por imagens sem preview
2. **Valida existência**: Verifica se o arquivo original existe no storage
3. **Gera preview**: 
   - Carrega a imagem original
   - Aplica correção de orientação EXIF
   - Redimensiona para 200px (lado maior) mantendo proporção
   - Salva com qualidade 85%
4. **Atualiza banco**: Marca `has_preview = true` no registro
5. **Continua**: Processa a próxima imagem

## Estrutura de Diretórios

```
storage/app/images/
├── pacientes/
│   └── 123/
│       └── foto.jpg          # Imagem original
└── preview/
    └── pacientes/
        └── 123/
            └── foto.jpg      # Preview (200px)
```

## Tratamento de Erros

O comando é robusto e trata os seguintes casos:
- ✅ Imagens não encontradas (ignora e continua)
- ✅ Previews já existentes (ignora, a menos que use `--force`)
- ✅ Erros de processamento (registra erro e continua)
- ✅ Interrupção manual (Ctrl+C) - pode ser retomado depois

## Uso em Produção

### Passo 1: Teste com dry-run
```bash
php artisan images:generate-previews --dry-run
```

### Passo 2: Teste com limite pequeno
```bash
php artisan images:generate-previews --limit=10
```

### Passo 3: Verifique os previews gerados
Acesse algumas imagens no frontend e confirme que os previews estão funcionando.

### Passo 4: Execute para todas as imagens
```bash
php artisan images:generate-previews
```

### Passo 5 (Opcional): Se precisar reprocessar
```bash
php artisan images:generate-previews --force
```

## Performance

- **Velocidade**: ~1-2 imagens por segundo (depende do tamanho das originais)
- **Memória**: ~50-100MB (Intervention Image)
- **Disco**: Previews ocupam ~10-20% do tamanho das originais

Para 1000 imagens:
- Tempo estimado: 8-15 minutos
- Espaço em disco: ~50-200MB (depende das originais)

## Troubleshooting

### Erro: "Class 'Intervention\Image\Facades\Image' not found"
```bash
composer require intervention/image
```

### Erro: "Permission denied"
```bash
chmod -R 775 storage/app/images/
```

### Erro: "Memory limit exceeded"
Aumente o limite de memória no php.ini:
```ini
memory_limit = 512M
```

Ou execute com limite maior:
```bash
php -d memory_limit=512M artisan images:generate-previews
```

## Notas Importantes

1. ⚠️ **Backup**: Faça backup do diretório `storage/app/images/` antes de usar `--force`
2. 🔒 **Permissões**: Certifique-se que o diretório `storage/app/images/preview/` tem permissões de escrita
3. 📊 **Monitoramento**: Em produção, monitore o uso de disco durante a execução
4. 🔄 **Idempotência**: O comando pode ser executado múltiplas vezes sem problemas (ignora imagens já processadas)

## Código Fonte

O comando está localizado em:
```
app/Console/Commands/GenerateImagePreviews.php
```

