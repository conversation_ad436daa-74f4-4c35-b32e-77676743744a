<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assinaturas', function (Blueprint $table) {
            // Adicionar campos para ciclos mais robustos
            $table->enum('tipo_periodo', ['trial', 'pago', 'promocional'])->default('pago')->after('status');
            $table->integer('dias_utilizados')->default(0)->after('tipo_periodo');
            $table->decimal('valor_proporcional', 10, 2)->nullable()->after('valor_mensal');
            $table->boolean('fatura_gerada')->default(false)->after('valor_proporcional');
            
            // Adicionar índices para performance
            $table->index(['clinica_id', 'tipo_periodo']);
            $table->index(['status', 'tipo_periodo']);
            $table->index('fatura_gerada');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assinaturas', function (Blueprint $table) {
            // Remover índices
            $table->dropIndex(['clinica_id', 'tipo_periodo']);
            $table->dropIndex(['status', 'tipo_periodo']);
            $table->dropIndex(['fatura_gerada']);
            
            // Remover colunas
            $table->dropColumn([
                'tipo_periodo',
                'dias_utilizados', 
                'valor_proporcional',
                'fatura_gerada'
            ]);
        });
    }
};
