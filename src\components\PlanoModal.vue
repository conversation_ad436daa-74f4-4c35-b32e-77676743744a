<template>
  <div class="modal fade" :class="{ show: show }" :style="{ display: show ? 'block' : 'none' }" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            {{ isEditing ? 'Editar plano' : 'Novo plano' }}
          </h5>
          <button type="button" class="btn-close" @click="close"></button>
        </div>
        
        <div class="modal-body">
          <form @submit.prevent="save">
            <!-- Informações básicas -->
            <div class="row mb-4">
              <div class="col-md-8">
                <label class="form-label">Nome *</label>
                <input
                  type="text"
                  class="form-control"
                  v-model="formData.nome"
                  :class="{ 'is-invalid': errors.nome }"
                  placeholder="Ex: Básico, Premium, Enterprise"
                  required
                />
                <div v-if="errors.nome" class="invalid-feedback">{{ errors.nome }}</div>
              </div>
              
              <div class="col-md-4">
                <label class="form-label">Cor</label>
                <div class="color-rectangle" :style="{ backgroundColor: formData.cor }" @click="$refs.colorInput.click()">
                  <input
                    ref="colorInput"
                    type="color"
                    class="color-input-hidden"
                    v-model="formData.cor"
                    title="Escolher cor"
                  />
                </div>
              </div>
            </div>

            <!-- Módulos centralizados -->
            <div class="row mb-3">
              <div class="col-12">
                <div class="d-flex justify-content-around align-items-center flex-wrap gap-3">
                  <div class="module-item">
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input module-required"
                        type="checkbox"
                        v-model="formData.modulo_clinica"
                        id="modulo_clinica"
                        disabled
                        checked
                      />
                      <label class="form-check-label module-label-required" for="modulo_clinica">
                        <i class="fas fa-clinic-medical text-success me-2"></i>
                        <strong>Módulo clínica</strong>
                        <small class="text-success d-block fw-bold">✓ Incluído</small>
                      </label>
                    </div>
                  </div>

                  <div class="module-item">
                    <div class="form-check form-switch">
                      <input
                        class="form-check-input custom-switch"
                        type="checkbox"
                        v-model="formData.modulo_ortodontia"
                        id="modulo_ortodontia"
                      />
                      <label class="form-check-label" for="modulo_ortodontia">
                        <i class="fas fa-teeth text-primary me-2"></i>
                        <strong>Módulo ortodontia</strong>
                        <small class="text-muted d-block">Opcional</small>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Campos de limites em layout de 3-4 colunas centralizados -->
            <div class="row mb-3 justify-content-center">
              <div class="col-lg-3 col-md-6 mb-3">
                <label class="form-label text-center d-block">Quantidade de usuários</label>
                <input
                  type="number"
                  class="form-control text-center"
                  v-model.number="formData.quantidade_usuarios"
                  :class="{ 'is-invalid': errors.quantidade_usuarios }"
                  min="1"
                  placeholder="Ilimitado"
                />
                <div v-if="errors.quantidade_usuarios" class="invalid-feedback">{{ errors.quantidade_usuarios }}</div>
              </div>

              <div class="col-lg-3 col-md-6 mb-3">
                <label class="form-label text-center d-block">Agendas/cadeiras</label>
                <input
                  type="number"
                  class="form-control text-center"
                  v-model.number="formData.quantidade_agendas_cadeiras"
                  :class="{ 'is-invalid': errors.quantidade_agendas_cadeiras }"
                  min="1"
                  placeholder="Ilimitado"
                />
                <div v-if="errors.quantidade_agendas_cadeiras" class="invalid-feedback">{{ errors.quantidade_agendas_cadeiras }}</div>
              </div>

              <div class="col-lg-3 col-md-6 mb-3">
                <label class="form-label text-center d-block">Meses de fidelidade</label>
                <input
                  type="number"
                  class="form-control text-center"
                  v-model.number="formData.meses_fidelidade_minima"
                  :class="{ 'is-invalid': errors.meses_fidelidade_minima }"
                  min="0"
                  placeholder="0"
                />
                <div v-if="errors.meses_fidelidade_minima" class="invalid-feedback">{{ errors.meses_fidelidade_minima }}</div>
              </div>

              <div class="col-lg-3 col-md-6 mb-3" v-if="formData.modulo_ortodontia">
                <label class="form-label text-center d-block">
                  <i class="fas fa-graduation-cap text-primary me-1"></i>
                  Mentorias/mês
                </label>
                <input
                  type="number"
                  class="form-control text-center"
                  v-model.number="formData.quantidade_mentorias_mensais"
                  :class="{ 'is-invalid': errors.quantidade_mentorias_mensais }"
                  min="1"
                  placeholder="Ilimitado"
                />
                <div v-if="errors.quantidade_mentorias_mensais" class="invalid-feedback">{{ errors.quantidade_mentorias_mensais }}</div>
              </div>
            </div>

            <!-- Layout de 4 colunas: Valor, Meses Gratuitos, Auto-registro, Plano Ativo -->
            <div class="row mb-3 justify-content-center">
              <div class="col-lg-3 col-md-6 mb-3">
                <label class="form-label text-center d-block">Valor mensal *</label>
                <div class="input-group">
                  <span class="input-group-text">R$</span>
                  <input
                    type="text"
                    class="form-control money-input text-center"
                    v-model="valorMensalFormatted"
                    @input="updateValorMensal($event.target.value)"
                    @keypress="onlyNumbers"
                    :class="{ 'is-invalid': errors.valor_mensal }"
                    placeholder="0,00"
                    maxlength="15"
                    inputmode="numeric"
                  />
                </div>
                <div v-if="errors.valor_mensal" class="invalid-feedback">{{ errors.valor_mensal }}</div>
                <small class="form-text text-muted text-center d-block">Deixe em branco para plano gratuito</small>
              </div>

              <div class="col-lg-3 col-md-6 mb-3">
                <label class="form-label text-center d-block">Dias gratuitos</label>
                <input
                  type="number"
                  class="form-control text-center"
                  v-model.number="formData.dias_gratuitos"
                  :class="{ 'is-invalid': errors.dias_gratuitos }"
                  min="0"
                  placeholder="0"
                />
                <div v-if="errors.dias_gratuitos" class="invalid-feedback">{{ errors.dias_gratuitos }}</div>
                <small class="form-text text-muted text-center d-block">Período de cortesia inicial</small>
              </div>

              <div class="col-lg-3 col-md-6 mb-3 d-flex align-items-center justify-content-center">
                <div class="text-center">
                  <label class="form-label d-block">Permitir auto-registro</label>
                  <div class="form-check form-switch d-inline-block">
                    <input
                      class="form-check-input custom-switch"
                      type="checkbox"
                      v-model="formData.auto_registro"
                      id="auto_registro"
                    />
                    <label class="form-check-label" for="auto_registro">
                      <small class="text-muted d-block">Aparece no cadastro</small>
                    </label>
                  </div>
                </div>
              </div>

              <div class="col-lg-3 col-md-6 mb-3 d-flex align-items-center justify-content-center">
                <div class="text-center">
                  <label class="form-label d-block">Plano ativo</label>
                  <div class="form-check form-switch d-inline-block">
                    <input
                      class="form-check-input custom-switch"
                      type="checkbox"
                      v-model="formData.ativo"
                      id="ativo"
                    />
                    <label class="form-check-label" for="ativo">
                      <small class="text-muted d-block">Disponível para seleção</small>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- Observações no final -->
            <div class="row mb-3">
              <div class="col-12">
                <label class="form-label">Observações (uso interno)</label>
                <textarea
                  class="form-control"
                  v-model="formData.observacoes"
                  rows="2"
                  placeholder="Observações internas sobre o plano"
                ></textarea>
              </div>
            </div>
          </form>
        </div>
        
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="close">
            Cancelar
          </button>
          <button type="button" class="btn btn-primary" @click="save" :disabled="isSaving">
            <span v-if="isSaving" class="spinner-border spinner-border-sm me-2"></span>
            {{ isEditing ? 'Atualizar' : 'Criar' }} Plano
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Backdrop -->
  <div v-if="show" class="modal-backdrop fade show" @click="close"></div>
</template>

<script>
import planosService from '@/services/planosService';

export default {
  name: 'PlanoModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    plano: {
      type: Object,
      default: null
    },
    isEditing: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {},
      errors: {},
      isSaving: false,
      valorMensalFormatted: '' // String formatada para o input
    };
  },
  watch: {
    show(newVal) {
      if (newVal) {
        this.resetForm();
        // Prevent body scroll when modal is open
        document.body.style.overflow = 'hidden';
      } else {
        // Restore body scroll when modal is closed
        document.body.style.overflow = '';
      }
    },
    plano: {
      handler() {
        this.resetForm();
      },
      deep: true
    },
    'formData.modulo_ortodontia'(newVal) {
      // Limpar mentorias se módulo ortodontia for desabilitado
      if (!newVal) {
        this.formData.quantidade_mentorias_mensais = null;
      }
    },
    'formData.quantidade_agendas_cadeiras'(newVal) {
      // Sincronizar agendas e cadeiras
      this.formData.quantidade_agendas = newVal;
      this.formData.quantidade_cadeiras = newVal;
    }
  },
  methods: {
    resetForm() {
      if (this.plano) {
        this.formData = { ...this.plano };
        // Configurar campo combinado de agendas/cadeiras para edição
        if (this.formData.quantidade_agendas) {
          this.formData.quantidade_agendas_cadeiras = this.formData.quantidade_agendas;
        }
        // Inicializar valor formatado
        this.valorMensalFormatted = this.formatCurrencyInput(this.formData.valor_mensal);
      } else {
        this.formData = planosService.getDefaultPlanoData();
        this.valorMensalFormatted = '';
      }
      this.errors = {};
    },

    close() {
      this.$emit('close');
    },

    formatCurrencyInput(value) {
      if (!value || value === 0) return '';
      return this.formatMoneyMask(value.toString());
    },

    formatCurrency(value) {
      if (!value) return 'R$ 0,00';
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    },

    // Máscara de dinheiro - versão reimplementada sem parseInt
    formatMoneyMask(value) {
      if (!value && value !== 0) return '';

      // Se for número, converter para string
      const stringValue = value.toString();

      // Remove tudo que não é dígito
      const digits = stringValue.replace(/\D/g, '');
      if (!digits) return '0,00';

      // Garantir pelo menos 3 dígitos (para ter centavos)
      const paddedDigits = digits.padStart(3, '0');

      // Separar reais e centavos usando slice
      const centavos = paddedDigits.slice(-2); // Últimos 2 dígitos
      const reaisDigits = paddedDigits.slice(0, -2); // Todos menos os últimos 2

      // Formatar reais com separadores de milhares
      const reaisFormatted = this.formatThousands(reaisDigits);

      return `${reaisFormatted},${centavos}`;
    },

    // Função auxiliar para formatar milhares
    formatThousands(digits) {
      if (!digits || digits === '0' || digits === '') return '0';

      // Remove zeros à esquerda
      const cleanDigits = digits.replace(/^0+/, '') || '0';

      // Adiciona pontos a cada 3 dígitos da direita para esquerda
      return cleanDigits.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    },

    updateValorMensal(value) {
      // Remove tudo que não é dígito (bloqueia letras)
      const digits = value.replace(/\D/g, '');

      if (!digits || digits === '') {
        this.formData.valor_mensal = '';
        this.valorMensalFormatted = '';
        return;
      }

      // Atualizar a string formatada
      this.valorMensalFormatted = this.formatMoneyMask(digits);

      // Garantir pelo menos 3 dígitos (para ter centavos)
      const paddedDigits = digits.padStart(3, '0');

      // Separar reais e centavos usando slice
      const centavos = paddedDigits.slice(-2); // Últimos 2 dígitos
      const reaisDigits = paddedDigits.slice(0, -2); // Todos menos os últimos 2

      // Converter para número decimal
      const reaisNumber = reaisDigits === '' ? 0 : Number(reaisDigits);
      const centavosNumber = Number(centavos);


      // Calcular valor final como decimal
      const valorFinal = reaisNumber + (centavosNumber / 100);

      // Atualizar o valor no formData
      this.formData.valor_mensal = valorFinal;
    },

    // Bloquear letras no input de valor
    onlyNumbers(event) {
      const char = String.fromCharCode(event.which);
      if (!/[0-9]/.test(char)) {
        event.preventDefault();
      }
    },

    async save() {
      // Validar dados
      const validation = planosService.validatePlanoData(this.formData);
      
      if (!validation.isValid) {
        this.errors = validation.errors;
        return;
      }

      this.isSaving = true;
      this.errors = {};

      try {
        // Preparar dados para envio
        const dataToSave = { ...this.formData };

        // Sincronizar agendas e cadeiras com o campo combinado
        if (dataToSave.quantidade_agendas_cadeiras) {
          dataToSave.quantidade_agendas = dataToSave.quantidade_agendas_cadeiras;
          dataToSave.quantidade_cadeiras = dataToSave.quantidade_agendas_cadeiras;
        }

        // Converter valores vazios para null (mas preservar 0 para campos numéricos)
        Object.keys(dataToSave).forEach(key => {
          if (dataToSave[key] === '' || dataToSave[key] === undefined) {
            dataToSave[key] = null;
          }
          // Garantir que dias_gratuitos seja enviado mesmo quando for 0
          if (key === 'dias_gratuitos' && (dataToSave[key] === null || dataToSave[key] === '')) {
            dataToSave[key] = 0;
          }
        });

        // Garantir que campos booleanos sejam enviados como strings para Laravel
        dataToSave.ativo = dataToSave.ativo ? 'true' : 'false';
        dataToSave.auto_registro = dataToSave.auto_registro ? 'true' : 'false';
        dataToSave.modulo_clinica = dataToSave.modulo_clinica ? 'true' : 'false';
        dataToSave.modulo_ortodontia = dataToSave.modulo_ortodontia ? 'true' : 'false';

        // Converter trial_available se existir
        if (dataToSave.trial_available !== undefined && dataToSave.trial_available !== null) {
          dataToSave.trial_available = dataToSave.trial_available ? 'true' : 'false';
        }

        this.$emit('save', dataToSave);
      } catch (error) {
        console.error('Erro ao salvar:', error);
      } finally {
        this.isSaving = false;
      }
    }
  },
  beforeUnmount() {
    // Restore body scroll if component is destroyed while modal is open
    document.body.style.overflow = '';
  }
};
</script>

<style scoped>
.section-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.section-header h6 {
  margin: 0;
  color: #495057;
  font-weight: 600;
}

.color-picker-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Retângulo de cor personalizado */
.color-rectangle {
  width: 100%;
  height: 38px;
  border-radius: 6px;
  border: 1px solid #ced4da;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.color-rectangle:hover {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.color-input-hidden {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

/* Estilos para módulos */
.module-item {
  text-align: center;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  min-width: 200px;
}

.module-item:hover {
  background: rgba(0, 123, 255, 0.05);
}

.module-item .form-check-label small {
  margin-bottom: 0;
  padding-bottom: 0;
}

.module-required {
  background-color: #28a745 !important;
  border-color: #28a745 !important;
  opacity: 1 !important;
}

.module-label-required {
  color: #495057 !important;
  opacity: 1 !important;
}

.module-label-required strong {
  color: #28a745;
}

/* Corrigir border-radius do input de valor */
.money-input {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

/* Toggles customizados */
.custom-switch {
  background-color: #dee2e6 !important;
  border-color: #dee2e6 !important;
  width: 2.5em;
  height: 1.25em;
}

.custom-switch:checked {
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.custom-switch:focus {
  box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25) !important;
}

/* Estilo para campo de moeda */
.money-input {
  text-align: right;
  font-weight: 600;
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
  color: #6c757d;
  font-weight: 500;
}

.form-check-label {
  cursor: pointer;
}

.form-check-label small {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.modal-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  border-radius: 12px;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
  border-bottom: 1px solid #e9ecef;
  padding: 1.5rem;
}

.modal-title {
  font-weight: 600;
  color: #495057;
}

.modal-body {
  padding: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  padding: 1rem 1.5rem;
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  opacity: 0.5;
  cursor: pointer;
}

.btn-close:hover {
  opacity: 0.75;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.form-check-input:checked {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  border-radius: 6px;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-secondary {
  border-radius: 6px;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}
</style>



/* Animações */
.modal.show {
  animation: modalFadeIn 0.3s ease-out;
}

.modal-backdrop.show {
  animation: backdropFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.5;
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .modal-body {
    max-height: 60vh;
  }
}
