<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Clinica extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'clinicas';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'imagem_url',
        'nome',
        'endereco',
        'observacoes',
        'slug',
        'plano_id',
        'dia_vencimento',
        'endereco_cep',
        'endereco_estado',
        'endereco_cidade',
        'endereco_logradouro',
        'endereco_numero',
        'endereco_bairro',
        'endereco_complemento',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
        ];
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*']);
    }

    public function pacientes(): HasMany
    {
        return $this->hasMany(Paciente::class);
    }

    public function dentistas(): HasMany
    {
        return $this->hasMany(Dentista::class);
    }

    public function financeiro_pagar(): HasMany
    {
        return $this->hasMany(FinanceiroPagar::class);
    }

    public function financeiro_receber(): HasMany
    {
        return $this->hasMany(FinanceiroReceber::class);
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function consultorios(): HasMany
    {
        return $this->hasMany(Consultorio::class);
    }

    public function plano(): BelongsTo
    {
        return $this->belongsTo(Plano::class);
    }

    public function assinaturas(): HasMany
    {
        return $this->hasMany(Assinatura::class);
    }

    public function assinaturaAtiva(): HasOne
    {
        return $this->hasOne(Assinatura::class)
                    ->where('status', 'ativo')
                    ->where(function ($query) {
                        $query->whereNull('data_fim')
                              ->orWhere('data_fim', '>=', now()->toDateString());
                    })
                    ->orderBy('data_inicio', 'desc');
    }

    public function historicoAssinaturas(): HasMany
    {
        return $this->hasMany(Assinatura::class)
                    ->whereIn('status', ['encerrado', 'cancelado'])
                    ->whereNotNull('data_fim')
                    ->orderBy('data_inicio', 'desc');
    }

    public function faturasClinica(): HasMany
    {
        return $this->hasMany(FaturaClinica::class);
    }
}
