<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;
use App\Models\Paciente;
use App\Models\Imagem;

class ImageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            $imagens = Imagem::all();

            return response()->json($imagens);
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        return $this->uploadImage($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $imagem = Imagem::findOrFail($id);

            return response()->json($imagem);
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $imagem = Imagem::findOrFail($id);

            if ($request->has('descricao')) {
                $imagem->descricao = $request->descricao;
            }

            if ($request->has('data')) {
                $imagem->data = $request->data;
            }

            if ($request->has('is_diagnostico')) {
                $imagem->is_diagnostico = (bool)$request->is_diagnostico;
            }

            if ($request->has('tag_diagnostico')) {
                $imagem->tag_diagnostico = $request->tag_diagnostico;
            }

            $imagem->save();

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $imagem = Imagem::findOrFail($id);

            // Remover o arquivo físico
            $decodedUrl = urldecode($imagem->url);
            $decodedUrl = urldecode($decodedUrl);

            $path = 'images/' . $imagem->dir . '/' . $imagem->filename;
            if (Storage::disk('local')->exists($path)) {
                Storage::disk('local')->delete($path);
            }

            // Remover o registro do banco de dados
            $imagem->delete();

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }

    public function uploadImage(Request $request)
    {
        try {
            if (!$request->hasFile('imagem'))
                throw new \Exception('Imagem inválida');

            $image = $request->file('imagem');

            $dir = $request->input('dir') ? $request->input('dir') : 'misc';

            $img = Image::make($image->getRealPath());

            // Aplicar orientação automática baseada nos dados EXIF
            $img->orientate();

            // $img->resize(120, 120, function ($constraint) {
            //     $constraint->aspectRatio();
            // });

            $img->stream(); // <-- Key point

            $uniqueFilename = $this->getUniqueFilename();
            $fileExtension = $image->getClientOriginalExtension();

            $filename = $uniqueFilename . '.' . $fileExtension;

            $image_url = $dir . '/' . $filename;

            Storage::disk('local')->put('images/' . $image_url, $img, 'public');

            // Processar e salvar thumbnail se fornecido
            $has_preview = false;
            if ($request->hasFile('thumbnail')) {
                try {
                    $thumbnail = $request->file('thumbnail');
                    $thumbnailImg = Image::make($thumbnail->getRealPath());

                    // Aplicar orientação automática
                    $thumbnailImg->orientate();
                    $thumbnailImg->stream();

                    // Salvar thumbnail no diretório preview
                    $preview_url = 'preview/' . $image_url;
                    Storage::disk('local')->put('images/' . $preview_url, $thumbnailImg, 'public');
                    $has_preview = true;
                } catch (\Exception $e) {
                    // Se falhar ao salvar thumbnail, continua sem ele
                    \Log::warning('Erro ao salvar thumbnail: ' . $e->getMessage());
                }
            }

            $paciente_id = $request->input('paciente_id');
            $dentista_id = $request->input('dentista_id');
            $data = $request->input('data');
            $descricao = $request->input('descricao') ? $request->input('descricao') : '';
            $is_diagnostico = filter_var($request->input('is_diagnostico'), FILTER_VALIDATE_BOOLEAN);
            $tag_diagnostico = $request->input('tag_diagnostico') ? $request->input('tag_diagnostico') : null;

            // Double encoded because of %2F ('/' - slash) character bug
            $encoded_url = urlencode(urlencode($image_url));

            // Se for upload de imagem de paciente, salvar na tabela imagens
            if ($paciente_id && $dir !== 'profile_pic_dentista') {
                $imagem = new Imagem();
                $imagem->paciente_id = $paciente_id;
                $imagem->dir = $dir;
                $imagem->filename = $filename;
                $imagem->url = $encoded_url;
                $imagem->data = $data;
                $imagem->descricao = $descricao;
                $imagem->is_diagnostico = $is_diagnostico;
                $imagem->tag_diagnostico = $tag_diagnostico;
                $imagem->has_preview = $has_preview;
                $imagem->save();
            }

            if ($dir == 'profile_pic') {
                $paciente = Paciente::find($paciente_id);

                if ($paciente) {
                    $paciente->profile_picture_url = $encoded_url;
                    $paciente->profile_picture_has_preview = $has_preview;
                    $paciente->save();
                }
            }

            if ($dir == 'profile_pic_dentista' && $dentista_id) {
                $dentista = \App\Models\Dentista::find($dentista_id);

                if ($dentista) {
                    $dentista->profile_picture_url = $encoded_url;
                    $dentista->save();
                }
            }

            $response = responseSuccess();
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage()
            ]);
        }

        return $response;
    }

    public function serveImage(Request $request, $url)
    {
        // Decodifica a URL
        $decodedUrl = urldecode($url);
        $decodedUrl = urldecode($decodedUrl);

        // Extrai o diretório e o nome do arquivo
        $parts = explode('/', $decodedUrl);
        $filename = end($parts);
        $directory = implode('/', array_slice($parts, 0, -1));

        // Verifica se o diretório e o nome do arquivo foram fornecidos
        if (!$directory || !$filename) {
            return response()->json(['error' => 'Diretório e nome do arquivo são obrigatórios'], 400);
        }

        // Verifica se o arquivo existe no diretório
        $path = storage_path('app/images/' . $directory . '/' . $filename);
        if (!file_exists($path)) {
            return response()->json(['error' => 'Arquivo não encontrado'], 404);
        }

        // Serve a imagem
        $mimeType = mime_content_type($path);
        $headers = [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'inline; filename="' . $filename . '"',
        ];

        return response()->file($path, $headers);
    }

    private function getUniqueFilename()
    {
        return uniqid(rand(), true) . uniqid(rand(), true);
    }

    public function getImagensByPaciente($paciente_id)
    {
        try {
            $imagens = Imagem::where('paciente_id', $paciente_id)->get();

            return response()->json($imagens);
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove images by patient ID and date.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function deleteByDateAndPatient(Request $request)
    {
        try {
            // Validar os parâmetros recebidos
            $request->validate([
                'paciente_id' => 'required|integer',
                'data' => 'required|date_format:Y-m-d',
            ]);

            $paciente_id = $request->input('paciente_id');
            $data = $request->input('data');

            // Buscar imagens que correspondem ao paciente_id e data
            $imagens = Imagem::where('paciente_id', $paciente_id)
                            ->where('data', $data)
                            ->get();

            if ($imagens->isEmpty()) {
                return responseError([
                    'message' => 'Nenhuma imagem encontrada para este paciente nesta data.'
                ]);
            }

            // Excluir cada imagem encontrada
            foreach ($imagens as $imagem) {
                // Remover o arquivo físico
                $path = 'images/' . $imagem->dir . '/' . $imagem->filename;
                if (Storage::disk('local')->exists($path)) {
                    Storage::disk('local')->delete($path);
                }

                // Remover o registro do banco de dados
                $imagem->delete();
            }

            return responseSuccess();
        } catch (\Exception $e) {
            return responseError([
                'message' => $e->getMessage()
            ]);
        }
    }
}
