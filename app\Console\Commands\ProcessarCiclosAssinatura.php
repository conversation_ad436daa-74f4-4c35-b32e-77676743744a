<?php

namespace App\Console\Commands;

use App\Models\Assinatura;
use App\Models\FaturaClinica;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProcessarCiclosAssinatura extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assinaturas:processar-ciclos {--force : Forçar processamento mesmo que não seja necessário}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Processar fechamento de ciclos de assinatura e iniciar novos períodos';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Iniciando processamento de ciclos de assinatura...');

        $hoje = Carbon::now();
        $ontem = $hoje->copy()->subDay();

        try {
            DB::transaction(function () use ($hoje, $ontem) {
                // Buscar assinaturas que venceram ontem
                $assinaturasVencidas = Assinatura::with(['clinica', 'plano'])
                    ->where('status', 'ativo')
                    ->where('data_fim', $ontem->toDateString())
                    ->get();

                $this->info("📋 Encontradas {$assinaturasVencidas->count()} assinaturas para processar");

                $processadas = 0;
                $erros = 0;

                foreach ($assinaturasVencidas as $assinatura) {
                    try {
                        $this->processarAssinatura($assinatura, $hoje);
                        $processadas++;
                        $this->line("✅ Processada assinatura ID {$assinatura->id} - Clínica: {$assinatura->clinica->nome}");
                    } catch (\Exception $e) {
                        $erros++;
                        $this->error("❌ Erro ao processar assinatura ID {$assinatura->id}: " . $e->getMessage());
                        Log::error("Erro ao processar ciclo de assinatura {$assinatura->id}: " . $e->getMessage());
                    }
                }

                $this->info("\n📊 Resumo do processamento:");
                $this->table(
                    ['Métrica', 'Quantidade'],
                    [
                        ['Assinaturas encontradas', $assinaturasVencidas->count()],
                        ['Processadas com sucesso', $processadas],
                        ['Erros', $erros],
                    ]
                );
            });

            $this->info('✅ Processamento de ciclos concluído com sucesso!');
            return 0;

        } catch (\Exception $e) {
            $this->error('❌ Erro durante o processamento: ' . $e->getMessage());
            Log::error('Erro no processamento de ciclos de assinatura: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Processar uma assinatura específica
     */
    private function processarAssinatura(Assinatura $assinatura, Carbon $hoje): void
    {
        // Encerrar período atual
        $assinatura->update([
            'status' => 'encerrado',
            'motivo_alteracao' => 'Fim do período de assinatura'
        ]);

        // Criar novo período com o mesmo plano
        $diaVencimento = $assinatura->clinica->dia_vencimento ?? $assinatura->dia_cobranca;
        $dataInicio = $hoje;
        $dataFim = $hoje->copy()->addMonth()->day($diaVencimento)->subDay();

        $novoPeriodo = Assinatura::create([
            'clinica_id' => $assinatura->clinica_id,
            'plano_id' => $assinatura->plano_id,
            'data_inicio' => $dataInicio->toDateString(),
            'data_fim' => $dataFim->toDateString(),
            'valor_mensal' => $assinatura->valor_mensal,
            'dia_cobranca' => $diaVencimento,
            'meses_fidelidade' => $assinatura->meses_fidelidade,
            'status' => 'ativo',
            'periodo_anterior_id' => $assinatura->id,
            'motivo_alteracao' => 'Renovação automática de período',
        ]);

        // Gerar fatura para o novo período
        $this->gerarFaturaParaPeriodo($novoPeriodo, $hoje);

        Log::info("Ciclo processado para clínica {$assinatura->clinica->nome} - Novo período: {$novoPeriodo->id}");
    }

    /**
     * Gerar fatura para um período
     */
    private function gerarFaturaParaPeriodo(Assinatura $assinatura, Carbon $dataReferencia): void
    {
        // Verificar se já existe fatura para este período
        $faturaExistente = FaturaClinica::where('assinatura_id', $assinatura->id)->first();
        if ($faturaExistente) {
            return;
        }

        // Calcular data de vencimento (30 dias a partir da data de início)
        $dataVencimento = Carbon::parse($assinatura->data_inicio)->addDays(30);

        // Gerar número da fatura
        $numeroFatura = $this->gerarNumeroFatura($dataReferencia);

        // Criar a fatura
        FaturaClinica::create([
            'assinatura_id' => $assinatura->id,
            'clinica_id' => $assinatura->clinica_id,
            'numero_fatura' => $numeroFatura,
            'descricao' => "Assinatura {$assinatura->plano->nome} - " . Carbon::parse($assinatura->data_inicio)->format('m/Y'),
            'valor_nominal' => $assinatura->valor_mensal,
            'valor_desconto' => 0,
            'valor_acrescimo' => 0,
            'valor_final' => $assinatura->valor_mensal,
            'data_vencimento' => $dataVencimento->toDateString(),
            'status' => 'pendente',
        ]);
    }

    /**
     * Gerar número único da fatura
     */
    private function gerarNumeroFatura(Carbon $data): string
    {
        $prefixo = 'FC-' . $data->format('Y-m');
        
        // Buscar o último número do mês
        $ultimaFatura = FaturaClinica::where('numero_fatura', 'LIKE', "{$prefixo}-%")
            ->orderBy('numero_fatura', 'desc')
            ->first();

        if ($ultimaFatura) {
            // Extrair o número sequencial
            $partes = explode('-', $ultimaFatura->numero_fatura);
            $ultimoNumero = (int) end($partes);
            $proximoNumero = $ultimoNumero + 1;
        } else {
            $proximoNumero = 1;
        }

        return $prefixo . '-' . str_pad($proximoNumero, 4, '0', STR_PAD_LEFT);
    }
}
