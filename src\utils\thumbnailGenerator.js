/**
 * Utilitário para geração de thumbnails de imagens
 * Gera thumbnails mantendo aspect ratio com 200px no lado maior
 */

/**
 * Gera um thumbnail de uma imagem
 * @param {File} file - Arquivo de imagem original
 * @param {number} maxSize - <PERSON><PERSON><PERSON> máximo do lado maior (padrão: 200)
 * @param {number} quality - Qualidade da compressão JPEG (0-1, padrão: 0.85)
 * @returns {Promise<File>} - Promise que resolve com o arquivo do thumbnail
 */
export async function generateThumbnail(file, maxSize = 200, quality = 0.85) {
  return new Promise((resolve, reject) => {
    // Validar se é uma imagem
    if (!file.type.startsWith('image/')) {
      reject(new Error('O arquivo não é uma imagem válida'));
      return;
    }

    const reader = new FileReader();
    
    reader.onerror = () => {
      reject(new Error('Erro ao ler o arquivo'));
    };

    reader.onload = (e) => {
      const img = new Image();
      
      img.onerror = () => {
        reject(new Error('Erro ao carregar a imagem'));
      };

      img.onload = () => {
        try {
          // Calcular novas dimensões mantendo aspect ratio
          let width = img.width;
          let height = img.height;
          
          if (width > height) {
            if (width > maxSize) {
              height = Math.round((height * maxSize) / width);
              width = maxSize;
            }
          } else {
            if (height > maxSize) {
              width = Math.round((width * maxSize) / height);
              height = maxSize;
            }
          }

          // Criar canvas para redimensionar
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;

          const ctx = canvas.getContext('2d');
          
          // Desenhar imagem redimensionada
          ctx.drawImage(img, 0, 0, width, height);

          // Converter canvas para blob
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Erro ao gerar thumbnail'));
                return;
              }

              // Criar novo arquivo com o blob
              const thumbnailFile = new File(
                [blob],
                file.name,
                {
                  type: 'image/jpeg',
                  lastModified: Date.now()
                }
              );

              resolve(thumbnailFile);
            },
            'image/jpeg',
            quality
          );
        } catch (error) {
          reject(error);
        }
      };

      img.src = e.target.result;
    };

    reader.readAsDataURL(file);
  });
}

/**
 * Gera thumbnails para múltiplas imagens
 * @param {File[]} files - Array de arquivos de imagem
 * @param {number} maxSize - Tamanho máximo do lado maior (padrão: 200)
 * @param {number} quality - Qualidade da compressão JPEG (0-1, padrão: 0.85)
 * @returns {Promise<File[]>} - Promise que resolve com array de thumbnails
 */
export async function generateThumbnails(files, maxSize = 200, quality = 0.85) {
  const promises = files.map(file => generateThumbnail(file, maxSize, quality));
  return Promise.all(promises);
}

/**
 * Gera thumbnail e retorna tanto o original quanto o thumbnail
 * @param {File} file - Arquivo de imagem original
 * @param {number} maxSize - Tamanho máximo do lado maior (padrão: 200)
 * @param {number} quality - Qualidade da compressão JPEG (0-1, padrão: 0.85)
 * @returns {Promise<{original: File, thumbnail: File}>} - Promise com original e thumbnail
 */
export async function generateThumbnailPair(file, maxSize = 200, quality = 0.85) {
  const thumbnail = await generateThumbnail(file, maxSize, quality);
  return {
    original: file,
    thumbnail: thumbnail
  };
}

/**
 * Gera pares de original/thumbnail para múltiplas imagens
 * @param {File[]} files - Array de arquivos de imagem
 * @param {number} maxSize - Tamanho máximo do lado maior (padrão: 200)
 * @param {number} quality - Qualidade da compressão JPEG (0-1, padrão: 0.85)
 * @returns {Promise<Array<{original: File, thumbnail: File}>>} - Promise com array de pares
 */
export async function generateThumbnailPairs(files, maxSize = 200, quality = 0.85) {
  const promises = files.map(file => generateThumbnailPair(file, maxSize, quality));
  return Promise.all(promises);
}

/**
 * Verifica se uma imagem precisa de thumbnail (se é maior que o tamanho máximo)
 * @param {File} file - Arquivo de imagem
 * @param {number} maxSize - Tamanho máximo do lado maior (padrão: 200)
 * @returns {Promise<boolean>} - Promise que resolve com true se precisa de thumbnail
 */
export async function needsThumbnail(file, maxSize = 200) {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith('image/')) {
      resolve(false);
      return;
    }

    const reader = new FileReader();
    
    reader.onerror = () => reject(new Error('Erro ao ler o arquivo'));

    reader.onload = (e) => {
      const img = new Image();
      
      img.onerror = () => reject(new Error('Erro ao carregar a imagem'));

      img.onload = () => {
        const needs = img.width > maxSize || img.height > maxSize;
        resolve(needs);
      };

      img.src = e.target.result;
    };

    reader.readAsDataURL(file);
  });
}

