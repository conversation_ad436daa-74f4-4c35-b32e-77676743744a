<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
class Paciente extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'pacientes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id_ficha',
        'nome',
        'data_nascimento',
        'cpf',
        'etnia',
        'como_conheceu',
        'observacoes',
        'endereco_cep',
        'endereco_logradouro',
        'endereco_numero',
        'endereco_complemento',
        'endereco_cidade',
        'endereco_bairro',
        'endereco_estado',
        'diagnostico',
        'prognostico',
        'tratamento',
        'data_inicio_tratamento',
        'data_final_prevista',
        'status_tratamento',
        'primeira_consulta',
        'ultima_consulta',
        'proxima_consulta',
        'rg',
        'nome_pai',
        'nome_mae',
        'responsavel_nome',
        'responsavel_cpf',
        'responsavel_rg',
        'consultas_realizadas',
        'formulario_respondido',
        'public_token',
        'clinica_id',
        'dentista_id',
        'profile_picture_url',
        'profile_picture_has_preview',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'data_nascimento' => 'date:Y-m-d',
            'data_inicio_tratamento' => 'date:Y-m-d',
            'data_final_prevista' => 'date:Y-m-d',
            'primeira_consulta' => 'datetime',
            'ultima_consulta' => 'datetime',
            'proxima_consulta' => 'datetime',
            'formulario_respondido' => 'boolean',
            'profile_picture_has_preview' => 'boolean',
        ];
    }

    /**
     * Appends attributes to the model's array form.
     *
     * @var array
     */
    protected $appends = ['profile_picture_preview_url', 'profile_picture_original_url'];

    protected static function booted() {
        static::addGlobalScope(new \App\Scopes\ClinicaScope);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*']);
    }

    public function consultas(): HasMany
    {
        return $this->hasMany(Consulta::class);
    }

    public function imagens(): HasMany
    {
        return $this->hasMany(Imagem::class);
    }

    public function fases_tratamento(): HasMany
    {
        return $this->hasMany(FaseTratamento::class);
    }

    public function metas_terapeuticas(): HasMany
    {
        return $this->hasMany(MetaTerapeutica::class);
    }

    public function mentoria(): HasOne
    {
        return $this->hasOne(Mentoria::class);
    }

    public function clinica(): HasOne
    {
        return $this->hasOne(Clinica::class, 'id', 'clinica_id');
    }

    public function dentista(): HasOne
    {
        return $this->hasOne(Dentista::class, 'id', 'dentista_id');
    }

    public function formulario_boas_vindas(): HasOne
    {
        return $this->hasOne(FormularioBoasVindas::class, 'id_paciente', 'id');
    }

    public function detalhes_paciente(): HasMany
    {
        return $this->hasMany(DetalhePaciente::class);
    }

    public function contatos(): HasMany
    {
        return $this->hasMany(ContatoPaciente::class);
    }

    public function tratamentos_sugeridos(): BelongsToMany
    {
        return $this->belongsToMany(TratamentoSugerido::class);
    }

    public function fatores_clinicos(): BelongsToMany
    {
        return $this->belongsToMany(FatorClinico::class);
    }

    public function fatores_diagnostico(): BelongsToMany
    {
        return $this->belongsToMany(FatorDiagnostico::class);
    }

    public function necessidades_encaminhamentos(): HasMany
    {
        return $this->hasMany(NecessidadeEncaminhamento::class);
    }

    public function aparatologia(): HasOne
    {
        return $this->hasOne(Aparatologia::class);
    }

    public function historicos(): HasMany
    {
        return $this->hasMany(HistoricoPaciente::class);
    }

    public function modelos3d(): HasMany
    {
        return $this->hasMany(Modelo3D::class);
    }

    /**
     * Get the preview URL for the profile picture
     *
     * @return string|null
     */
    public function getProfilePicturePreviewUrlAttribute()
    {
        if (!$this->profile_picture_url) {
            return null;
        }

        if (!$this->profile_picture_has_preview) {
            return imageUrl($this->profile_picture_url);
        }

        // Decodificar a URL relativa armazenada no banco
        $decoded_url = urldecode(urldecode($this->profile_picture_url));

        // Adicionar o prefixo 'preview/'
        $preview_path = 'preview/' . $decoded_url;

        // Re-encodar e aplicar imageUrl()
        $encoded = urlencode(urlencode($preview_path));
        return imageUrl($encoded);
    }

    /**
     * Get the original URL for the profile picture
     *
     * @return string|null
     */
    public function getProfilePictureOriginalUrlAttribute()
    {
        return imageUrl($this->profile_picture_url);
    }
}
