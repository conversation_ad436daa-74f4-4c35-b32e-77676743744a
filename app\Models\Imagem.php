<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notifiable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class Imagem extends Model
{
    use HasFactory, Notifiable, LogsActivity;

    protected $table = 'imagens';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'paciente_id',
        'dir',
        'filename',
        'url',
        'data',
        'descricao',
        'is_diagnostico',
        'tag_diagnostico',
        'has_preview'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'has_preview' => 'boolean',
        ];
    }

    /**
     * Appends attributes to the model's array form.
     *
     * @var array
     */
    protected $appends = ['url_preview', 'url_original'];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
        ->logOnly(['*']);
    }

    // Add a relationship to the FormularioBoasVindas model
    public function paciente()
    {
        return $this->belongsTo(Paciente::class, 'paciente_id');
    }

    /**
     * Get the preview URL for the image
     *
     * @return string
     */
    public function getUrlPreviewAttribute()
    {
        if (!$this->has_preview) {
            return imageUrl($this->url);
        }

        // Decodificar a URL relativa armazenada no banco
        $decoded_url = urldecode(urldecode($this->url));

        // Adicionar o prefixo 'preview/'
        $preview_path = 'preview/' . $decoded_url;

        // Re-encodar e aplicar imageUrl()
        $encoded = urlencode(urlencode($preview_path));
        return imageUrl($encoded);
    }

    /**
     * Get the original URL for the image
     *
     * @return string
     */
    public function getUrlOriginalAttribute()
    {
        return imageUrl($this->url);
    }
}