<template>
  <div>
    <!-- Modal de revisão e salvamento -->
    <div 
      v-if="showReviewModal" 
      class="modal fade show d-block" 
      tabindex="-1" 
      style="background: rgba(0,0,0,0.5);"
    >
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-eye me-2"></i>
              Revisar alterações - {{ currentReviewDraft?.patientName }}
            </h5>
            <button 
              type="button" 
              class="btn-close" 
              @click="closeReviewModal"
            ></button>
          </div>
          
          <div class="modal-body">
            <div v-if="reviewChanges" class="changes-preview">
              <p class="mb-3">
                <i class="fas fa-info-circle text-info me-2"></i>
                Encontramos <strong>{{ currentReviewDraft?.changesCount }}</strong> 
                {{ currentReviewDraft?.changesCount === 1 ? 'alteração' : 'alterações' }} não {{ currentReviewDraft?.changesCount === 1 ? 'salva' : 'salvas' }}.
              </p>
              
              <div v-html="reviewChanges" class="changes-table-container"></div>
              
              <p class="text-muted mt-3 mb-0">
                <i class="fas fa-clock me-1"></i>
                Última modificação: {{ formatDate(currentReviewDraft?.lastModified) }}
              </p>
            </div>
            
            <div v-else class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Carregando...</span>
              </div>
              <p class="mt-2 mb-0">Carregando alterações...</p>
            </div>
          </div>
          
          <div class="modal-footer">
            <button 
              type="button" 
              class="btn btn-secondary" 
              @click="closeReviewModal"
            >
              <i class="fas fa-times me-1"></i>
              Cancelar
            </button>
            <button 
              type="button" 
              class="btn btn-success" 
              @click="saveReviewedChanges"
              :disabled="!reviewChanges"
            >
              <i class="fas fa-save me-1"></i>
              Salvar alterações
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- FABs globais -->
    <GlobalDraftsFab
      :drafts="draftsArray"
      @toggle-tooltip="handleToggleTooltip"
      @close-tooltip="handleCloseTooltip"
      @review-and-save="handleReviewAndSave"
      @open-patient="handleOpenPatient"
    />
  </div>
</template>

<script>
import { computed, ref, onMounted } from 'vue';
import GlobalDraftsFab from './GlobalDraftsFab.vue';
import { getGlobalDraftsInstance } from '@/composables/useGlobalDrafts';
import cSwal from '@/utils/cSwal.js';
import { showSuccessToast, showErrorToast } from '@/utils/toastHelper.js';
import { updatePaciente } from '@/services/pacientesService';

export default {
  name: 'GlobalDraftsManager',
  components: {
    GlobalDraftsFab
  },
  setup() {
    const globalDrafts = getGlobalDraftsInstance();
    const showReviewModal = ref(false);
    const currentReviewDraft = ref(null);
    const reviewChanges = ref(null);

    // Computed para os drafts
    const draftsArray = computed(() => globalDrafts.draftsArray.value);

    // Manipuladores de eventos
    const handleToggleTooltip = (patientId) => {
      globalDrafts.toggleTooltip(patientId);
    };

    const handleCloseTooltip = (patientId) => {
      globalDrafts.closeTooltip(patientId);
    };

    const handleOpenPatient = (draft) => {
      globalDrafts.openPatient(draft);
    };

    const handleReviewAndSave = async (draft) => {
      try {
        // Carregar dados do cache
        const cacheData = localStorage.getItem(draft.cacheKey);
        if (!cacheData) {
          showErrorToast('Cache não encontrado. Os dados podem ter expirado.');
          return;
        }

        const cached = JSON.parse(cacheData);

        // Mostrar modal elegante igual ao do paciente
        showPendingChangesModal(cached, draft);

        globalDrafts.closeAllTooltips();

      } catch (error) {
        console.error('Erro ao carregar dados para revisão:', error);
        showErrorToast('Erro ao carregar dados. Cache pode ter expirado.');
      }
    };

    // Modal elegante igual ao do Paciente.vue
    const showPendingChangesModal = (cached, draft) => {
      const changesHtml = generateChangesPreview(cached, true); // Com botões de descartar
      const count = cached.changedFields.length;

      cSwal.fire({
        title: `Alterações pendentes<br><small class="text-muted" style="font-size: 0.8em; font-weight: normal;">${draft.patientName}</small>`,
        html: `
          <div class="pending-changes-modal">
            <div class="alert alert-warning mb-3" style="background-color: #fff3cd; border-color: #ffeaa7; color: #f8f9fa;">
              <i class="fas fa-exclamation-triangle me-2"></i>
              Este paciente possui <strong>${count} ${count === 1 ? 'alteração' : 'alterações'}</strong> não ${count === 1 ? 'salva' : 'salvas'}.
            </div>

            <div class="changes-preview">
              <div class="compact-changes-table">
                ${changesHtml}
              </div>
            </div>

            <div class="text-muted mt-3">
              <small>
                <i class="fas fa-clock me-1"></i>
                Última modificação: ${new Date(cached.timestamp).toLocaleString('pt-BR')}
              </small>
            </div>

            <!-- Botões customizados em 3 níveis -->
            <div class="custom-buttons-container mt-4">
              <!-- Nível 1: Ver paciente -->
              <div class="button-level mb-3">
                <button id="btn-view-patient" class="btn btn-outline-primary w-100">
                  <i class="fas fa-user me-2"></i>
                  Ver paciente
                </button>
              </div>

              <!-- Nível 2: Descartar e Salvar -->
              <div class="button-level mb-4">
                <div class="level-2-actions">
                  <button id="btn-discard-all" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i>
                    Descartar tudo
                  </button>
                  <button id="btn-save-changes" class="btn btn-success">
                    <i class="fas fa-save me-1"></i>
                    Salvar alterações
                  </button>
                </div>
              </div>

              <!-- Nível 3: Fechar elegante -->
              <div class="button-level">
                <button id="btn-close-modal" class="btn" title="Fechar">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>

          <style>
            .compact-changes-table {
              max-height: 300px;
              overflow-y: auto;
              border: 1px solid #dee2e6;
              border-radius: 6px;
            }

            .compact-changes-table .changes-table {
              margin: 0;
              font-size: 0.85rem;
            }

            .compact-changes-table .changes-table th {
              background: #f8f9fa;
              padding: 8px 12px;
              font-weight: 600;
              font-size: 0.8rem;
              border-bottom: 1px solid #dee2e6;
            }

            .compact-changes-table .changes-table td {
              padding: 6px 12px;
              border-bottom: 1px solid #f1f3f4;
              vertical-align: middle;
            }

            .compact-changes-table .table-secondary td {
              background: #e9ecef;
              font-weight: 600;
              font-size: 0.8rem;
              padding: 8px 12px;
            }

            .compact-changes-table .old-value,
            .compact-changes-table .new-value {
              padding: 2px 6px;
              border-radius: 3px;
              font-size: 0.8rem;
              max-width: 150px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .compact-changes-table .old-value {
              background: #fff3cd;
              color: #856404;
              border: 1px solid #ffeaa7;
            }

            .compact-changes-table .new-value {
              background: #d1edff;
              color: #0c5460;
              border: 1px solid #bee5eb;
              font-weight: 600;
            }

            .btn-discard-change {
              background: #dc3545;
              color: white;
              border: none;
              border-radius: 4px;
              padding: 4px 6px;
              font-size: 10px;
              cursor: pointer;
              transition: all 0.2s ease;
            }

            .btn-discard-change:hover {
              background: #c82333;
              transform: scale(1.1);
            }

            .action-cell {
              text-align: center;
              vertical-align: middle;
            }

            .custom-buttons-container {
              margin-top: 20px;
            }

            .button-level {
              display: flex;
              justify-content: center;
              align-items: center;
              gap: 12px;
            }

            .button-level .btn {
              font-weight: 600;
              border-radius: 8px;
              transition: all 0.2s ease;
            }

            .button-level .btn:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }

            /* Botão Ver paciente - altura reduzida */
            #btn-view-patient {
              border: 2px solid #007bff;
              color: #007bff;
              padding: 8px 16px;
              font-size: 1rem;
            }

            #btn-view-patient:hover {
              background: #007bff;
              color: white;
            }

            /* Nível 2 - Layout flex para ocupar 100% */
            .level-2-actions {
              display: flex;
              gap: 12px;
              width: 100%;
            }

            .level-2-actions .btn {
              flex: 1;
              padding: 10px 8px;
              font-size: 0.9rem;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            #btn-discard-all {
              background: #dc3545;
              border-color: #dc3545;
              color: white;
            }

            #btn-discard-all:hover {
              background: #c82333;
              border-color: #c82333;
            }

            #btn-save-changes {
              background: #28a745;
              border-color: #28a745;
            }

            #btn-save-changes:hover {
              background: #218838;
              border-color: #218838;
            }

            /* Botão fechar elegante - círculo com X */
            #btn-close-modal {
              background: transparent;
              border: 2px solid #dee2e6;
              color: #6c757d;
              width: 40px;
              height: 40px;
              border-radius: 50%;
              padding: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 16px;
              margin: 0 auto;
            }

            #btn-close-modal:hover {
              border-color: #adb5bd;
              color: #495057;
              background: rgba(108, 117, 125, 0.1);
              transform: translateY(-1px);
            }

            #btn-close-modal i {
              margin: 0;
            }
          </style>
        `,
        icon: null,
        showConfirmButton: false,
        showCancelButton: false,
        showDenyButton: false,
        customClass: {
          popup: 'pending-changes-info-popup'
        },
        width: '600px',
        didOpen: () => {
          // Adicionar listeners para os botões de descartar individual
          const discardButtons = document.querySelectorAll('.btn-discard-change');
          discardButtons.forEach(button => {
            button.addEventListener('click', (e) => {
              e.preventDefault();
              e.stopPropagation();
              const fieldName = button.getAttribute('data-field');
              if (fieldName) {
                // Fechar o modal atual
                cSwal.close();
                // Executar descarte individual
                discardSingleChange(fieldName, draft);
              }
            });
          });

          // Adicionar listeners para os botões customizados
          const btnViewPatient = document.getElementById('btn-view-patient');
          const btnDiscardAll = document.getElementById('btn-discard-all');
          const btnSaveChanges = document.getElementById('btn-save-changes');
          const btnCloseModal = document.getElementById('btn-close-modal');

          if (btnViewPatient) {
            btnViewPatient.addEventListener('click', (e) => {
              e.preventDefault();
              cSwal.close();
              globalDrafts.openPatient(draft);
            });
          }

          if (btnDiscardAll) {
            btnDiscardAll.addEventListener('click', (e) => {
              e.preventDefault();
              cSwal.close();
              discardAllChanges(draft);
            });
          }

          if (btnSaveChanges) {
            btnSaveChanges.addEventListener('click', (e) => {
              e.preventDefault();
              cSwal.close();
              saveReviewedChanges(cached, draft);
            });
          }

          if (btnCloseModal) {
            btnCloseModal.addEventListener('click', (e) => {
              e.preventDefault();
              cSwal.close();
            });
          }
        }
      });
    };

    // Descartar alteração individual
    const discardSingleChange = (fieldName, draft) => {
      cSwal.fire({
        title: 'Descartar alteração?',
        text: `Deseja realmente descartar a alteração do campo "${fieldName}"?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sim, descartar',
        cancelButtonText: 'Cancelar',
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d'
      }).then((result) => {
        if (result.isConfirmed) {
          // Remover campo específico do cache
          const cacheData = localStorage.getItem(draft.cacheKey);
          if (cacheData) {
            const cached = JSON.parse(cacheData);

            // Remover campo das alterações
            cached.changedFields = cached.changedFields.filter(field => field !== fieldName);

            // Restaurar valor original no paciente
            if (cached.originalPaciente[fieldName] !== undefined) {
              cached.paciente[fieldName] = cached.originalPaciente[fieldName];
            }

            // Atualizar cache
            if (cached.changedFields.length > 0) {
              localStorage.setItem(draft.cacheKey, JSON.stringify(cached));

              // Atualizar draft global
              globalDrafts.addOrUpdateDraft({
                ...draft,
                changesCount: cached.changedFields.length
              });
            } else {
              // Se não há mais alterações, remover tudo
              localStorage.removeItem(draft.cacheKey);
              globalDrafts.removeDraft(draft.patientId);
            }

            cSwal.fire({
              title: 'Alteração descartada!',
              text: 'A alteração foi removida com sucesso.',
              icon: 'success',
              timer: 2000,
              showConfirmButton: false
            });

            // console.log(`✅ Campo "${fieldName}" descartado com sucesso`);
          }
        }
      });
    };

    // Descartar todas as alterações
    const discardAllChanges = (draft) => {
      cSwal.fire({
        title: 'Descartar todas as alterações?',
        text: 'Esta ação não pode ser desfeita. Todas as alterações não salvas serão perdidas.',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sim, descartar tudo',
        cancelButtonText: 'Cancelar',
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d'
      }).then((result) => {
        if (result.isConfirmed) {
          // Remover cache e draft
          localStorage.removeItem(draft.cacheKey);
          globalDrafts.removeDraft(draft.patientId);

          cSwal.fire({
            title: 'Alterações descartadas!',
            text: 'Todas as alterações foram removidas com sucesso.',
            icon: 'success',
            timer: 2000,
            showConfirmButton: false
          });

          // console.log(`✅ Todas as alterações do paciente ${draft.patientName} foram descartadas`);
        }
      });
    };

    // Salvar alterações revisadas
    const saveReviewedChanges = async (cached, draft) => {
      try {
        // Confirmar salvamento com o usuário
        cSwal.cConfirm(
          `Deseja realmente salvar as alterações de ${draft.patientName} no banco de dados?`,
          async () => {
            try {
              // Salvar no banco usando o serviço
              const { updatePaciente } = await import('@/services/pacientesService');
              const update = await updatePaciente(cached.paciente);

              if (update) {
                cSwal.cSuccess(`As alterações de ${draft.patientName} foram salvas no banco de dados.`);

                // Limpar cache local
                localStorage.removeItem(draft.cacheKey);

                // Remover o draft após salvar com sucesso, notificando sobre o salvamento
                globalDrafts.removeDraft(draft.patientId, {
                  savedViaModal: true,
                  savedPatientData: cached.paciente
                });
              } else {
                cSwal.cError("Ocorreu um erro ao salvar as alterações.");
              }
            } catch (error) {
              console.error('Erro ao salvar alterações:', error);
              showErrorToast('Erro ao salvar alterações. Tente novamente.');
            }
          }
        );
      } catch (error) {
        console.error('Erro ao processar salvamento:', error);
        showErrorToast('Erro ao processar salvamento.');
      }
    };

    const closeReviewModal = () => {
      showReviewModal.value = false;
      currentReviewDraft.value = null;
      reviewChanges.value = null;
      globalDrafts.closeAllTooltips();
    };



    // Gerar preview das alterações com botões de descartar
    const generateChangesPreview = (cached, includeDiscardButtons = false) => {
      const fieldMapping = {
        'nome': { label: 'Nome completo', category: 'Dados Pessoais' },
        'cpf': { label: 'CPF', category: 'Dados Pessoais' },
        'rg': { label: 'RG', category: 'Dados Pessoais' },
        'data_nascimento': { label: 'Data de nascimento', category: 'Dados Pessoais' },
        'como_conheceu': { label: 'Como conheceu a clínica', category: 'Dados Pessoais' },
        'nome_mae': { label: 'Nome da mãe', category: 'Dados Pessoais' },
        'nome_pai': { label: 'Nome do pai', category: 'Dados Pessoais' },
        'observacoes': { label: 'Observações', category: 'Dados Pessoais' },
        'dentista_id': { label: 'Profissional responsável', category: 'Dados Pessoais' },
        'responsavel_nome': { label: 'Nome do responsável', category: 'Responsável' },
        'responsavel_cpf': { label: 'CPF do responsável', category: 'Responsável' },
        'responsavel_rg': { label: 'RG do responsável', category: 'Responsável' },
        'endereco_cep': { label: 'CEP', category: 'Endereço' },
        'endereco_estado': { label: 'Estado', category: 'Endereço' },
        'endereco_cidade': { label: 'Cidade', category: 'Endereço' },
        'endereco_logradouro': { label: 'Logradouro', category: 'Endereço' },
        'endereco_numero': { label: 'Número', category: 'Endereço' },
        'endereco_complemento': { label: 'Complemento', category: 'Endereço' }
      };

      // Agrupar alterações por categoria
      const changesByCategory = {};

      cached.changedFields.forEach(fieldName => {
        const fieldInfo = fieldMapping[fieldName];
        if (fieldInfo) {
          const category = fieldInfo.category;
          if (!changesByCategory[category]) {
            changesByCategory[category] = [];
          }

          // Obter valores antigo e novo
          const oldValue = cached.originalPaciente[fieldName] || '';
          const newValue = cached.paciente[fieldName] || '';

          changesByCategory[category].push({
            field: fieldName,
            label: fieldInfo.label,
            oldValue: formatFieldValue(fieldName, oldValue),
            newValue: formatFieldValue(fieldName, newValue)
          });
        }
      });

      // Gerar tabela HTML limpa
      let html = '<table class="changes-table table table-sm">';
      html += `
        <thead class="table-light">
          <tr>
            <th>Campo</th>
            <th>Valor Anterior</th>
            <th>Novo Valor</th>
            ${includeDiscardButtons ? '<th width="80"></th>' : ''}
          </tr>
        </thead>
        <tbody>
      `;

      Object.keys(changesByCategory).forEach(category => {
        const changes = changesByCategory[category];

        // Header da categoria
        html += `
          <tr class="table-secondary">
            <td colspan="${includeDiscardButtons ? '4' : '3'}" class="category-header">
              <strong>${category} (${changes.length} ${changes.length === 1 ? 'alteração' : 'alterações'})</strong>
            </td>
          </tr>
        `;

        // Linhas das alterações
        changes.forEach(change => {
          html += `
            <tr class="change-row">
              <td class="field-name">${change.label}</td>
              <td class="old-value">${change.oldValue || '<em>vazio</em>'}</td>
              <td class="new-value">${change.newValue || '<em>vazio</em>'}</td>
              ${includeDiscardButtons ? `
                <td class="action-cell">
                  <button class="btn-discard-change" data-field="${change.field}" title="Descartar esta alteração">
                    <i class="fas fa-trash"></i>
                  </button>
                </td>
              ` : ''}
            </tr>
          `;
        });
      });

      html += '</tbody></table>';
      return html;
    };

    const formatFieldValue = (fieldName, value) => {
      if (!value || value === '') return '';

      // Formatações específicas por tipo de campo
      switch (fieldName) {
        case 'cpf':
        case 'responsavel_cpf':
          return value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
        case 'endereco_cep':
          return value.replace(/(\d{5})(\d{3})/, '$1-$2');
        case 'data_nascimento':
          if (value.includes('-')) {
            const [year, month, day] = value.split('-');
            return `${day}/${month}/${year}`;
          }
          return value;
        default:
          return value;
      }
    };

    const formatDate = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    onMounted(() => {
      // Inicializar o sistema de drafts globais
      console.log('🚀 GlobalDraftsManager montado - inicializando sistema de drafts');
      globalDrafts.initialize();

      // Log para debug
      setTimeout(() => {
        console.log('📊 Drafts carregados:', globalDrafts.draftsArray.value.length);
      }, 100);
    });

    return {
      draftsArray,
      showReviewModal,
      currentReviewDraft,
      reviewChanges,
      handleToggleTooltip,
      handleCloseTooltip,
      handleOpenPatient,
      handleReviewAndSave,
      closeReviewModal,
      saveReviewedChanges,
      formatDate
    };
  }
};
</script>

<style scoped>
.changes-table-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 6px;
}

.changes-table {
  margin: 0;
}

.changes-table th {
  position: sticky;
  top: 0;
  background: #f8f9fa;
  z-index: 10;
}

.old-value {
  background: #fff3cd;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.9em;
}

.new-value {
  background: #d1ecf1;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9em;
}
</style>
