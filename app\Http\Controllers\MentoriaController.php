<?php

namespace App\Http\Controllers;

use App\Models\Mentoria;
use App\Models\MentoriaMensagem;
use App\Models\Paciente;
use App\Models\Dentista;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Utils;
use App\Models\MetaTerapeutica;

class MentoriaController extends Controller
{
    public function getAll(Request $request)
    {
        try {
            $user = auth()->payload();

            // Se for system_admin, retorna todas as mentorias
            // Se não for, retorna apenas as mentorias onde o usuário é o solicitante
            if ($user['system_admin']) {
                $mentorias = Mentoria::with([
                    'paciente' => function($query) {
                        $query->withoutGlobalScope(\App\Scopes\ClinicaScope::class);
                    },
                    'paciente.clinica',
                    'solicitante' => function($query) {
                        $query->withoutGlobalScope(\App\Scopes\ClinicaScope::class);
                    }
                ])
                    ->leftJoin('mentoria_mensagens', function($join) {
                        $join->on('mentorias.id', '=', 'mentoria_mensagens.mentoria_id')
                             ->whereRaw('mentoria_mensagens.created_at = (SELECT MAX(created_at) FROM mentoria_mensagens WHERE mentoria_id = mentorias.id)');
                    })
                    ->select('mentorias.*', 'mentoria_mensagens.created_at as ultima_mensagem')
                    ->get();
            } else {
                // Buscar mentorias onde o solicitante_id é o dentista do usuário logado
                $dentista_id = $user['dentista']['id'] ?? null;

                if (!$dentista_id) {
                    return responseError(['message' => 'Usuário não possui dentista associado']);
                }

                $mentorias = Mentoria::with([
                    'paciente' => function($query) {
                        $query->withoutGlobalScope(\App\Scopes\ClinicaScope::class);
                    },
                    'paciente.clinica',
                    'solicitante' => function($query) {
                        $query->withoutGlobalScope(\App\Scopes\ClinicaScope::class);
                    }
                ])
                    ->leftJoin('mentoria_mensagens', function($join) {
                        $join->on('mentorias.id', '=', 'mentoria_mensagens.mentoria_id')
                             ->whereRaw('mentoria_mensagens.created_at = (SELECT MAX(created_at) FROM mentoria_mensagens WHERE mentoria_id = mentorias.id)');
                    })
                    ->select('mentorias.*', 'mentoria_mensagens.created_at as ultima_mensagem')
                    ->where('mentorias.solicitante_id', $dentista_id)
                    ->get();
            }

            $response = responseSuccess(['data' => $mentorias]);
        } catch (\Exception $e) {
            // Log::error($e->getMessage());
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function solicitarMentoria(Request $request)
    {
        $body = $request->all();

        try {

            DB::transaction(function () use ($body) {
                // Delete existing analyses for the paciente_id
                Mentoria::where('paciente_id', $body['paciente_id'])->delete();

                $user = auth()->payload();
                $dentista_id = $user['dentista']['id'] ?? null;

                $mentoria = new Mentoria();
                $mentoria->paciente_id = $body['paciente_id'];
                $mentoria->solicitante_id = $dentista_id;
                $mentoria->observacao = $body['observacao'];
                $mentoria->save();
            });

            $response = responseSuccess();
        } catch (\Exception $e) {
            // Log::error($e->getMessage());
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function getMentoria(Request $request, $id)
    {
        try {
            $user = auth()->payload();

            $mentoria = Mentoria::with([
                'paciente' => function($query) {
                    $query->withoutGlobalScope(\App\Scopes\ClinicaScope::class);
                },
                'paciente.clinica',
                'solicitante' => function($query) {
                    $query->withoutGlobalScope(\App\Scopes\ClinicaScope::class);
                },
                'mentor' => function($query) {
                    $query->withoutGlobalScope(\App\Scopes\ClinicaScope::class);
                },
                'mensagens.remetente' => function($query) {
                    $query->withoutGlobalScope(\App\Scopes\ClinicaScope::class);
                },
                'mensagens.imagensRelacionadas' // Eager load das imagens relacionadas
            ])->find($id);

            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões - system_admin ou solicitante da mentoria
            $dentista_id = $user['dentista']['id'] ?? null;
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            $response = responseSuccess(['data' => $mentoria]);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function enviarMensagem(Request $request)
    {
        $body = $request->all();

        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            // Verificar se a mentoria existe e se o usuário tem permissão
            $mentoria = Mentoria::find($body['mentoria_id']);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões: system_admin ou solicitante da mentoria
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            $mensagem = null;
            DB::transaction(function () use ($body, $dentista_id, $user, $mentoria, &$mensagem) {
                // Criar a mensagem
                $mensagem = new MentoriaMensagem();
                $mensagem->mentoria_id = $body['mentoria_id'];
                $mensagem->remetente_id = $dentista_id; // Usa dentista_id
                $mensagem->mensagem = $body['mensagem'];

                // Adicionar imagens se fornecidas
                if (isset($body['imagens']) && is_array($body['imagens'])) {
                    $mensagem->imagens = $body['imagens'];
                }

                $mensagem->save();

                // Criar notificações
                $this->criarNotificacoesMensagem($mentoria, $mensagem, $user);
            });

            $response = responseSuccess();
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function enviarMensagemComImagens(Request $request)
    {
        $body = $request->all();

        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            // Verificar se a mentoria existe e se o usuário tem permissão
            $mentoria = Mentoria::find($body['mentoria_id']);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões: system_admin ou solicitante da mentoria
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            // Validar se imagens foram fornecidas
            if (!isset($body['imagens']) || !is_array($body['imagens']) || empty($body['imagens'])) {
                return responseError(['message' => 'Nenhuma imagem fornecida']);
            }

            $mensagem = null;
            DB::transaction(function () use ($body, $dentista_id, $user, $mentoria, &$mensagem) {
                // Criar a mensagem
                $mensagem = new MentoriaMensagem();
                $mensagem->mentoria_id = $body['mentoria_id'];
                $mensagem->remetente_id = $dentista_id;
                $mensagem->mensagem = $body['mensagem'];
                $mensagem->save();

                // Associar imagens através da tabela pivot
                foreach ($body['imagens'] as $imagemData) {
                    $mensagem->imagensRelacionadas()->attach($imagemData['id'], [
                        'observation' => $imagemData['observation'] ?? null
                    ]);
                }

                // Criar notificações
                $this->criarNotificacoesMensagem($mentoria, $mensagem, $user);
            });

            $response = responseSuccess();
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function marcarMensagensComoLidas(Request $request, $mentoriaId)
    {
        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            // Verificar se a mentoria existe e se o usuário tem permissão
            $mentoria = Mentoria::find($mentoriaId);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            // Marcar mensagens como lidas (exceto as próprias)
            MentoriaMensagem::where('mentoria_id', $mentoriaId)
                ->where('remetente_id', '!=', $dentista_id) // Usa dentista_id
                ->where('lida', false)
                ->update(['lida' => true]);

            $response = responseSuccess();
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    private function criarNotificacoesMensagem($mentoria, $mensagem, $user)
    {
        $action_url = "/pacientes?mentoria_id=" . $mentoria->id;

        if ($user['system_admin']) {
            // Mentor enviou mensagem - notificar o solicitante
            // Buscar o user_id do dentista solicitante
            $solicitante = Dentista::with('user')->find($mentoria->solicitante_id);
            if ($solicitante && $solicitante->user) {
                $notification = new Notification();
                $notification->user_id = $solicitante->user->id;
                $notification->title = 'Nova mensagem na mentoria';
                $notification->message = 'Você recebeu uma nova mensagem do mentor na mentoria do paciente ' . $mentoria->paciente->nome;
                $notification->action_url = $action_url;
                $notification->save();
            }
        } else {
            // Solicitante enviou mensagem - notificar todos os system_admin
            $systemAdmins = Dentista::whereHas('user', function($query) {
                $query->where('system_admin', true);
            })->with('user')->get();

            foreach ($systemAdmins as $admin) {
                if ($admin->user) {
                    $notification = new Notification();
                    $notification->user_id = $admin->user->id;
                    $notification->title = 'Nova mensagem na mentoria';
                    $notification->message = 'Nova mensagem na mentoria do paciente ' . $mentoria->paciente->nome . ' de ' . $mentoria->solicitante->nome;
                    $notification->action_url = $action_url;
                    $notification->save();
                }
            }
        }
    }

    public function iniciarMentoria(Request $request, $id)
    {
        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$user['system_admin']) {
                return responseError(['message' => 'Apenas administradores podem iniciar mentorias']);
            }

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            $mentoria = Mentoria::find($id);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            if ($mentoria->status !== 'AGUARDANDO') {
                return responseError(['message' => 'Esta mentoria não pode ser iniciada']);
            }

            DB::transaction(function () use ($mentoria, $dentista_id, $user) {
                // Atualizar status e mentor
                $mentoria->status = 'EM_ANDAMENTO';
                $mentoria->mentor_id = $dentista_id;
                $mentoria->iniciada_em = now();
                $mentoria->save();

                // Criar mensagem do sistema
                $nomeUsuario = explode(' ', $user['dentista']['nome'])[0];
                $this->criarMensagemSistema($mentoria, 'Mentoria iniciada por ' . $nomeUsuario);
            });

            $response = responseSuccess(['message' => 'Mentoria iniciada com sucesso']);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function finalizarMentoria(Request $request, $id)
    {
        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            $mentoria = Mentoria::find($id);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            if ($mentoria->status === 'FINALIZADA' || $mentoria->status === 'CANCELADA') {
                return responseError(['message' => 'Esta mentoria já foi finalizada ou cancelada']);
            }

            DB::transaction(function () use ($mentoria, $user) {
                // Atualizar status
                $mentoria->status = 'FINALIZADA';
                $mentoria->finalizada_em = now();
                $mentoria->save();

                // Criar mensagem do sistema
                $nomeUsuario = explode(' ', $user['dentista']['nome'])[0];
                $this->criarMensagemSistema($mentoria, 'Mentoria finalizada por ' . $nomeUsuario);
            });

            $response = responseSuccess(['message' => 'Mentoria finalizada com sucesso']);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function cancelarMentoria(Request $request, $id)
    {
        try {
            $user = auth()->payload();
            $dentista_id = $user['dentista']['id'] ?? null;

            if (!$dentista_id) {
                return responseError(['message' => 'Usuário não possui dentista associado']);
            }

            $mentoria = Mentoria::find($id);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            // Verificar permissões
            if (!$user['system_admin'] && $mentoria->solicitante_id != $dentista_id) {
                return responseError(['message' => 'Acesso negado']);
            }

            if ($mentoria->status === 'FINALIZADA' || $mentoria->status === 'CANCELADA') {
                return responseError(['message' => 'Esta mentoria já foi finalizada ou cancelada']);
            }

            DB::transaction(function () use ($mentoria, $user) {
                // Atualizar status
                $mentoria->status = 'CANCELADA';
                $mentoria->cancelada_em = now();
                $mentoria->save();

                // Criar mensagem do sistema
                $nomeUsuario = explode(' ', $user['dentista']['nome'])[0];
                $this->criarMensagemSistema($mentoria, 'Mentoria cancelada por ' . $nomeUsuario);
            });

            $response = responseSuccess(['message' => 'Mentoria cancelada com sucesso']);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    public function reabrirMentoria(Request $request, $id)
    {
        try {
            $user = auth()->payload();

            // Verificar se é system_admin
            if (!$user['system_admin']) {
                return responseError(['message' => 'Apenas administradores podem reabrir mentorias']);
            }

            $mentoria = Mentoria::find($id);
            if (!$mentoria) {
                return responseError(['message' => 'Mentoria não encontrada']);
            }

            if (!in_array($mentoria->status, ['CANCELADA', 'FINALIZADA'])) {
                return responseError(['message' => 'Apenas mentorias canceladas ou finalizadas podem ser reabertas']);
            }

            DB::transaction(function () use ($mentoria, $user) {
                $mentoria->status = 'AGUARDANDO';
                $mentoria->mentor_id = null;
                $mentoria->iniciada_em = null;
                $mentoria->cancelada_em = null;
                $mentoria->save();

                // Criar mensagem do sistema
                $nomeUsuario = explode(' ', $user['dentista']['nome'])[0];
                $this->criarMensagemSistema($mentoria, 'Mentoria reaberta por ' . $nomeUsuario);
            });

            $response = responseSuccess(['message' => 'Mentoria reaberta com sucesso']);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }



    private function criarMensagemSistema($mentoria, $mensagem)
    {
        $mensagemSistema = new MentoriaMensagem();
        $mensagemSistema->mentoria_id = $mentoria->id;
        $mensagemSistema->remetente_id = null; // Mensagem do sistema
        $mensagemSistema->mensagem = $mensagem;
        $mensagemSistema->tipo = 'SISTEMA';
        $mensagemSistema->lida = true; // Mensagens do sistema já são "lidas"
        $mensagemSistema->save();
    }

    public function refinarMensagemComIA(Request $request)
    {
        try {
            $user = auth()->payload();

            // Verificar se é system_admin
            if (!$user['system_admin']) {
                return responseError(['message' => 'Apenas administradores podem usar esta funcionalidade']);
            }

            $body = $request->all();

            if (!isset($body['mensagem']) || empty(trim($body['mensagem']))) {
                return responseError(['message' => 'Mensagem não fornecida']);
            }

            $mensagemOriginal = trim($body['mensagem']);

            // Determinar saudação baseada no horário
            $hora = (int) date('H');
            $saudacao = '';
            if ($hora >= 6 && $hora < 12) {
                $saudacao = 'Bom dia!';
            } elseif ($hora >= 12 && $hora < 18) {
                $saudacao = 'Boa tarde!';
            } else {
                $saudacao = 'Boa noite!';
            }

            // Criar o pre-prompt para a IA
            $prePrompt = "Você é um mentor ortodontista experiente conversando com um colega dentista. Sua tarefa é refinar a mensagem abaixo mantendo um tom natural, técnico e profissional, como se fosse uma conversa entre colegas de profissão.

REGRAS IMPORTANTES:
1. SEMPRE comece a mensagem refinada com a saudação: \"{$saudacao}\"
2. Mantenha o TOM e ESTILO do texto original - se algo está em MAIÚSCULAS ou com ênfase, preserve essa ênfase na mensagem refinada usando MAIÚSCULAS em palavras-chave importantes (NÃO use asteriscos ou markdown)
3. Se houver muitas exclamações (!!!) ou texto todo em MAIÚSCULAS, isso indica ÊNFASE FORTE - mantenha essa intensidade usando MAIÚSCULAS
4. Não use frases genéricas de introdução como \"Para uma análise mais precisa\" ou \"É importante ressaltar\" - vá direto ao ponto após a saudação
5. Mantenha a linguagem técnica e específica da ortodontia
6. Não seja excessivamente formal ou robotizado - escreva como um colega falaria com outro
7. Preserve termos técnicos, siglas e especificações exatas (medidas, tipos de aparelhos, etc.)
8. Se o texto original tem um tom mais direto/imperativo, mantenha esse tom
9. Organize melhor o texto em parágrafos quando necessário, mas sem perder a naturalidade
10. NÃO adicione despedidas ou assinaturas

Mensagem original para refinar:
";

            $promptCompleto = $prePrompt . $mensagemOriginal;

            // Chamar a API do Gemini
            $mensagemRefinada = $this->chamarGeminiAPI($promptCompleto);

            if (!$mensagemRefinada) {
                return responseError(['message' => 'Erro ao refinar mensagem com IA']);
            }

            $response = responseSuccess(['data' => ['mensagem_refinada' => $mensagemRefinada]]);
        } catch (\Exception $e) {
            $response = responseError([
                'message' => $e->getMessage(),
                'stacktrace' => $e->getTraceAsString()
            ]);
        }

        return $response;
    }

    private function chamarGeminiAPI($prompt)
    {
        $apiKey = 'AIzaSyA8TaWEmxZsbKtkNpfQ0bv_Pwu37v2aXUU';
        $model = 'gemini-2.0-flash-exp';
        $url = "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent";

        $data = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $prompt
                        ]
                    ]
                ]
            ]
        ];

        $ch = curl_init($url);

        curl_setopt_array($ch, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_HTTPHEADER => [
                'x-goog-api-key: ' . $apiKey,
                'Content-Type: application/json'
            ],
            CURLOPT_POSTFIELDS => json_encode($data)
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            \Log::error("Erro no cURL ao chamar Gemini API: {$error}");
            return false;
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            \Log::error("Erro na API Gemini: HTTP {$httpCode} - {$response}");
            return false;
        }

        $result = json_decode($response, true);

        // Extrai o texto da resposta
        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            return trim($result['candidates'][0]['content']['parts'][0]['text']);
        }

        \Log::error("Estrutura de resposta inesperada da API Gemini: " . json_encode($result));
        return false;
    }
}
