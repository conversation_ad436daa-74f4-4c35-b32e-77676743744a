<template>
  <div class="sidenav-compact-toggle">
    <div class="toggle-container">
      <button
        v-for="option in toggleOptions"
        :key="option.value"
        @click="selectView(option.value)"
        class="toggle-btn"
        :class="{
          'active': modelValue === option.value,
          'inactive': modelValue !== option.value
        }"
      >
        <span class="toggle-label">{{ option.label }}</span>
      </button>
    </div>
  </div>
</template>

<script>
import moment from 'moment';

// Configurar moment para português
moment.locale('pt-br');

export default {
  name: 'SidenavCompactToggle',
  props: {
    modelValue: {
      type: String,
      default: 'week',
      validator: (value) => ['day', 'week', 'month'].includes(value)
    },
    date: {
      type: Date,
      default: () => new Date()
    },
    events: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue', 'view-changed'],
  data() {
    return {
      toggleOptions: [
        { value: 'day', label: 'DIA' },
        { value: 'week', label: 'SEMANA' },
        { value: 'month', label: 'MÊS' }
      ]
    };
  },
  methods: {
    selectView(view) {
      this.$emit('update:modelValue', view);
      this.$emit('view-changed', view);
    },
    
    getIndicator(view) {
      const currentDate = moment(this.date);
      
      switch (view) {
        case 'day':
          return currentDate.format('DD');
        case 'week':
          // Calcular número da semana
          return this.getWeekNumber(currentDate);
        case 'month':
          return currentDate.format('MMM').toUpperCase();
        default:
          return '';
      }
    },
    
    getWeekNumber(date) {
      const target = moment(date);
      const dayNr = (target.day() + 6) % 7;
      target.subtract(dayNr, 'days').add(3, 'days');
      const firstThursday = target.valueOf();
      target.startOf('year');
      if (target.day() !== 4) {
        target.add(((4 - target.day()) + 7) % 7, 'days');
      }
      return 1 + Math.ceil((firstThursday - target.valueOf()) / 604800000);
    },
    
    getEventCount(view) {
      if (!this.events || this.events.length === 0) return 0;
      
      const currentDate = moment(this.date);
      let count = 0;
      
      try {
        if (view === 'day') {
          // Contar eventos do dia
          count = this.events.filter(event => {
            const eventDate = moment(event.date);
            return eventDate.isSame(currentDate, 'day');
          }).length;
        } else if (view === 'week') {
          // Contar eventos da semana
          const startOfWeek = moment(currentDate).startOf('week');
          const endOfWeek = moment(currentDate).endOf('week');
          count = this.events.filter(event => {
            const eventDate = moment(event.date);
            return eventDate.isBetween(startOfWeek, endOfWeek, null, '[]');
          }).length;
        } else if (view === 'month') {
          // Contar eventos do mês
          count = this.events.filter(event => {
            const eventDate = moment(event.date);
            return eventDate.isSame(currentDate, 'month');
          }).length;
        }
      } catch (error) {
        console.error('Erro ao calcular eventos:', error);
        return 0;
      }
      
      return count;
    }
  }
};
</script>

<style scoped>
.sidenav-compact-toggle {
  width: 100%;
  padding: 0 12px;
  margin: 2px 0 6px 0;
}

.toggle-container {
  display: flex;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  padding: 2px;
  gap: 2px;
}

.toggle-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 4px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  background: transparent;
  min-height: 38px;
  position: relative;
}

.toggle-icon {
  font-size: 14px;
  margin-bottom: 4px;
  line-height: 1;
  transition: all 0.25s ease;
}

.toggle-label {
  font-size: 9px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
  transition: all 0.25s ease;
}

/* Estado inativo */
.toggle-btn.inactive {
  background: #fefefe;
  color: #64748b;
  border: 1px solid transparent;
}

.toggle-btn.inactive:hover {
  background: #ffffff;
  color: #475569;
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.toggle-btn.inactive:hover .toggle-icon {
  color: #0ea5e9;
}

.toggle-btn.inactive:hover .toggle-label {
  color: #475569;
}

/* Estado ativo */
.toggle-btn.active {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%) !important;
  color: #ffffff !important;
  box-shadow:
    0 3px 8px rgba(14, 165, 233, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(14, 165, 233, 0.2);
  border: 1px solid rgba(14, 165, 233, 0.3);
  transform: translateY(-1px);
}

.toggle-btn.active .toggle-icon {
  color: #ffffff !important;
}

.toggle-btn.active .toggle-label {
  color: #ffffff !important;
}

.toggle-btn.active:hover {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%) !important;
  color: #ffffff !important;
}

.toggle-btn.active::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(14, 165, 233, 0.1) 0%, rgba(2, 132, 199, 0.1) 100%);
  border-radius: 6px;
  z-index: -1;
  opacity: 0.6;
}

.toggle-btn.active .toggle-label {
  color: rgba(255, 255, 255, 0.95);
  opacity: 1;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.toggle-btn.active .toggle-indicator {
  color: #ffffff;
  font-weight: 800;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.toggle-btn.active .toggle-count {
  color: rgba(255, 255, 255, 0.9);
  opacity: 1;
  font-weight: 600;
}

/* Responsividade para sidenav muito estreita */
@media (max-width: 280px) {
  .toggle-label {
    font-size: 7px;
  }
  
  .toggle-indicator {
    font-size: 11px;
  }
  
  .toggle-count {
    font-size: 6px;
  }
  
  .toggle-btn {
    min-height: 40px;
    padding: 5px 3px;
  }
}

/* Ajustes para diferentes tamanhos de sidenav */
.sidenav-compact-toggle {
  max-width: 100%;
  overflow: hidden;
}

.toggle-container {
  min-width: 0; /* Permite que o flex shrink funcione */
}

.toggle-btn {
  min-width: 0; /* Permite que os botões encolham se necessário */
  white-space: nowrap;
  overflow: hidden;
}

/* Animações suaves */
.toggle-btn {
  will-change: transform, background-color, box-shadow;
}

.toggle-label,
.toggle-indicator,
.toggle-count {
  will-change: color, opacity;
}
</style>
