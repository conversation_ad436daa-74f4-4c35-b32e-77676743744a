<template>
  <div
    class="modal fade"
    id="importTabelaPrecosModal"
    tabindex="-1"
    aria-labelledby="importTabelaPrecosModalLabel"
    aria-hidden="true"
    data-bs-backdrop="static"
    data-bs-keyboard="false"
  >
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content modal-content-full">
        <!-- Header -->
        <div class="modal-header bg-gradient-primary text-white">
          <h5 class="modal-title" id="importTabelaPrecosModalLabel">
            <i class="fas fa-file-import me-2"></i>
            Importar Tabela de Preços
          </h5>
          <button
            type="button"
            class="btn-close btn-close-white"
            @click="fechar"
            :disabled="importando"
          ></button>
        </div>

        <!-- Body -->
        <div class="modal-body">
          <!-- Etapa 1: Upload do arquivo -->
          <div v-if="etapa === 1" class="upload-section h-100 d-flex flex-column">
            <div class="row flex-grow-1">
              <!-- Coluna Esquerda: Upload -->
              <div class="col-md-6 d-flex flex-column">
                <div class="text-center mb-3 flex-shrink-0">
                  <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-2"></i>
                  <h6>Selecione o arquivo para importação</h6>
                  <p class="text-muted small mb-3">
                    Formatos aceitos: Excel (.xlsx, .xls) ou CSV (.csv)
                  </p>
                </div>

                <!-- Área de upload -->
                <div
                  class="upload-area flex-grow-1"
                  :class="{ 'drag-over': dragOver }"
                  @drop.prevent="handleDrop"
                  @dragover.prevent="dragOver = true"
                  @dragleave.prevent="dragOver = false"
                  @click="$refs.fileInput.click()"
                >
                  <input
                    type="file"
                    ref="fileInput"
                    @change="handleFileSelect"
                    accept=".xlsx,.xls,.csv"
                    class="d-none"
                  />
                  <div class="upload-content">
                    <i class="fas fa-file-excel fa-3x text-success mb-2"></i>
                    <p class="mb-1">
                      <strong>Clique para selecionar</strong> ou arraste o arquivo aqui
                    </p>
                    <p class="text-muted small mb-0">Tamanho máximo: 5MB</p>
                  </div>
                </div>

                <!-- Arquivo selecionado -->
                <div v-if="arquivo" class="alert alert-success mt-3 mb-0 alert-white-text flex-shrink-0">
                  <div class="d-flex align-items-center justify-content-between">
                    <div>
                      <i class="fas fa-check-circle me-2"></i>
                      <strong>{{ arquivo.name }}</strong>
                      <span class="ms-2 small">({{ formatFileSize(arquivo.size) }})</span>
                    </div>
                    <button class="btn btn-sm btn-outline-light" @click.stop="removerArquivo">
                      <i class="fas fa-times"></i>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Coluna Direita: Colunas Esperadas -->
              <div class="col-md-6 d-flex flex-column">
                <div class="colunas-esperadas-container flex-grow-1 d-flex flex-column">
                  <div class="flex-shrink-0">
                    <h6 class="mb-2">
                      <i class="fas fa-info-circle me-2 text-primary"></i>
                      Colunas Esperadas
                    </h6>
                    <p class="text-muted small mb-2">
                      Sua planilha deve conter as seguintes colunas (a ordem não importa):
                    </p>
                  </div>
                  <ul class="colunas-lista flex-grow-1 overflow-auto">
                    <li v-for="campo in camposEsperados" :key="campo.key" class="coluna-item">
                      <strong>{{ campo.label }}</strong>
                      <span v-if="campo.obrigatorio" class="text-danger fw-bold"> (obrigatório)</span>
                      <span class="campo-desc"> - {{ campo.descricao }}</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Botão para processar -->
            <div class="text-end mt-3 flex-shrink-0" v-if="arquivo">
              <button
                class="btn btn-primary"
                @click="processarArquivo"
                :disabled="!arquivo || processando"
              >
                <span v-if="processando">
                  <span class="spinner-border spinner-border-sm me-2"></span>
                  Carregando biblioteca...
                </span>
                <span v-else>
                  <i class="fas fa-arrow-right me-2"></i>
                  Próximo: Mapear Colunas
                </span>
              </button>
            </div>
          </div>

          <!-- Etapa 2: Mapeamento de colunas -->
          <div v-if="etapa === 2" class="mapping-section h-100 d-flex flex-column">
            <div class="alert alert-info compact-alert mb-3 alert-white-text flex-shrink-0">
              <i class="fas fa-map-marked-alt me-2"></i>
              <strong>Mapeie as colunas</strong> - {{ dadosPreview.length }} linhas encontradas
            </div>

            <!-- 2 Tabelas de Mapeamento -->
            <div class="row flex-grow-1 overflow-auto gx-3">
              <!-- Tabela Esquerda -->
              <div class="col-md-6">
                <div class="table-responsive mapping-table-wrapper">
                  <table class="table table-sm table-bordered mb-0">
                    <thead class="table-light sticky-top">
                      <tr>
                        <th style="width: 40%">Coluna</th>
                        <th style="width: 60%">Mapear para</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(coluna, index) in colunasArquivoMetadeEsquerda" :key="'left-' + index">
                        <td class="align-middle">
                          <span v-if="mapeamento[coluna] && isCampoObrigatorio(mapeamento[coluna])" class="text-danger me-1">*</span>
                          <strong class="small">{{ coluna }}</strong>
                        </td>
                        <td class="align-middle">
                          <select
                            class="form-select form-select-sm"
                            v-model="mapeamento[coluna]"
                          >
                            <option value="">-- Ignorar --</option>
                            <option
                              v-for="campo in camposEsperados"
                              :key="campo.key"
                              :value="campo.key"
                            >
                              {{ campo.obrigatorio ? '* ' : '' }}{{ campo.label }}
                            </option>
                          </select>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <!-- Tabela Direita -->
              <div class="col-md-6">
                <div class="table-responsive mapping-table-wrapper">
                  <table class="table table-sm table-bordered mb-0">
                    <thead class="table-light sticky-top">
                      <tr>
                        <th style="width: 40%">Coluna</th>
                        <th style="width: 60%">Mapear para</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="(coluna, index) in colunasArquivoMetadeDireita" :key="'right-' + index">
                        <td class="align-middle">
                          <span v-if="mapeamento[coluna] && isCampoObrigatorio(mapeamento[coluna])" class="text-danger me-1">*</span>
                          <strong class="small">{{ coluna }}</strong>
                        </td>
                        <td class="align-middle">
                          <select
                            class="form-select form-select-sm"
                            v-model="mapeamento[coluna]"
                          >
                            <option value="">-- Ignorar --</option>
                            <option
                              v-for="campo in camposEsperados"
                              :key="campo.key"
                              :value="campo.key"
                            >
                              {{ campo.obrigatorio ? '* ' : '' }}{{ campo.label }}
                            </option>
                          </select>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- Validação do mapeamento -->
            <div v-if="errosMapeamento.length > 0" class="alert alert-danger compact-alert mt-3 mb-0 flex-shrink-0 alert-white-text">
              <strong>
                <i class="fas fa-exclamation-triangle me-2"></i>
                Erros no Mapeamento
              </strong>
              <ul class="mb-0 mt-1 small">
                <li v-for="(erro, index) in errosMapeamento" :key="index">{{ erro }}</li>
              </ul>
            </div>

            <!-- Botões de navegação -->
            <div class="d-flex justify-content-between mt-3 flex-shrink-0">
              <button class="btn btn-outline-secondary" @click="voltarParaUpload">
                <i class="fas fa-arrow-left me-2"></i>
                Voltar
              </button>
              <button
                class="btn btn-primary"
                @click="avancarParaPreview"
                :disabled="errosMapeamento.length > 0"
              >
                <i class="fas fa-arrow-right me-2"></i>
                Próximo: Visualizar Dados
              </button>
            </div>
          </div>

          <!-- Etapa 3: Preview e confirmação -->
          <div v-if="etapa === 3" class="preview-section">
            <div class="alert alert-success compact-alert mb-2 alert-white-text">
              <i class="fas fa-check-circle me-2"></i>
              <strong>Pronto para importar!</strong> Revise os dados abaixo.
            </div>

            <!-- Estatísticas Compactas -->
            <div class="stats-compact mb-2">
              <div class="stat-item">
                <i class="fas fa-list-ol text-primary"></i>
                <span class="stat-value">{{ dadosMapeados.length }}</span>
                <span class="stat-label">Itens</span>
              </div>
              <div class="stat-item">
                <i class="fas fa-user-md text-success"></i>
                <span class="stat-value">{{ contarPorTipo('procedimento') }}</span>
                <span class="stat-label">Procedimentos</span>
              </div>
              <div class="stat-item">
                <i class="fas fa-box text-warning"></i>
                <span class="stat-value">{{ contarPorTipo('produto') }}</span>
                <span class="stat-label">Produtos</span>
              </div>
            </div>

            <!-- Preview dos dados -->
            <div class="table-responsive preview-table-container">
              <table class="table table-sm table-striped table-hover mb-0">
                <thead class="table-dark sticky-top compact-thead">
                  <tr>
                    <th>#</th>
                    <th>Código</th>
                    <th>Nome</th>
                    <th>Tipo</th>
                    <th>Valor Base</th>
                    <th>Valor Mín.</th>
                    <th>Valor Máx.</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in dadosMapeados.slice(0, 50)" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td><code>{{ item.codigo || '-' }}</code></td>
                    <td>{{ item.nome }}</td>
                    <td>
                      <span class="badge" :class="getTipoBadgeClass(item.tipo)">
                        {{ getTipoLabel(item.tipo) }}
                      </span>
                    </td>
                    <td>{{ formatCurrency(item.valor_base) }}</td>
                    <td>{{ item.valor_minimo ? formatCurrency(item.valor_minimo) : '-' }}</td>
                    <td>{{ item.valor_maximo ? formatCurrency(item.valor_maximo) : '-' }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <p v-if="dadosMapeados.length > 50" class="text-muted text-center mt-2">
              <small>Mostrando apenas os primeiros 50 itens. Total: {{ dadosMapeados.length }}</small>
            </p>

            <!-- Botões de ação -->
            <div class="d-flex justify-content-between mt-3">
              <button class="btn btn-outline-secondary" @click="voltarParaMapeamento" :disabled="importando">
                <i class="fas fa-arrow-left me-2"></i>
                Voltar
              </button>
              <button
                class="btn btn-success btn-lg"
                @click="confirmarImportacao"
                :disabled="importando"
              >
                <span v-if="importando">
                  <span class="spinner-border spinner-border-sm me-2"></span>
                  Importando...
                </span>
                <span v-else>
                  <i class="fas fa-check me-2"></i>
                  Confirmar Importação
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { servicoProdutoService } from '@/services/servicoProdutoService';
import cSwal from '@/utils/cSwal';
import { Modal } from 'bootstrap';

export default {
  name: 'ImportTabelaPrecosModal',
  data() {
    return {
      modalInstance: null,
      etapa: 1, // 1: Upload, 2: Mapeamento, 3: Preview
      arquivo: null,
      dragOver: false,
      processando: false,
      importando: false,

      // Dados do arquivo
      colunasArquivo: [],
      dadosPreview: [],
      mapeamento: {},
      dadosMapeados: [],

      // Campos esperados (obrigatórios primeiro)
      camposEsperados: [
        { key: 'nome', label: 'Nome', obrigatorio: true, descricao: 'Nome do procedimento/produto' },
        { key: 'tipo', label: 'Tipo', obrigatorio: true, descricao: 'procedimento ou produto' },
        { key: 'valor_base', label: 'Valor Base', obrigatorio: true, descricao: 'Valor padrão (R$)' },
        { key: 'codigo', label: 'Código', obrigatorio: false, descricao: 'Código único do item' },
        { key: 'descricao', label: 'Descrição', obrigatorio: false, descricao: 'Descrição detalhada' },
        { key: 'valor_minimo', label: 'Valor Mínimo', obrigatorio: false, descricao: 'Valor mínimo (R$)' },
        { key: 'valor_maximo', label: 'Valor Máximo', obrigatorio: false, descricao: 'Valor máximo (R$)' },
        { key: 'tempo_estimado', label: 'Tempo Estimado', obrigatorio: false, descricao: 'Duração estimada' },
        { key: 'unidade_tempo', label: 'Unidade Tempo', obrigatorio: false, descricao: 'minutos, horas ou sessões' },
        { key: 'observacoes', label: 'Observações', obrigatorio: false, descricao: 'Observações adicionais' },
        { key: 'ativo', label: 'Ativo', obrigatorio: false, descricao: 'Sim/Não, 1/0, S/N' },
      ],
    };
  },
  computed: {
    colunasArquivoOrdenadas() {
      // Ordenar: obrigatórios mapeados primeiro, depois não mapeados, depois opcionais
      return [...this.colunasArquivo].sort((a, b) => {
        const campoA = this.mapeamento[a];
        const campoB = this.mapeamento[b];

        const obrigatorioA = campoA && this.isCampoObrigatorio(campoA);
        const obrigatorioB = campoB && this.isCampoObrigatorio(campoB);

        if (obrigatorioA && !obrigatorioB) return -1;
        if (!obrigatorioA && obrigatorioB) return 1;
        return 0;
      });
    },
    colunasArquivoMetadeEsquerda() {
      const meio = Math.ceil(this.colunasArquivoOrdenadas.length / 2);
      return this.colunasArquivoOrdenadas.slice(0, meio);
    },
    colunasArquivoMetadeDireita() {
      const meio = Math.ceil(this.colunasArquivoOrdenadas.length / 2);
      return this.colunasArquivoOrdenadas.slice(meio);
    },
    errosMapeamento() {
      const erros = [];

      // Verificar campos obrigatórios
      const camposObrigatorios = this.camposEsperados.filter(c => c.obrigatorio);
      for (const campo of camposObrigatorios) {
        const mapeado = Object.values(this.mapeamento).includes(campo.key);
        if (!mapeado) {
          erros.push(`Campo obrigatório "${campo.label}" não foi mapeado`);
        }
      }

      // Verificar duplicatas
      const valores = Object.values(this.mapeamento).filter(v => v !== '');
      const duplicatas = valores.filter((v, i) => valores.indexOf(v) !== i);
      if (duplicatas.length > 0) {
        erros.push('Existem colunas mapeadas para o mesmo campo');
      }

      return erros;
    },
  },
  methods: {
    abrir() {
      this.resetar();
      if (!this.modalInstance) {
        this.modalInstance = new Modal(document.getElementById('importTabelaPrecosModal'));
      }
      this.modalInstance.show();

      // Ocultar botão flutuante da sidenav (mobile)
      this.ocultarBotaoSidenav(true);
    },

    fechar() {
      if (this.modalInstance) {
        this.modalInstance.hide();
      }
      this.resetar();

      // Mostrar botão flutuante da sidenav novamente
      this.ocultarBotaoSidenav(false);
    },

    ocultarBotaoSidenav(ocultar) {
      // Procurar pelo botão flutuante da sidenav
      const botaoSidenav = document.querySelector('.sidenav-toggler, .navbar-toggler, [data-bs-toggle="sidenav"]');
      if (botaoSidenav) {
        if (ocultar) {
          botaoSidenav.style.display = 'none';
        } else {
          botaoSidenav.style.display = '';
        }
      }
    },

    resetar() {
      this.etapa = 1;
      this.arquivo = null;
      this.dragOver = false;
      this.processando = false;
      this.importando = false;
      this.colunasArquivo = [];
      this.dadosPreview = [];
      this.mapeamento = {};
      this.dadosMapeados = [];
    },

    handleDrop(e) {
      this.dragOver = false;
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        this.handleFile(files[0]);
      }
    },

    handleFileSelect(e) {
      const files = e.target.files;
      if (files.length > 0) {
        this.handleFile(files[0]);
      }
    },

    handleFile(file) {
      // Validar tipo de arquivo
      const extensao = file.name.split('.').pop().toLowerCase();
      if (!['xlsx', 'xls', 'csv'].includes(extensao)) {
        cSwal.cError('Formato de arquivo inválido. Use Excel (.xlsx, .xls) ou CSV (.csv)');
        return;
      }

      // Validar tamanho (5MB)
      if (file.size > 5 * 1024 * 1024) {
        cSwal.cError('Arquivo muito grande. Tamanho máximo: 5MB');
        return;
      }

      this.arquivo = file;

      // Processar automaticamente
      this.processarArquivo();
    },

    removerArquivo() {
      this.arquivo = null;
      this.$refs.fileInput.value = '';
    },

    async processarArquivo() {
      if (!this.arquivo) return;

      this.processando = true;

      try {
        // Carregar XLSX dinamicamente apenas quando necessário
        const XLSX = await import('xlsx');

        const reader = new FileReader();

        reader.onload = (e) => {
          try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });

            // Pegar a primeira planilha
            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

            // Converter para JSON
            const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });

            if (jsonData.length < 2) {
              throw new Error('Arquivo vazio ou sem dados');
            }

            // Primeira linha são os cabeçalhos
            this.colunasArquivo = jsonData[0].filter(col => col && col.toString().trim() !== '');

            // Restante são os dados
            this.dadosPreview = jsonData.slice(1).filter(row => {
              // Filtrar linhas vazias
              return row.some(cell => cell !== null && cell !== undefined && cell.toString().trim() !== '');
            });

            // Tentar mapear automaticamente
            this.tentarMapeamentoAutomatico();

            // Avançar para etapa 2
            this.etapa = 2;
            this.processando = false;
          } catch (error) {
            console.error('Erro ao processar arquivo:', error);
            cSwal.cError('Erro ao processar arquivo: ' + error.message);
            this.processando = false;
          }
        };

        reader.onerror = () => {
          cSwal.cError('Erro ao ler arquivo');
          this.processando = false;
        };

        reader.readAsArrayBuffer(this.arquivo);
      } catch (error) {
        console.error('Erro ao carregar biblioteca XLSX:', error);
        cSwal.cError('Erro ao carregar biblioteca de processamento de planilhas');
        this.processando = false;
      }
    },

    tentarMapeamentoAutomatico() {
      // Tentar mapear automaticamente baseado em nomes similares
      this.mapeamento = {};

      for (const coluna of this.colunasArquivo) {
        const colunaLower = coluna.toLowerCase().trim();

        for (const campo of this.camposEsperados) {
          const campoLower = campo.label.toLowerCase();
          const keyLower = campo.key.toLowerCase();

          if (colunaLower === campoLower ||
              colunaLower === keyLower ||
              colunaLower.includes(keyLower) ||
              keyLower.includes(colunaLower)) {
            this.mapeamento[coluna] = campo.key;
            break;
          }
        }
      }
    },

    getPreviewDados(coluna) {
      const index = this.colunasArquivo.indexOf(coluna);
      const exemplos = this.dadosPreview.slice(0, 3).map(row => row[index]).filter(v => v);
      return exemplos.join(', ') || '-';
    },

    isCampoObrigatorio(campoKey) {
      const campo = this.camposEsperados.find(c => c.key === campoKey);
      return campo ? campo.obrigatorio : false;
    },

    voltarParaUpload() {
      this.etapa = 1;
      this.colunasArquivo = [];
      this.dadosPreview = [];
      this.mapeamento = {};
    },

    avancarParaPreview() {
      if (this.errosMapeamento.length > 0) return;

      // Mapear os dados
      this.dadosMapeados = this.dadosPreview.map((row, rowIndex) => {
        const item = {};

        for (const [coluna, campo] of Object.entries(this.mapeamento)) {
          if (campo) {
            const index = this.colunasArquivo.indexOf(coluna);
            let valor = row[index];

            // Processar valor baseado no tipo de campo
            if (campo === 'valor_base' || campo === 'valor_minimo' || campo === 'valor_maximo') {
              // Converter para número, removendo símbolos de moeda
              valor = this.parseValorMonetario(valor);
            } else if (campo === 'tempo_estimado') {
              valor = parseInt(valor) || null;
            } else if (campo === 'tipo') {
              valor = valor?.toString().toLowerCase().trim();
              // Normalizar tipo
              if (valor === 'proc' || valor === 'procedimento') valor = 'procedimento';
              if (valor === 'prod' || valor === 'produto') valor = 'produto';
            } else if (campo === 'ativo') {
              // Converter para boolean
              valor = this.parseBoolean(valor);
            } else if (campo === 'unidade') {
              // Normalizar unidade de tempo
              valor = this.parseUnidadeTempo(valor);
            }

            item[campo] = valor;
          }
        }

        // Definir valores padrão
        if (!item.tipo) item.tipo = 'procedimento';
        if (!item.unidade) item.unidade = 'minutos';
        if (item.ativo === undefined || item.ativo === null) item.ativo = true;

        return item;
      }).filter(item => item.nome && item.valor_base); // Filtrar itens inválidos

      this.etapa = 3;
    },

    parseValorMonetario(valor) {
      if (!valor) return null;

      // Converter para string e remover símbolos
      let valorStr = valor.toString()
        .replace(/[R$\s]/g, '')
        .replace(/\./g, '')
        .replace(',', '.');

      const numero = parseFloat(valorStr);
      return isNaN(numero) ? null : numero;
    },

    parseBoolean(valor) {
      if (valor === null || valor === undefined || valor === '') return true;

      const valorStr = valor.toString().toLowerCase().trim();

      // Valores que representam "true"
      if (['1', 'sim', 's', 'yes', 'y', 'true', 'ativo', 'verdadeiro'].includes(valorStr)) {
        return true;
      }

      // Valores que representam "false"
      if (['0', 'não', 'nao', 'n', 'no', 'false', 'inativo', 'falso'].includes(valorStr)) {
        return false;
      }

      // Padrão: true
      return true;
    },

    parseUnidadeTempo(valor) {
      if (!valor) return 'minutos';

      const valorStr = valor.toString().toLowerCase().trim();

      // Normalizar unidades
      if (['min', 'minuto', 'minutos', 'minute', 'minutes'].includes(valorStr)) {
        return 'minutos';
      }
      if (['h', 'hr', 'hora', 'horas', 'hour', 'hours'].includes(valorStr)) {
        return 'horas';
      }
      if (['sessao', 'sessão', 'sessoes', 'sessões', 'session', 'sessions'].includes(valorStr)) {
        return 'sessões';
      }

      // Se não reconhecer, retornar o valor original ou padrão
      return valorStr || 'minutos';
    },

    voltarParaMapeamento() {
      this.etapa = 2;
      this.dadosMapeados = [];
    },

    async confirmarImportacao() {
      this.importando = true;

      try {
        // Importar em lote
        const response = await servicoProdutoService.importarLote(this.dadosMapeados);

        const sucesso = response.data?.data?.sucesso || 0;
        const erros = response.data?.data?.erros || 0;

        if (erros === 0) {
          cSwal.cSuccess(`${sucesso} itens importados com sucesso!`);
        } else {
          cSwal.cWarning(
            `Importação concluída com avisos:<br>` +
            `✓ ${sucesso} itens importados<br>` +
            `✗ ${erros} itens com erro`
          );
        }

        this.$emit('imported');
        this.fechar();
      } catch (error) {
        console.error('Erro ao importar:', error);
        cSwal.cError('Erro ao importar dados: ' + (error.response?.data?.message || error.message));
      } finally {
        this.importando = false;
      }
    },

    // Métodos auxiliares
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
    },

    formatCurrency(value) {
      if (!value) return '-';
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    },

    getTipoLabel(tipo) {
      const labels = {
        'produto': 'Produto',
        'procedimento': 'Procedimento'
      };
      return labels[tipo] || tipo;
    },

    getTipoBadgeClass(tipo) {
      const classes = {
        'produto': 'bg-warning',
        'procedimento': 'bg-primary'
      };
      return classes[tipo] || 'bg-secondary';
    },

    contarPorTipo(tipo) {
      return this.dadosMapeados.filter(item => item.tipo === tipo).length;
    },
  },
};
</script>

<style scoped>
/* Modal e Header */
.bg-gradient-primary {
  background: linear-gradient(87deg, #007bff 0, #0056b3 100%) !important;
}

.modal-title {
  color: #ffffff !important;
}

.modal-xl {
  max-width: 1400px;
  width: 90%;
  margin: 2rem auto;
}

.modal-content-full {
  height: 85vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 0.75rem 1rem;
  flex-shrink: 0;
}

.modal-body {
  padding: 1rem;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.modal-footer {
  padding: 0.75rem 1rem;
  flex-shrink: 0;
  border-top: 1px solid #dee2e6;
}

/* Upload Area */
.upload-area {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 1.25rem 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #007bff;
  background-color: #e7f1ff;
}

.upload-area.drag-over {
  border-color: #28a745;
  background-color: #d4edda;
}

.upload-content {
  width: 100%;
}

.upload-content i {
  font-size: 2.5rem;
}

.upload-content p {
  margin-bottom: 0.5rem;
}

/* Colunas Esperadas - Lista Simples */
.colunas-esperadas-container {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 0.75rem;
}

.colunas-lista {
  padding-left: 1.25rem;
  margin-bottom: 0;
  font-size: 0.85rem;
  overflow-y: auto;
}

.coluna-item {
  padding: 0.25rem 0;
  line-height: 1.5;
}

.campo-desc {
  color: #6c757d;
}

/* Mapeamento em 2 Tabelas */
.mapping-table-wrapper {
  overflow-y: auto;
  max-height: calc(85vh - 250px);
  border: 1px solid #dee2e6;
  border-radius: 8px;
}

.mapping-table-wrapper table {
  font-size: 0.85rem;
}

.mapping-table-wrapper thead th {
  background-color: #f8f9fa;
  font-weight: 600;
  font-size: 0.85rem;
  padding: 0.5rem;
  position: sticky;
  top: 0;
  z-index: 10;
}

.mapping-table-wrapper tbody td {
  padding: 0.4rem 0.5rem;
  vertical-align: middle;
}

.mapping-table-wrapper select {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
}

.mapping-table-wrapper .text-danger {
  font-weight: bold;
}

/* Alertas Compactos */
.compact-alert {
  padding: 0.4rem 0.75rem;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  flex-shrink: 0;
}

.compact-alert.alert-info {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: #ffffff;
}

.compact-alert.alert-success {
  background-color: #28a745;
  border-color: #28a745;
  color: #ffffff;
}

.compact-alert.alert-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: #ffffff;
}

.alert-white-text {
  color: #ffffff !important;
}

.alert-white-text strong,
.alert-white-text span,
.alert-white-text i {
  color: #ffffff !important;
}

/* Estatísticas Compactas */
.stats-compact {
  display: flex;
  gap: 0.75rem;
  padding: 0.5rem;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  margin-bottom: 0.5rem;
  flex-shrink: 0;
}

.stat-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.4rem 0.5rem;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.stat-item i {
  font-size: 1.1rem;
}

.stat-item .stat-value {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2c3e50;
}

.stat-item .stat-label {
  font-size: 0.7rem;
  color: #6c757d;
  white-space: nowrap;
}

/* Tabelas */
.mapping-section,
.preview-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.mapping-table-container {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  min-height: 0;
}

.preview-table-container {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  min-height: 0;
}

.compact-thead th {
  padding: 0.4rem 0.5rem;
  font-size: 0.8rem;
  background-color: #343a40 !important;
  color: white !important;
  border-color: #454d55 !important;
  font-weight: 600;
}

.table-sm td {
  padding: 0.3rem 0.5rem;
  font-size: 0.8rem;
  vertical-align: middle;
}

.table-sm th {
  padding: 0.4rem 0.5rem;
  font-size: 0.8rem;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 10;
}

.modal-xl {
  max-width: 1200px;
}

.btn-close-white {
  filter: brightness(0) invert(1);
}

/* Responsividade */
@media (max-width: 1200px) {
  .modal-xl {
    width: 95%;
    margin: 1.5rem auto;
  }
}

@media (max-width: 768px) {
  .modal-xl {
    width: 98%;
    margin: 1rem auto;
  }

  .modal-content-full {
    height: 90vh;
  }

  .stats-compact {
    flex-direction: column;
    gap: 0.5rem;
  }

  .stat-item {
    justify-content: center;
  }
}

@media (max-height: 700px) {
  .modal-content-full {
    height: 92vh;
  }

  /* .colunas-lista {
    max-height: 180px;
  } */
}
</style>
