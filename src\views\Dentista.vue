<template>
  <div class="page-width-container">
    <main class="page-width">
      <div class="container-fluid p-0">
        <div class="card no-shadow">
          <div class="card-body p-3">
            <div class="row gx-4">
              <div class="col-auto">
                <div class="avatar avatar-xl position-relative">
                  <input
                    id="profilePictureFileInput"
                    type="file"
                    accept="image/*"
                    @change="profilePicUpload"
                    hidden
                  />

                  <div class="profile-pic pointer" @click="confirmUpdatePhoto" title="Clique para alterar a foto de perfil">
                    <img
                        v-if="!isLoading.ortodontista && dentista.profile_picture_url"
                        :src="dentista.profile_picture_url"
                        alt="profile_image"
                        class="shadow-sm w-100 border-radius-lg"
                        @load="profilePictureLoaded"
                      />
                      <div v-else-if="isLoading.ortodontista" class="spinner-border text-primary" role="status"></div>
                      <div v-else class="shadow-sm border-radius-lg d-flex align-center justify-content-center px-2 pb-3"
                        style="border: 2px solid #deeaf2; font-size: 25pt;color: #5988A8;">
                        <v-icon class="d-none d-md-block">mdi-doctor</v-icon>
                      </div>
                  </div>
                </div>
              </div>
              <div class="col-auto my-auto">
                <div class="h-100">
                  <h5 class="mb-1 fs-4 dentist-name pointer"
                    :class="{'long-name': dentista.nome && dentista.nome.length > 35}"
                    @click="confirmChangeName"
                    title="Clique para alterar o nome">
                    {{ dentista.nome }}
                    <font-awesome-icon :icon="['fas', 'edit']" class="edit-icon-hover ms-1" style="font-size: 0.7em;" />
                  </h5>
                  <p class="mb-0 font-weight-bold">{{ dentista.clinica.nome_fantasia }}</p>
                </div>
              </div>
              <div class="mx-auto mt-3 col-md-6 my-sm-auto ms-sm-auto me-sm-0">
                <div class="nav-wrapper position-relative end-0">
                  <ul class="p-1 bg-transparent nav nav-pills nav-fill" role="tablist">
                    <li class="nav-item" @click="openTab('detalhes')">
                      <a class="px-0 py-1 mb-0 nav-link nav-tab active" data-bs-toggle="tab" href="javascript:;"
                        role="tab" aria-selected="true">
                        <i class="fas fa-list"></i>
                        <br>
                        <span class="ms-1">Detalhes</span>
                      </a>
                    </li>
                    <li class="nav-item" @click="openTab('pacientes')">
                      <a class="px-0 py-1 mb-0 nav-link nav-tab" data-bs-toggle="tab" href="javascript:;" role="tab"
                        aria-selected="false">
                        <i class="fas fa-user"></i>
                        <br>
                        <span class="ms-1">Pacientes</span>
                      </a>
                    </li>
                    <li class="nav-item" @click="openTab('consultas')">
                      <a class="px-0 py-1 mb-0 nav-link nav-tab" data-bs-toggle="tab" href="javascript:;" role="tab"
                        aria-selected="false">
                        <i class="fas fa-calendar-alt"></i>
                        <br>
                        <span class="ms-1">Consultas</span>
                      </a>
                    </li>
                    <!-- <li class="nav-item" @click="openTab('financeiro')">
                      <a class="px-0 py-1 mb-0 nav-link nav-tab" data-bs-toggle="tab" href="javascript:;" role="tab"
                        aria-selected="false">
                        <i class="fas fa-dollar-sign"></i>
                        <br>
                        <span class="ms-1">Financeiro</span>
                      </a>
                    </li> -->
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Transition>
        <div class="p-0 container-fluid" v-if="activeTab == 'detalhes'">
          <div class="row">
            <div class="col-md-12">
              <Transition>
                <div class="main-container" v-if="activeProfileTab == 'perfilPessoal'">
                  <div class="row p-3 pt-1 pb-4 w-100 mx-auto">
                    <div class="col-md-6 border-end pe-4">
                      <p class="text-uppercase text-sm mt-3" style="font-weight: 600">Informações pessoais</p>
                      <div class="row mt-4">
                        <div class="col-md-6 mb-2">
                          <label for="dentista_clinica" class="form-control-label">Clínica</label>
                          <select class="form-select" id="dentista_clinica" v-model="dentista.clinica.id">
                            <option hidden>Selecionar...</option>
                            <option v-for="clinica in clinicas" :key="clinica.id" :value="clinica.id">{{ clinica.nome }}</option>
                          </select>
                        </div>

                        <div class="col-sm-6 col-md-3 mb-2 text-center">
                          <MaterialInput readonly class="text-center" label="Pacientes" type="text"
                            :modelValue="dentista.pacientes_count" />
                        </div>

                        <div class="col-sm-6 col-md-3 mb-2 text-center">
                          <MaterialInput readonly class="text-center" label="Consultas" type="text"
                            :modelValue="dentista.consultas_count" />
                        </div>

                        <div class="col-md-6 mb-2">
                          <MaterialInput type="email" label="E-mail" v-model="dentista.user.email" id="dentista_rg" />
                        </div>
                        <div class="col-md-6 mb-2">
                          <MaterialInput label="Nova senha" type="password" v-model="dentista.novaSenha"
                            placeholder="********" id="dentista_cpf" />
                        </div>
                      </div>

                      <p class="text-uppercase text-sm mt-3" style="font-weight: 600"><label
                          for="dentista_observacoes">Observações</label></p>
                      <textarea class="form-control" id="dentista_observacoes" rows="5" v-model="dentista.observacoes">
                      </textarea>
                    </div>
                    <div class="col-md-6 ps-4">

                      <hr class="horizontal dark" />
                      <p class="text-uppercase text-sm mt-3 mb-2" style="font-weight: 600">Meios de
                        contato<font-awesome-icon :icon="['fas', 'edit']" class="ms-2 pointer"
                          title="Gerenciar meios de contato" @click="toggleEditMode('meiosContatos')" />
                        <span v-if="isEditing.meiosContatos" class="text-capitalize text-info pointer ms-2"
                          @click="toggleEditMode('meiosContatos')"><u>Cancelar edição</u></span>
                      </p>
                      <v-table style="font-size: 12pt;" class="contains-dropdown">
                        <thead>
                          <tr>
                            <th><label>Contato</label></th>
                            <th style="width: 50%;"><label>Descrição</label></th>
                            <th></th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="contato in dentista.contatos" v-bind:key="contato.id">
                            <td>
                              <a :href="getContatoHref(contato.tipo, contato.contato)" class="hoverable">
                                <span class="d-inline-block text-center" style="width: 30px;">

                                  <font-awesome-icon v-if="contato.tipo != 'telefone'"
                                    :icon="getContatoIcon(contato.tipo)" class="me-2"
                                    :class="{ 'text-success': contato.tipo == 'whatsapp', 'fs-14': contato.tipo == 'email', 'fs-15': contato.tipo != 'email' }" />

                                  <v-icon v-if="contato.tipo == 'telefone'" class="me-2" style="font-size: 17pt;">{{
                                    getContatoIcon(contato.tipo) }}</v-icon>

                                </span>
                                {{ contato.contato }}
                              </a>
                            </td>
                            <td>{{ contato.descricao }}</td>
                            <td>
                              <button v-if="isEditing.meiosContatos" class="btn btn-vsm btn-sm btn-danger"
                                @click="excluirContato(contato.id, contato.tipo)">
                                <font-awesome-icon :icon="['fas', 'trash']" />
                              </button>
                            </td>
                          </tr>
                          <tr>
                            <td style="vertical-align: middle;">
                              <div class="d-flex flex-row align-center">
                                <div class="dropdown text-center dropup">
                                  <span data-bs-toggle="dropdown" class="pointer dropdown-toggle">
                                    <font-awesome-icon v-if="novoContato.tipo == 'email'" :icon="['fas', 'envelope']"
                                      style="font-size: 15pt; margin-right: 3px;" />
                                    <v-icon v-if="novoContato.tipo == 'telefone'"
                                      style="font-size: 17pt;">mdi-phone</v-icon>
                                    <font-awesome-icon v-if="novoContato.tipo == 'celular'"
                                      :icon="['fas', 'mobile-screen-button']"
                                      style="font-size: 15pt; margin-right: 3px;" />
                                    <font-awesome-icon v-if="novoContato.tipo == 'whatsapp'" :icon="['fab', 'whatsapp']"
                                      class="text-success" style="font-size: 15pt;" />
                                  </span>
                                  <ul class="dropdown-menu dropdown-menu-icons hidden">
                                    <li title="E-mail" @click="selectMeioContato('email')">
                                      <a class="dropdown-item dropdown-item-sm" href="#">
                                        <font-awesome-icon icon="fa-solid fa-envelope" style="font-size: 14pt;" />
                                      </a>
                                    </li>
                                    <li title="Telefone" @click="selectMeioContato('telefone')">
                                      <a class="dropdown-item dropdown-item-sm" href="#">
                                        <v-icon style="font-size: 17pt;">mdi-phone</v-icon>
                                      </a>
                                    </li>
                                    <li title="Celular" @click="selectMeioContato('celular')">
                                      <a class="dropdown-item dropdown-item-sm" href="#">
                                        <font-awesome-icon :icon="['fas', 'mobile-screen-button']"
                                          style="font-size: 15pt; margin-right: 3px;" />
                                      </a>
                                    </li>
                                    <li title="WhatsApp" @click="selectMeioContato('whatsapp')">
                                      <a class="dropdown-item" href="#">
                                        <font-awesome-icon :icon="['fab', 'whatsapp']" class="text-success"
                                          style="font-size: 15pt;" />
                                      </a>
                                    </li>
                                  </ul>
                                </div>

                                <MaterialInput type="text" class="form-control input-sm"
                                  :placeholder="getContatoPlaceholder"
                                  style="display: inline-block; width: calc(100% - 30px);" v-model="novoContato.contato"
                                  ref="contatoInput" :mask="novoContatoMask" :input="contatoChange" />

                              </div>
                            </td>
                            <td style="vertical-align: middle; padding-top: 5px;">

                              <MaterialInput type="text" class="form-control input-sm" placeholder="Descrição"
                                style="display: inline; width: calc(100% - 51px);" ref="contatoDescricaoInput"
                                v-model="novoContato.descricao" />
                              <button class="btn btn-sm btn-primary mt-2" style="width: 46px; margin-left: 5px;"
                                @click="adicionarContato">
                                <font-awesome-icon :icon="['fas', 'plus']" />
                              </button>

                            </td>
                            <td></td>
                          </tr>
                        </tbody>
                      </v-table>

                      <div class="p-horizontal-divider"></div>

                      <p class="text-uppercase text-sm mt-3" style="font-weight: 600">Endereço</p>
                      <div class="row">
                        <div class="col-md-4 mb-2">
                          <MaterialInput label="CEP" type="text" v-model="dentista.endereco_cep" :input="getEndereco"
                            mask="#####-###" id="dentista_enderecoCep" />
                        </div>
                        <div class="col-md-6 mb-2">
                          <MaterialInput label="Logradouro" type="text" v-model="dentista.endereco_logradouro"
                            id="dentista_enderecoLogradouro" />
                        </div>
                        <div class="col-md-2 mb-2">
                          <MaterialInput label="Nº" type="text" v-model="dentista.endereco_numero"
                            id="dentista_enderecoNumero" ref="endereco_numero" />
                        </div>
                        <div class="col-md-4">
                          <MaterialInput label="Complemento" type="text" v-model="dentista.endereco_complemento"
                            id="dentista_enderecoComplemento" />
                        </div>
                        <div class="col-md-4">
                          <MaterialInput label="Cidade" type="text" v-model="dentista.endereco_cidade"
                            id="dentista_enderecoCidade" />
                        </div>
                        <div class="col-md-4">
                          <MaterialInput label="Estado" type="text" v-model="dentista.endereco_estado"
                            id="dentista_enderecoEstado" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <Transition name="fadeHeight">
                    <div v-cloak v-if="hasPendingChanges" class="row col-12">
                      <div class="p-horizontal-divider my-0"></div>
                      <div class="w-100 py-3 text-center">
                        <button class="btn btn btn-primary m-0" @click="confirmSaveDentista">
                          Salvar alterações
                        </button>
                      </div>
                    </div>
                  </Transition>

                  <!-- Botão de exclusão centralizado -->
                  <div class="text-center">
                    <button
                      class="delete-dentist-btn"
                      @click="confirmarExcluirDentista"
                      title="Excluir profissional"
                    >
                      <font-awesome-icon :icon="['fas', 'trash-alt']" />
                    </button>
                  </div>

                </div>
              </Transition>
              <Transition>
                <div v-if="activeProfileTab == 'perfilClinico'">
                </div>
              </Transition>
            </div>
            <!-- <div class="col-md-4">
          <profile-card />
        </div> -->
          </div>
        </div>
      </Transition>

      <Transition>
        <div class="py-4 container-fluid" v-if="activeTab == 'pacientes'">
          <div class="row">
            <div class="col-12">
              <div class="card">
                <div class="card-header pb-0">
                  <div class="d-flex align-items-center justify-content-between">
                    <h6>Pacientes do Profissional</h6>
                    <span class="badge badge-sm bg-gradient-info">{{ pacientes.length }} paciente(s)</span>
                  </div>
                </div>
                <div class="card-body px-0 pt-0 pb-2">
                  <div v-if="isLoading.pacientes" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Carregando...</span>
                    </div>
                  </div>
                  <div v-else-if="pacientes.length === 0" class="text-center py-4">
                    <p class="text-muted mb-0">Este ortodontista ainda não possui pacientes cadastrados.</p>
                  </div>
                  <div v-else class="table-responsive p-0">
                    <table class="table align-items-center mb-0">
                      <thead>
                        <tr>
                          <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Paciente</th>
                          <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Ficha</th>
                          <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Contato</th>
                          <th class="text-secondary opacity-7"></th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="paciente in pacientes" :key="paciente.id">
                          <td>
                            <div class="d-flex px-2 py-1">
                              <div class="d-flex flex-column justify-content-center">
                                <h6 class="mb-0 text-sm">{{ paciente.nome }}</h6>
                              </div>
                            </div>
                          </td>
                          <td>
                            <p class="text-xs font-weight-bold mb-0">{{ paciente.id_ficha }}</p>
                          </td>
                          <td class="align-middle text-center text-sm">
                            <span v-if="paciente.contatos && paciente.contatos.length > 0" class="text-xs">
                              {{ paciente.contatos[0].contato }}
                            </span>
                            <span v-else class="text-xs text-muted">-</span>
                          </td>
                          <td class="align-middle">
                            <button
                              class="btn btn-link text-secondary mb-0"
                              @click="abrirPaciente(paciente)"
                              title="Ver prontuário"
                            >
                              <i class="fas fa-external-link-alt text-xs"></i>
                            </button>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Transition>

      <Transition>
        <div class="py-4 container-fluid" v-if="activeTab == 'consultas'">

          <div class="row">
            <div class="col-sm-6 col-md-3 text-center">
              <MaterialInput label="Consultas realizadas" readonly centered type="text"
                :modelValue="dentista.consultas_realizadas ? dentista.consultas_realizadas : 0"
                id="dentista_proximaConsulta" />
            </div>
            <div class="col-sm-6 col-md-3 text-center">
              <MaterialInput label="Primeira consulta" readonly centered type="text"
                :modelValue="$filters.dateDmy(dentista.primeira_consulta)" id="dentista_primeiraConsulta" />
              <span>
                {{ $filters.howMuchTime(dentista.primeira_consulta) }}
              </span>
            </div>
            <div class="col-sm-6 col-md-3 text-center">
              <MaterialInput label="Última consulta" readonly centered type="text"
                :modelValue="$filters.dateDmy(dentista.ultima_consulta)" id="dentista_ultimaConsulta" />
              <span>
                {{ $filters.howMuchTime(dentista.ultima_consulta) }}
              </span>
            </div>
            <div class="col-sm-6 col-md-3 text-center">
              <MaterialInput label="Próxima consulta" readonly centered type="text"
                :modelValue="$filters.dateDmy(dentista.proxima_consulta)" id="dentista_proximaConsulta" />
              <span class="text-success" style="font-weight: 500;">
                {{ $filters.howMuchTime(dentista.proxima_consulta) }}
              </span>
            </div>
          </div>

          <div class="p-horizontal-divider"></div>

          <div class="row">
            <div class="col-md-12">
              <v-table>
                <tbody>
                  <tr>
                    <td class="bg-gradient-light text-dark text-center" style="border-radius: 3px; padding: 2px 20px;">
                      Ainda não foram agendadas consultas.
                    </td>
                  </tr>
                </tbody>
              </v-table>
            </div>
            <!-- <div class="col-md-4">
          <profile-card />
        </div> -->
          </div>
        </div>
      </Transition>

      <Transition>
        <div class="py-4 container-fluid" v-if="activeTab == 'financeiro'">
          <div class="row">
            <div class="col-md-12">
              <v-table>
                <tbody>
                  <tr>
                    <td class="bg-gradient-success text-light text-center"
                      style="border-radius: 3px; padding: 2px 20px;">
                      Não há pendências financeiras.
                    </td>
                  </tr>
                </tbody>
              </v-table>
            </div>
            <!-- <div class="col-md-4">
          <profile-card />
        </div> -->
          </div>
        </div>
      </Transition>
    </main>
  </div>
</template>

<script>
import { phoneMask } from "@/helpers/utils.js";
import setNavPills from "@/assets/js/nav-pills.js";
import setTooltip from "@/assets/js/tooltip.js";
// import ProfileCard from "./components/ProfileCard.vue";
import MaterialInput from "@/components/MaterialInput.vue";
import MaterialButton from "@/components/MaterialButton.vue";
import Planejamento from "@/views/Planejamento.vue"
import { getEnderecoByCep } from "@/services/commonService"
import { getDentista, updateDentista, adicionarMeioContato, excluirMeioContato, excluirDentista, uploadImageDentista } from "@/services/dentistasService"
import { getClinicas } from "@/services/clinicasService"
import { getClinicaPacientes } from "@/services/pacientesService"
import cSwal from "@/utils/cSwal.js"

var isEditing = []

var dentista = {
  pacientes_count: '-',
  consultas_count: '-',
  user: {
    email: '',
  },
  clinica: {
    nome_fantasia: '',
  },
}

var originalDentista = {
  pacientes_count: '-',
  consultas_count: '-',
  user: {
    email: '',
  },
  clinica: {
    nome_fantasia: '',
  },
}

var showTratamento = false;

var activeProfileTab = 'perfilPessoal';

var activeTab = 'detalhes'

let clinicaSlug
let ortodontistaIdMatricula

export default {
  name: "dentista",
  components: {
    // ProfileCard,
    MaterialInput,
    MaterialButton,
    Planejamento,
  },
  data() {
    return {
      isLoading: {
        ortodontista: true,
        profilePicture: true,
        pacientes: false,
      },
      clinicas: [],
      pacientes: [],
      isEditing,
      novoContato: {
        tipo: 'whatsapp',
        contato: '',
        descricao: '',
      },
      showMenu: false,
      dentista,
      originalDentista,
      showTratamento,
      activeTab,
      activeProfileTab
    };
  },
  computed: {
    getContatoPlaceholder() {
      var placeholder = null;
      switch (this.novoContato.tipo) {
        case 'whatsapp':
          placeholder = 'WhatsApp';
          break;
        case 'celular':
          placeholder = 'Celular';
          break;
        case 'telefone':
          placeholder = 'Telefone';
          break;
        case 'email':
          placeholder = 'E-mail'
          break;
      }

      return placeholder;
    },

    novoContatoMask() {
      return [
        'telefone', 'celular', 'whatsapp'
      ].includes(this.novoContato.tipo) ? phoneMask(this.novoContato.contato) : ''
    },
    possuiWhatsapp() {
      return this.dentista && this.dentista.contatos && this.dentista.contatos.some(contato => contato.tipo === 'whatsapp');
    },
    whatsappNumero() {
      if (this.possuiWhatsapp) {
        const whatsappContato = this.dentista.contatos.find(contato => contato.tipo === 'whatsapp');
        return whatsappContato.contato;
      } else {
        return null;
      }
    },
    detalhesPessoais() {
      return this.dentista.detalhes_dentista ? this.dentista.detalhes_dentista.filter(detalhe => detalhe.tipo == 'pessoal') : [];
    },
    hasPendingChanges() {
      return this.originalDentista && this.dentista && JSON.stringify(this.originalDentista) !== JSON.stringify(this.dentista)
    }
  },
  watch: {
    dentista: {
      handler() {
        for (const propriedade in this.dentista)
          if (this.dentista[propriedade] === '')
            this.dentista[propriedade] = null
      },
      deep: true // Observação profunda de alterações aninhadas
    },
    activeTab(newTab) {
      // Carregar pacientes quando a aba de pacientes for aberta
      if (newTab === 'pacientes' && this.pacientes.length === 0) {
        this.refreshPacientes();
      }
    }
  },
  methods: {

    profilePictureLoaded() {
      this.isLoading.profilePicture = false;
    },

    confirmChangeName() {
      cSwal.cConfirm("Deseja alterar o nome do profissional?", () => {
        this.changeName();
      });
    },

    changeName() {
      cSwal.fire({
        title: 'Alterar nome do profissional',
        input: 'text',
        inputValue: this.dentista.nome,
        inputLabel: 'Novo nome:',
        inputPlaceholder: 'Digite o novo nome',
        showCancelButton: true,
        confirmButtonText: 'Salvar',
        cancelButtonText: 'Cancelar',
        customClass: {
          input: 'text-center',
          popup: 'swal-wider-popup'
        },
        inputValidator: (value) => {
          if (!value || value.trim() === '') {
            return 'O nome não pode estar vazio';
          }
        },
        didOpen: () => {
          // Adicionar estilo para centralizar o texto do input
          const style = document.createElement('style');
          style.textContent = `
            .swal-wider-popup {
              width: 600px !important;
              max-width: 90vw !important;
            }
            .swal2-input {
              text-align: center !important;
            }
          `;
          document.head.appendChild(style);
        }
      }).then(async (result) => {
        if (result.isConfirmed) {
          const newName = result.value.trim();
          if (newName !== this.dentista.nome) {
            cSwal.loading("Atualizando nome do profissional...");

            // Aplicar capitalização ao nome (primeira letra de cada palavra em maiúscula)
            const capitalizedName = this.capitalizeAllStr(newName);

            // Criar uma cópia do profissional para atualização
            const dentistaToUpdate = { ...this.dentista };
            dentistaToUpdate.nome = capitalizedName;

            const update = await updateDentista(dentistaToUpdate);

            if (update) {
              // Atualizar o nome localmente com a versão capitalizada
              this.dentista.nome = capitalizedName;
              this.originalDentista.nome = capitalizedName;

              cSwal.loaded();
              cSwal.cSuccess("Nome do profissional atualizado com sucesso.");
            } else {
              cSwal.loaded();
              cSwal.cError("Ocorreu um erro ao atualizar o nome do profissional.");
            }
          }
        }
      });
    },

    confirmUpdatePhoto() {
      cSwal.cConfirm("Deseja atualizar a foto de perfil?", () => {
        this.chooseProfilePictureFile();
      });
    },

    chooseProfilePictureFile() {
      document.getElementById("profilePictureFileInput").value = "";
      document.getElementById("profilePictureFileInput").click();
    },

    profilePicUpload(e) {
      cSwal.loading("Atualizando imagem de perfil...");

      const imagem = e.target.files[0];
      const reader = new FileReader();
      reader.readAsDataURL(imagem);
      reader.onload = async () => {
        const imgData = {
          dentista_id: this.dentista.id,
          imagem,
          dir: "profile_pic_dentista",
          data: new Date().toISOString().split('T')[0], // Data atual no formato YYYY-MM-DD
          descricao: 'Foto de perfil'
        };
        const upload = await uploadImageDentista(imgData);

        if (upload) {
          await this.refreshDentista();
          cSwal.loaded();
          cSwal.cSuccess("A foto de perfil do profissional foi atualizada.");
        } else {
          cSwal.loaded();
          cSwal.cError("Ocorreu um erro ao atualizar a foto de perfil do profissional.");
        }
      };
    },

    capitalizeAllStr(str) {
      return str.replace(/\b\w/g, (l) => l.toUpperCase());
    },

    confirmarExcluirDentista() {
      cSwal.cConfirm(
        "Tem certeza que deseja excluir este profissional? Esta ação não pode ser desfeita.",
        () => this.excluirDentista(),
        {
          title: "Excluir profissional",
          icon: "warning",
          confirmButtonText: "Sim, excluir",
          cancelButtonText: "Cancelar",
        }
      );
    },

    async excluirDentista() {
      cSwal.loading("Excluindo profissional...");

      try {
        const resultado = await excluirDentista(this.dentista.id);

        if (resultado) {
          cSwal.loaded();
          cSwal.cSuccess("Profissional excluído com sucesso.");
          // Redirecionar para a lista de ortodontistas
          this.$router.push({ name: "Profissionals" });
        } else {
          cSwal.loaded();
          cSwal.cError("Não foi possível excluir o profissional. Tente novamente.");
        }
      } catch (error) {
        console.error("Erro ao excluir profissional:", error);
        cSwal.loaded();
        cSwal.cError("Ocorreu um erro ao excluir o profissional.");
      }
    },

    contatoChange() {
      if (this.novoContato.tipo == 'celular' || this.novoContato.tipo == 'whatsapp') {
        if (this.novoContato.contato.length > 14) {
          this.$refs.contatoDescricaoInput.getInput().focus();
        }
      }
    },

    toggleEditMode(section) {
      this.isEditing[section] = !this.isEditing[section];
    },
    clearNovoContato() {
      this.novoContato.contato = ''
      this.novoContato.descricao = ''
    },
    getContatoHref(tipo, contato) {
      switch (tipo) {
        case 'email':
          return `mailto:${contato}`;
        case 'whatsapp':
          return `https://wa.me/55${contato.replace(/\D+/g, '')}`;
        case 'telefone':
        case 'celular':
          return `tel:${contato.replace(/\D+/g, '')}`;
        default:
          return '#';
      }
    },
    async adicionarContato() {
      cSwal.loading('Adicionando contato...')
      const add = await adicionarMeioContato(this.dentista.id, this.novoContato);

      if (add) {
        await this.refreshDentista({ onlyContatos: true })
        cSwal.loaded()
        this.clearNovoContato()
      }
      else {
        cSwal.loaded()
        cSwal.cError('Ocorreu um erro ao salvar o contato.')
      }

    },

    excluirContato(id, tipo) {
      if (tipo == 'whatsapp')
        tipo = 'WhatsApp'
      else if (tipo == 'email')
        tipo = 'e-mail'

      cSwal.cConfirm('Deseja realmente excluir este ' + tipo + '?', async () => {
        cSwal.loading('Excluindo contato...')
        const del = await excluirMeioContato(id)
        if (del) {
          await this.refreshDentista({ onlyContatos: true })
          cSwal.loaded()
        }
        else {
          cSwal.loaded()
          cSwal.cError('Ocorreu um erro ao excluir o meio de contato')
        }
      })
    },

    selectMeioContato(tipo) {
      this.novoContato.tipo = tipo
      this.$refs.contatoInput.getInput().focus()
    },
    async refreshDentista(options) {
      await this.getDentistaDetails(this.dentista.id_matricula, clinicaSlug, options)
    },
    confirmSaveDentista() {
      cSwal.cConfirm('Deseja realmente salvar as alterações?', async () => {
        const update = await updateDentista(this.dentista)

        if (update) {
          cSwal.cSuccess('As alterações foram salvas.')
          await this.refreshDentista()
        }
        else {
          cSwal.cError('Ocorreu um erro ao salvar as alterações.')
        }
      })
    },

    validarCep(cep) {
      return /^\d{8}$/.test(cep.replace(/[^\d]+/g, ""))
    },

    zipCodeMask(value) {
      if (!value) return ""
      value = value.replace(/\D/g, '')
      value = value.replace(/(\d{5})(\d)/, '$1-$2')
      return value
    },

    async getEndereco(event) {
      clearTimeout(this.timeout);
      this.timeout = setTimeout(async () => {
        var cep = event.target.value
        cep = this.dentista.endereco_cep

        if (!this.validarCep(cep))
          return false

        const enderecoInfo = await getEnderecoByCep(cep)
        if (!enderecoInfo)
          return false

        this.dentista.endereco_logradouro = enderecoInfo.street
        this.dentista.endereco_cidade = enderecoInfo.city
        this.dentista.endereco_estado = enderecoInfo.state

        if (!this.dentista.endereco_numero)
          this.$refs.endereco_numero.getInput().focus();
      }, 50);
    },

    getContatoIcon(type) {
      var icon = null;
      switch (type) {
        case 'whatsapp':
          icon = ['fab', 'whatsapp'];
          break;
        case 'celular':
          icon = ['fas', 'mobile-screen-button'];
          break;
        case 'telefone':
          icon = 'mdi-phone';
          break;
        case 'email':
          icon = ['fas', 'envelope'];
          break;
      }

      return icon;
    },

    getInfoIcon(nivel) {
      var icon = null
      switch (nivel) {
        case 'positivo':
          icon = 'thumbs-up'
          break
        case 'neutro':
          icon = 'info-circle'
          break
        case 'atencao':
          icon = 'circle-exclamation'
          break
        case 'negativo':
          icon = 'thumbs-down'
          break
      }

      return icon
    },

    setProfileTab(tab) {
      this.activeProfileTab = tab
    },

    openTab(tab) {
      this.activeTab = tab;
    },

    async getDentistaDetails(idMatricula, clinicaSlug, options) {
      this.isLoading.ortodontista = true;

      options = {
        onlyContatos: false,
        ...options
      }
      const dentista = await getDentista(idMatricula, clinicaSlug)
      if (dentista && !options.onlyContatos) {
        this.dentista = JSON.parse(JSON.stringify(dentista))
        this.originalDentista = JSON.parse(JSON.stringify(dentista))
      }
      else if (dentista && options.onlyContatos) {
        this.dentista.contatos = dentista.contatos
        this.originalDentista = {
          ...this.originalDentista,
          contatos: dentista.contatos,
        }
      }

      else if (idMatricula) {
          cSwal.cError("Ocorreu um erro ao tentar carregar os dados do profissional.");
      }

      this.isLoading.ortodontista = false;
    },

    async refreshPacientes() {
      if (!this.dentista?.clinica?.slug) {
        console.warn('Clínica slug não disponível para buscar pacientes');
        return;
      }

      this.isLoading.pacientes = true;
      try {
        const pacientes = await getClinicaPacientes(this.dentista.clinica.slug);
        // Filtrar apenas os pacientes deste profissional
        this.pacientes = pacientes.filter(p => p.dentista_id === this.dentista.id);
      } catch (error) {
        console.error('Erro ao buscar pacientes:', error);
        this.pacientes = [];
      } finally {
        this.isLoading.pacientes = false;
      }
    },

    abrirPaciente(paciente) {
      if (!paciente?.clinica?.slug || !paciente?.id_ficha) {
        console.error('Dados do paciente incompletos:', paciente);
        return;
      }

      // Verificar se o paciente é de uma clínica diferente da que está logada
      const userClinica = this.$store?.state?.token?.clinica;
      const pacienteClinica = paciente.clinica;

      if (userClinica && pacienteClinica && userClinica.id !== pacienteClinica.id) {
        // Usar rota com slug da clínica se for de clínica diferente
        this.$router.push({
          name: "PacienteClinica",
          params: {
            clinica_slug: pacienteClinica.slug,
            id_ficha: paciente.id_ficha,
          },
        });
      } else {
        // Usar rota padrão se for da mesma clínica
        this.$router.push({
          name: "PacientePadrao",
          params: {
            id_ficha: paciente.id_ficha,
          },
        });
      }
    },
  },

  async created() {
    clinicaSlug = this.$route.params.clinica_slug || null;
    ortodontistaIdMatricula = this.$route.params.id_matricula;
    this.clinicas = await getClinicas()
    await this.getDentistaDetails(ortodontistaIdMatricula, clinicaSlug);
  },

  async mounted() {
    this.$store.state.isAbsolute = true;
    setNavPills();
    setTooltip();
  },

  async beforeMount() {
  },

  beforeUnmount() {
  }
};
</script>

<style scoped>
.profile-pic {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
}

.profile-pic::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  border-radius: 8px;
}

.profile-pic:hover::before {
  opacity: 1;
}

.profile-pic::after {
  content: '📷';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 1.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2;
}

.profile-pic:hover::after {
  opacity: 1;
}

.dentist-name {
  transition: color 0.2s ease;
  cursor: pointer;
}

.dentist-name:hover {
  color: #3a8bd6;
}

.dentist-name .edit-icon-hover {
  transition: all 0.2s ease;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-5px);
  color: inherit;
}

.dentist-name:hover .edit-icon-hover {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
  color: #3a8bd6;
}

.long-name {
  font-size: 1.1rem !important;
}

/* Estilos para o botão de exclusão de ortodontista */
.delete-dentist-btn {
  width: 42px;
  height: 42px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #6c757d;
  transition: all 0.3s ease;
  padding: 0;
  margin: 1.5rem auto 0;
  font-size: 1rem;
}

.delete-dentist-btn:hover {
  background-color: #dc3545;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
}

.pointer {
  cursor: pointer;
}
</style>
