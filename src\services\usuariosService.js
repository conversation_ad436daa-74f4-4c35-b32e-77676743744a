import axios from '@/services/axios'
import router from "@/router/index";
import { jwtDecode } from 'jwt-decode';
import store from '@/store';

function isAuthenticated() {
    return localStorage.getItem('isAuthenticated', 'false') === 'true';
}

function isSystemAdmin() {
    const decoded = decodedToken();
    if (!decoded) return false;

    return typeof decoded.system_admin === 'boolean' ? decoded.system_admin : decoded.system_admin === 1;
}

function getClinica() {
    const decoded = decodedToken();
    if (!decoded) return null;
    return decoded.clinica;
}

function getAuthToken() {
    return localStorage.getItem('token');
}

function decodedToken() {
    const token = getAuthToken();
    if (!token) return null;

    try {
        const decoded = jwtDecode(token);
        return decoded;
    } catch (error) {
        console.error('Erro ao decodificar token:', error);
        return null;
    }
}

async function login(credentials) {
    try {
        const { username, password } = credentials

        const response = await axios.post('/auth/login', {
            username, password
        });

        if (!response || !response.data || !response.data.access_token)
            return false

        const data = response.data

        axios.refreshToken(data.access_token)
        localStorage.setItem('isAuthenticated', 'true');

        // Decodificar token e atualizar Vuex store
        const decodedToken = jwtDecode(data.access_token);
        store.commit('setToken', decodedToken);

        // Carregar configurações da agenda após login bem-sucedido
        await loadAgendaConfigAfterLogin();

        return true

    } catch (error) {
        console.error('Erro ao realizar login:', error);
    }

    return false
}

async function logout(callback = null) {

    try {
        await axios.post('/auth/logout')
        if (callback) callback()

        // Limpar localStorage
        localStorage.clear()

        // Limpar header do axios
        delete axios.defaults.headers.common['Authorization']

        // Limpar Vuex store
        store.commit('setToken', null)

        router.push('/entrar')

        return true

    } catch (error) {
        console.error('Erro ao realizar logout:', error);

        // Mesmo com erro, limpar dados locais
        localStorage.clear()
        delete axios.defaults.headers.common['Authorization']
        store.commit('setToken', null)
        router.push('/entrar')

        return false
    }

}

async function refreshAuth(options = {}) {
    // Se não está autenticado, retorna null imediatamente
    if (!isAuthenticated()) {
        return null;
    }

    // Verificar se o token existe e é válido
    const token = decodedToken();
    if (!token) {
        // Token inválido, limpar autenticação
        localStorage.removeItem('isAuthenticated');
        localStorage.removeItem('token');
        return false;
    }

    // Se não é forçado e o token ainda é válido, retorna true
    if (!options?.force && token.exp > Math.floor(Date.now() / 1000)) {
        return true;
    }

    try {
        const response = await axios.post('/auth/refresh');

        if (!response || !response.data || !response.data.access_token) {
            // Falha no refresh, limpar autenticação
            localStorage.removeItem('isAuthenticated');
            localStorage.removeItem('token');
            return false;
        }

        const data = response.data;
        axios.refreshToken(data.access_token);

        // Carregar configurações da agenda após refresh do token
        await loadAgendaConfigAfterLogin();

        return true;

    } catch (error) {
        console.error('Erro ao realizar refresh do token:', error);
        // Em caso de erro, limpar autenticação
        localStorage.removeItem('isAuthenticated');
        localStorage.removeItem('token');
        return false;
    }
}

async function updateProfile(userData) {
    try {
        const response = await axios.patch('/profile', userData);

        if (!response || !response.data || response.data.status === 'error')
            return false;

        return response.data;

    } catch (error) {
        console.error('Erro ao atualizar perfil:', error);
        return false;
    }
}

async function loadAgendaConfigAfterLogin() {
    try {
        // Decodificar o token para obter as configurações da agenda
        const token = decodedToken();

        // Importar o store dinamicamente para evitar dependência circular
        const { default: store } = await import('@/store');

        if (token && token.agenda_config) {
            // Carregar as configurações no store
            await store.dispatch('agendaConfig/loadAgendaConfig', token.agenda_config);

        // Carregar TODAS as configurações de agenda de uma vez (bulk load)
        console.log('📦 Fazendo bulk load de todas as configurações de agenda...');
        const bulkSuccess = await store.dispatch('agendaConfig/loadAllAgendaConfigsBulk');

        if (bulkSuccess) {
            console.log('✅ Todas as configurações de agenda carregadas com sucesso via bulk load');

            // Salvar no localStorage para persistência
            const configsByConsultorio = store.state.agendaConfig.configsByConsultorio;
            localStorage.setItem('agendaConfigs', JSON.stringify(configsByConsultorio));
            localStorage.setItem('agendaConfigsTimestamp', Date.now().toString());

            return true;
        } else {
            console.warn('⚠️ Bulk load falhou, tentando carregar do token JWT...');

            // Fallback: tentar carregar do token JWT
            const token = decodedToken();
            if (token && token.agenda_config) {
                // Primeiro, garantir que há um consultório selecionado
                let selectedConsultorioId = store.getters['agendaConfig/selectedConsultorioId'];
                if (!selectedConsultorioId) {
                    // Se não há consultório selecionado, usar o primeiro disponível ou ID padrão
                    const consultorios = store.getters['agendaConfig/consultorios'];
                    if (consultorios && consultorios.length > 0) {
                        selectedConsultorioId = consultorios[0].id;
                    } else {
                        selectedConsultorioId = 1; // ID padrão
                    }
                    store.commit('agendaConfig/SET_SELECTED_CONSULTORIO', selectedConsultorioId);
                }

                // Carregar as configurações no store para o consultório
                store.commit('agendaConfig/SET_AGENDA_CONFIG_FOR_CONSULTORIO', {
                    consultorioId: selectedConsultorioId,
                    config: token.agenda_config
                });

                // Gerar os time slots para o consultório
                store.dispatch('agendaConfig/generateTimeSlotsForConsultorio', selectedConsultorioId);

                console.log('✅ Configurações da agenda carregadas do token para consultório:', selectedConsultorioId);
                return true;
            }

            console.warn('⚠️ Nenhuma configuração disponível no token JWT');
            return false;
        }
    }
} catch (error) {
        console.error('❌ Erro ao carregar configurações da agenda:', error);
        return false;
    }
}

// ============================================
// GERENCIAMENTO DE USUÁRIOS DA CLÍNICA
// ============================================

/**
 * Listar usuários da clínica
 * @param {number} clinicaId - ID da clínica
 * @param {object} filters - Filtros opcionais (search, page, per_page)
 * @returns {Promise}
 */
async function getClinicaUsuarios(clinicaId, filters = {}) {
    try {
        const params = new URLSearchParams();
        if (filters.search) params.append('search', filters.search);
        if (filters.page) params.append('page', filters.page);
        if (filters.per_page) params.append('per_page', filters.per_page);

        const response = await axios.get(`/clinicas/${clinicaId}/usuarios?${params.toString()}`);
        return response.data;
    } catch (error) {
        console.error('Erro ao listar usuários da clínica:', error);
        throw error;
    }
}

/**
 * Obter detalhes de um usuário
 * @param {number} clinicaId - ID da clínica
 * @param {number} usuarioId - ID do usuário
 * @returns {Promise}
 */
async function getClinicaUsuario(clinicaId, usuarioId) {
    try {
        const response = await axios.get(`/clinicas/${clinicaId}/usuarios/${usuarioId}`);
        return response.data;
    } catch (error) {
        console.error('Erro ao obter usuário:', error);
        throw error;
    }
}

/**
 * Criar novo usuário na clínica
 * @param {number} clinicaId - ID da clínica
 * @param {object} userData - Dados do usuário (name, username, email, password, clinica_admin)
 * @returns {Promise}
 */
async function createClinicaUsuario(clinicaId, userData) {
    try {
        const response = await axios.post(`/clinicas/${clinicaId}/usuarios`, userData);
        return response.data;
    } catch (error) {
        console.error('Erro ao criar usuário:', error);
        throw error;
    }
}

/**
 * Atualizar usuário da clínica
 * @param {number} clinicaId - ID da clínica
 * @param {number} usuarioId - ID do usuário
 * @param {object} userData - Dados a atualizar
 * @returns {Promise}
 */
async function updateClinicaUsuario(clinicaId, usuarioId, userData) {
    try {
        const response = await axios.put(`/clinicas/${clinicaId}/usuarios/${usuarioId}`, userData);
        return response.data;
    } catch (error) {
        console.error('Erro ao atualizar usuário:', error);
        throw error;
    }
}

/**
 * Desativar/deletar usuário da clínica (soft delete)
 * @param {number} clinicaId - ID da clínica
 * @param {number} usuarioId - ID do usuário
 * @returns {Promise}
 */
async function deleteClinicaUsuario(clinicaId, usuarioId) {
    try {
        const response = await axios.delete(`/clinicas/${clinicaId}/usuarios/${usuarioId}`);
        return response.data;
    } catch (error) {
        console.error('Erro ao desativar usuário:', error);
        throw error;
    }
}

/**
 * Verificar se o usuário atual é admin da clínica
 * @returns {boolean}
 */
function isClinicaAdmin() {
    const decoded = decodedToken();
    if (!decoded) return false;
    const result = typeof decoded.clinica_admin === 'boolean' ? decoded.clinica_admin : decoded.clinica_admin === 1;
    console.log('[isClinicaAdmin] decoded.clinica_admin:', decoded.clinica_admin, 'result:', result);
    return result;
}

/**
 * Verificar se a clínica do usuário tem módulo de ortodontia ativo
 * @returns {boolean}
 */
function hasModuloOrtodontia() {
    const decoded = decodedToken();
    if (!decoded) return false;
    return typeof decoded.modulo_ortodontia === 'boolean' ? decoded.modulo_ortodontia : false;
}

export default {
    login,
    logout,
    decodedToken,
    getAuthToken,
    isSystemAdmin,
    isClinicaAdmin,
    hasModuloOrtodontia,
    getClinica,
    isAuthenticated,
    refreshAuth,
    updateProfile,
    // Gerenciamento de usuários
    getClinicaUsuarios,
    getClinicaUsuario,
    createClinicaUsuario,
    updateClinicaUsuario,
    deleteClinicaUsuario,
    loadAgendaConfigAfterLogin,
}
