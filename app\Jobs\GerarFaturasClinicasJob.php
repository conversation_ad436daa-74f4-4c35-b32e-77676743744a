<?php

namespace App\Jobs;

use App\Models\Assinatura;
use App\Models\FaturaClinica;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GerarFaturasClinicasJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Iniciando geração de faturas de clínicas');

        $hoje = Carbon::now();
        $diaAtual = $hoje->day;

        // Buscar todas as assinaturas ativas que devem ser faturadas hoje
        $assinaturasParaFaturar = Assinatura::with(['clinica', 'plano'])
            ->where('status', 'ativo')
            ->where('data_fim', '>=', $hoje->toDateString()) // Período ainda vigente
            ->where('dia_cobranca', $diaAtual)
            ->get();

        Log::info("Encontradas {$assinaturasParaFaturar->count()} assinaturas para faturar");

        foreach ($assinaturasParaFaturar as $assinatura) {
            try {
                $this->gerarFaturaParaAssinatura($assinatura, $hoje);
            } catch (\Exception $e) {
                Log::error("Erro ao gerar fatura para assinatura {$assinatura->id}: " . $e->getMessage());
            }
        }

        Log::info('Finalizada geração de faturas de clínicas');
    }

    /**
     * Gerar fatura para uma assinatura específica
     */
    private function gerarFaturaParaAssinatura(Assinatura $assinatura, Carbon $dataReferencia): void
    {
        // Verificar se já existe fatura para este mês
        $mesReferencia = $dataReferencia->format('Y-m');
        $faturaExistente = FaturaClinica::where('assinatura_id', $assinatura->id)
            ->where('descricao', 'LIKE', "%{$mesReferencia}%")
            ->first();

        if ($faturaExistente) {
            Log::info("Fatura já existe para assinatura {$assinatura->id} no mês {$mesReferencia}");
            return;
        }

        // Calcular data de vencimento (30 dias a partir da data de cobrança)
        $dataVencimento = $dataReferencia->copy()->addDays(30);

        // Gerar número da fatura
        $numeroFatura = $this->gerarNumeroFatura($dataReferencia);

        // Criar a fatura
        $fatura = FaturaClinica::create([
            'assinatura_id' => $assinatura->id,
            'clinica_id' => $assinatura->clinica_id,
            'numero_fatura' => $numeroFatura,
            'descricao' => "Assinatura {$assinatura->plano->nome} - {$mesReferencia}",
            'valor_nominal' => $assinatura->valor_mensal,
            'valor_desconto' => 0,
            'valor_acrescimo' => 0,
            'valor_final' => $assinatura->valor_mensal,
            'data_vencimento' => $dataVencimento->toDateString(),
            'status' => 'pendente',
        ]);

        Log::info("Fatura {$numeroFatura} gerada para clínica {$assinatura->clinica->nome}");
    }

    /**
     * Gerar número único da fatura
     */
    private function gerarNumeroFatura(Carbon $data): string
    {
        $prefixo = 'FC-' . $data->format('Y-m');
        
        // Buscar o último número do mês
        $ultimaFatura = FaturaClinica::where('numero_fatura', 'LIKE', "{$prefixo}-%")
            ->orderBy('numero_fatura', 'desc')
            ->first();

        if ($ultimaFatura) {
            // Extrair o número sequencial
            $partes = explode('-', $ultimaFatura->numero_fatura);
            $ultimoNumero = (int) end($partes);
            $proximoNumero = $ultimoNumero + 1;
        } else {
            $proximoNumero = 1;
        }

        return $prefixo . '-' . str_pad($proximoNumero, 4, '0', STR_PAD_LEFT);
    }
}
