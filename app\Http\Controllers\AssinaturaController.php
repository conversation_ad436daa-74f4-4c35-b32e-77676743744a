<?php

namespace App\Http\Controllers;

use App\Models\Assinatura;
use App\Models\Clinica;
use App\Models\Plano;
use App\Services\AssinaturaCicloService;
use App\Traits\LogsActionHistory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AssinaturaController extends Controller
{
    use LogsActionHistory;

    protected AssinaturaCicloService $cicloService;

    public function __construct(AssinaturaCicloService $cicloService)
    {
        $this->cicloService = $cicloService;
    }

    /**
     * Display a listing of assinaturas for a specific clinica.
     */
    public function index(Request $request, $clinicaId): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);

        $query = $clinica->assinaturas()
                        ->with(['plano', 'faturas', 'periodoAnterior'])
                        ->orderBy('data_inicio', 'desc');

        // Filtros opcionais
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('apenas_historico') && $request->apenas_historico) {
            $query->historico();
        }

        if ($request->has('periodo_atual') && $request->periodo_atual) {
            $query->periodoAtual();
        }

        $periodos = $query->get();

        // Adicionar informações calculadas
        $periodos->each(function ($periodo) {
            $periodo->duracao_dias = $periodo->data_fim
                ? Carbon::parse($periodo->data_inicio)->diffInDays(Carbon::parse($periodo->data_fim)) + 1
                : Carbon::parse($periodo->data_inicio)->diffInDays(Carbon::now()) + 1;

            $periodo->is_periodo_atual = $periodo->status === 'ativo' && is_null($periodo->data_fim);
        });

        return response()->json($periodos);
    }

    /**
     * Store a newly created assinatura.
     */
    public function store(Request $request, $clinicaId): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);

        // Verificar se já existe uma assinatura ativa
        $assinaturaExistente = $clinica->assinaturaAtiva()->first();
        if ($assinaturaExistente) {
            return response()->json([
                'message' => 'Clínica já possui uma assinatura ativa. Use o endpoint de alteração de plano.',
                'assinatura_ativa' => $assinaturaExistente
            ], 422);
        }

        $validated = $request->validate([
            'plano_id' => 'required|exists:planos,id',
            'data_inicio' => 'required|date',
            'dia_cobranca' => 'sometimes|integer|min:1|max:31',
        ]);

        $plano = Plano::findOrFail($validated['plano_id']);

        $assinatura = Assinatura::create([
            'clinica_id' => $clinicaId,
            'plano_id' => $validated['plano_id'],
            'data_inicio' => $validated['data_inicio'],
            'data_fim' => null, // Período ativo atual
            'valor_mensal' => $plano->valor_mensal ?? 0,
            'dia_cobranca' => $validated['dia_cobranca'] ?? 1,
            'meses_fidelidade' => $plano->meses_fidelidade_minima ?? 0,
            'status' => 'ativo',
            'periodo_anterior_id' => null,
            'motivo_alteracao' => 'Primeira assinatura',
        ]);

        // Atualizar plano_id na clínica
        $clinica->update(['plano_id' => $validated['plano_id']]);

        $assinatura->load(['plano', 'clinica']);

        // Log da ação
        $this->logCreateAction($assinatura, null, $request,
            "Created first subscription for clinica: {$clinica->nome} with plan: {$plano->nome}");

        return response()->json([
            'assinatura' => $assinatura,
            'message' => 'Primeira assinatura criada com sucesso.'
        ], 201);
    }

    /**
     * Display the specified assinatura.
     */
    public function show($clinicaId, $id): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);
        
        $assinatura = $clinica->assinaturas()
                             ->with(['plano', 'faturas'])
                             ->findOrFail($id);

        return response()->json($assinatura);
    }

    /**
     * Update the specified assinatura.
     */
    public function update(Request $request, $clinicaId, $id): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);
        $assinatura = $clinica->assinaturas()->findOrFail($id);

        $validated = $request->validate([
            'data_fim' => 'sometimes|date|after:data_inicio',
            'valor_mensal' => 'sometimes|numeric|min:0',
            'periodo_cobranca' => ['sometimes', Rule::in(['mensal', 'trimestral', 'semestral', 'anual'])],
            'dia_cobranca' => 'sometimes|integer|min:1|max:31',
            'meses_fidelidade' => 'sometimes|integer|min:0',
            'status' => ['sometimes', Rule::in(['ativo', 'cancelado', 'suspenso', 'trial'])],
        ]);

        $assinatura->update($validated);
        $assinatura->load(['plano', 'clinica']);

        // Log da ação
        $this->logUpdateAction($assinatura, $clinica, $request, 
            "Updated assinatura for clinica: {$clinica->nome}");

        return response()->json($assinatura);
    }

    /**
     * Cancel the specified assinatura.
     */
    public function cancelar(Request $request, $clinicaId, $id): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);
        $assinatura = $clinica->assinaturas()->findOrFail($id);

        $validated = $request->validate([
            'motivo_cancelamento' => 'required|string|max:1000',
        ]);

        // Verificar se pode ser cancelada (fidelidade)
        if (!$assinatura->podeSerCancelada()) {
            $diasRestantes = $assinatura->data_inicio
                                       ->addMonths($assinatura->meses_fidelidade)
                                       ->diffInDays(Carbon::now());
            
            return response()->json([
                'message' => 'Assinatura ainda está no período de fidelidade.',
                'dias_restantes_fidelidade' => $diasRestantes
            ], 422);
        }

        $assinatura->update([
            'status' => 'cancelado',
            'data_cancelamento' => Carbon::now()->toDateString(),
            'motivo_cancelamento' => $validated['motivo_cancelamento'],
        ]);

        // Log da ação
        $this->logUpdateAction($assinatura, $clinica, $request, 
            "Cancelled assinatura for clinica: {$clinica->nome}");

        return response()->json($assinatura);
    }

    /**
     * Get active assinatura for a clinica.
     */
    public function assinaturaAtiva($clinicaId): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);
        
        $assinatura = $clinica->assinaturaAtiva()
                             ->with(['plano'])
                             ->first();

        if (!$assinatura) {
            return response()->json([
                'message' => 'Nenhuma assinatura ativa encontrada.'
            ], 404);
        }

        return response()->json($assinatura);
    }

    /**
     * Get active assinatura for a clinica.
     */
    public function getAssinaturaAtiva($clinicaId): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);

        $assinaturaAtiva = $clinica->assinaturaAtiva()->with(['plano'])->first();

        if (!$assinaturaAtiva) {
            return response()->json([
                'message' => 'Nenhuma assinatura ativa encontrada.'
            ], 404);
        }

        return response()->json($assinaturaAtiva);
    }

    /**
     * Change plan for a clinica (creates new assinatura).
     */
    public function alterarPlano(Request $request, $clinicaId): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);

        $validated = $request->validate([
            'novo_plano_id' => 'required|exists:planos,id',
            'data_inicio' => 'required|date',
            'motivo_alteracao' => 'sometimes|string|max:1000',
        ]);

        $novoPlano = Plano::findOrFail($validated['novo_plano_id']);
        $dataInicio = Carbon::parse($validated['data_inicio']);

        // Verificar se o usuário é system_admin
        $user = auth()->payload();
        $isSystemAdmin = $user && isset($user['system_admin']) && $user['system_admin'] == 1;

        // Verificar se já existe assinatura com a mesma data_inicio (constraint única)
        $assinaturaExistente = $clinica->assinaturas()
            ->where('data_inicio', $dataInicio->toDateString())
            ->first();

        if ($assinaturaExistente) {
            if ($isSystemAdmin) {
                // System admin pode remover assinatura do mesmo dia se não houver faturas pagas
                $faturasPagas = $assinaturaExistente->faturas()
                    ->where('status', 'pago')
                    ->exists();

                if ($faturasPagas) {
                    return response()->json([
                        'message' => 'Não é possível alterar o plano pois já existem faturas pagas para a assinatura de hoje.',
                        'data_assinatura' => $assinaturaExistente->data_inicio
                    ], 422);
                }

                // Remover assinatura existente do mesmo dia
                $assinaturaExistente->delete();
                Log::info("System admin removeu assinatura do mesmo dia para permitir nova alteração", [
                    'clinica_id' => $clinicaId,
                    'assinatura_removida_id' => $assinaturaExistente->id,
                    'data_inicio' => $dataInicio->toDateString()
                ]);
            } else {
                // Para usuários normais, verificar período de 30 dias
                $ultimaAlteracao = $clinica->assinaturas()
                    ->where('created_at', '>=', Carbon::now()->subDays(30))
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($ultimaAlteracao) {
                    $diasRestantes = 30 - Carbon::now()->diffInDays($ultimaAlteracao->created_at);
                    return response()->json([
                        'message' => "A assinatura já foi alterada recentemente. A assinatura somente pode ser alterada daqui {$diasRestantes} dias.",
                        'dias_restantes' => $diasRestantes,
                        'ultima_alteracao' => $ultimaAlteracao->created_at->format('d/m/Y')
                    ], 422);
                }
            }
        }

        // Encerrar período atual se existir com valor proporcional
        $periodoAtual = $clinica->assinaturaAtiva()->first();
        if ($periodoAtual) {
            // Calcular valor proporcional para dias utilizados
            $diasUtilizados = Carbon::parse($periodoAtual->data_inicio)->diffInDays($dataInicio);
            $valorProporcional = ($periodoAtual->valor_mensal / 30) * $diasUtilizados;

            $periodoAtual->update([
                'status' => 'encerrado',
                'data_fim' => $dataInicio->copy()->subDay()->toDateString(),
                'valor_proporcional' => $valorProporcional,
                'dias_utilizados' => $diasUtilizados,
                'motivo_alteracao' => $validated['motivo_alteracao'] ?? 'Alteração de plano',
            ]);
        }

        // Usar dia de vencimento da clínica
        $diaVencimento = $clinica->dia_vencimento ?? $dataInicio->day;

        // Calcular data_fim: manter o ciclo no mesmo dia do mês
        // Se mudança é no dia 15 e vencimento é dia 4, próximo período vai até 03 do próximo mês
        $dataFim = $dataInicio->copy()->addMonth()->day($diaVencimento)->subDay();

        // Criar novo período
        $novoPeriodo = Assinatura::create([
            'clinica_id' => $clinicaId,
            'plano_id' => $validated['novo_plano_id'],
            'data_inicio' => $dataInicio->toDateString(),
            'data_fim' => $dataFim->toDateString(),
            'valor_mensal' => $novoPlano->valor_mensal ?? 0,
            'dia_cobranca' => $diaVencimento,
            'meses_fidelidade' => $novoPlano->meses_fidelidade_minima ?? 0,
            'status' => 'ativo',
            'periodo_anterior_id' => $periodoAtual?->id,
            'motivo_alteracao' => $validated['motivo_alteracao'] ?? 'Alteração de plano',
        ]);

        // Atualizar plano_id na clínica
        $clinica->update(['plano_id' => $validated['novo_plano_id']]);

        $novoPeriodo->load(['plano', 'clinica', 'periodoAnterior']);

        // Log da ação
        $this->logCreateAction($novoPeriodo, null, $request,
            "Changed plan for clinica: {$clinica->nome} to {$novoPlano->nome}");

        return response()->json([
            'periodo_anterior' => $periodoAtual,
            'novo_periodo' => $novoPeriodo,
            'message' => 'Plano alterado com sucesso.'
        ]);
    }

    /**
     * Cancelar assinatura ativa
     */
    public function cancelarAssinatura(Request $request, $clinicaId): JsonResponse
    {
        $clinica = Clinica::findOrFail($clinicaId);

        $validated = $request->validate([
            'data_cancelamento' => 'required|date',
            'motivo_cancelamento' => 'required|string|max:1000',
        ]);

        $assinaturaAtiva = $clinica->assinaturaAtiva()->first();

        if (!$assinaturaAtiva) {
            return response()->json([
                'message' => 'Nenhuma assinatura ativa encontrada para cancelar.'
            ], 404);
        }

        $dataCancelamento = Carbon::parse($validated['data_cancelamento']);

        // Cancelar o período atual
        $assinaturaAtiva->update([
            'status' => 'cancelado',
            'data_fim' => $dataCancelamento->toDateString(),
            'motivo_alteracao' => $validated['motivo_cancelamento'],
        ]);

        // Remover plano_id da clínica
        $clinica->update(['plano_id' => null]);

        $assinaturaAtiva->load(['plano', 'clinica']);

        // Log da ação
        $this->logUpdateAction($assinaturaAtiva, null, $request,
            "Cancelled subscription for clinica: {$clinica->nome}");

        return response()->json([
            'assinatura_cancelada' => $assinaturaAtiva,
            'message' => 'Assinatura cancelada com sucesso.'
        ]);
    }
}
