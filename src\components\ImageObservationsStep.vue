<template>
  <div class="image-observations-step">
    <!-- Header -->
    <div class="observations-header">
      <div class="header-content">
        <div class="header-title">
          <i class="fas fa-comment-medical me-2"></i>
          <span>Adicionar <PERSON>er<PERSON></span>
        </div>
        <div class="header-subtitle">
          Adicione observações opcionais para cada imagem selecionada
        </div>
      </div>
      <div class="header-actions">
        <button 
          class="btn btn-outline-secondary btn-sm me-2"
          @click="$emit('back')"
        >
          <i class="fas fa-arrow-left me-1"></i>
          Voltar
        </button>
        <button 
          class="btn btn-success btn-sm"
          @click="sendImages"
          :disabled="sending"
        >
          <span v-if="sending" class="spinner-border spinner-border-sm me-2"></span>
          <i v-else class="fas fa-paper-plane me-1"></i>
          Enviar {{ selectedImages.length }} imagem{{ selectedImages.length > 1 ? 'ns' : '' }}
        </button>
      </div>
    </div>

    <!-- Lista de Imagens com Observações -->
    <div class="observations-container">
      <div class="images-list">
        <div 
          v-for="(image, index) in selectedImages" 
          :key="image.id"
          class="image-observation-item"
        >
          <div class="image-preview">
            <img :src="image.url_preview || image.url_original || image.url" :alt="getImageDescription(image)" />
          </div>
          
          <div class="observation-content">
            <div class="image-info">
              <div class="image-title">
                {{ getImageDescription(image) }}
              </div>
              <div class="image-date">
                <i class="fas fa-calendar me-1"></i>
                {{ $filters.dateLong(image.data) }}
              </div>
            </div>
            
            <div class="observation-input">
              <label class="form-label">
                Observação (opcional)
              </label>
              <textarea
                v-model="imageObservations[image.id]"
                class="form-control"
                rows="3"
                :placeholder="`Adicione uma observação sobre esta imagem...`"
                maxlength="500"
              ></textarea>
              <div class="char-counter">
                {{ (imageObservations[image.id] || '').length }}/500
              </div>
            </div>
          </div>
          
          <div class="item-actions">
            <button 
              class="btn btn-outline-danger btn-sm"
              @click="removeImage(index)"
              title="Remover esta imagem"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Ações Rápidas -->
    <div class="quick-actions">
      <div class="quick-actions-content">
        <div class="quick-action-item">
          <label class="form-label">Observação para todas as imagens:</label>
          <div class="input-group">
            <input
              v-model="globalObservation"
              type="text"
              class="form-control"
              placeholder="Digite uma observação comum..."
              maxlength="500"
            />
            <button 
              class="btn btn-outline-primary"
              @click="applyGlobalObservation"
              :disabled="!globalObservation.trim()"
            >
              <i class="fas fa-copy me-1"></i>
              Aplicar a todas
            </button>
          </div>
        </div>
        
        <div class="quick-action-item">
          <button 
            class="btn btn-outline-warning btn-sm"
            @click="clearAllObservations"
          >
            <i class="fas fa-eraser me-1"></i>
            Limpar todas as observações
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getImageDescription } from "@/helpers/utils";

export default {
  name: 'ImageObservationsStep',
  props: {
    selectedImages: {
      type: Array,
      required: true
    },
    sending: {
      type: Boolean,
      default: false
    }
  },
  emits: ['back', 'send'],
  data() {
    return {
      imageObservations: {},
      globalObservation: ''
    };
  },
  watch: {
    selectedImages: {
      immediate: true,
      handler(newImages) {
        // Inicializar observações vazias para todas as imagens
        if (newImages && newImages.length > 0) {
          const newObservations = { ...this.imageObservations };
          newImages.forEach(image => {
            if (!newObservations[image.id]) {
              newObservations[image.id] = '';
            }
          });
          this.imageObservations = newObservations;
        }
      }
    }
  },
  methods: {
    getImageDescription,
    
    removeImage(index) {
      const updatedImages = [...this.selectedImages];
      const removedImage = updatedImages.splice(index, 1)[0];
      
      // Remove a observação da imagem removida
      delete this.imageObservations[removedImage.id];
      
      // Emitir evento para atualizar as imagens selecionadas no componente pai
      this.$emit('update-selected-images', updatedImages);
      
      // Se não há mais imagens, volta para a galeria
      if (updatedImages.length === 0) {
        this.$emit('back');
      }
    },
    
    applyGlobalObservation() {
      if (!this.globalObservation.trim()) return;

      const newObservations = { ...this.imageObservations };
      this.selectedImages.forEach(image => {
        newObservations[image.id] = this.globalObservation.trim();
      });
      this.imageObservations = newObservations;

      this.globalObservation = '';
    },
    
    clearAllObservations() {
      const newObservations = { ...this.imageObservations };
      this.selectedImages.forEach(image => {
        newObservations[image.id] = '';
      });
      this.imageObservations = newObservations;
    },
    
    sendImages() {
      // Prepara os dados das imagens com suas observações
      const imagesWithObservations = this.selectedImages.map(image => ({
        ...image,
        observation: this.imageObservations[image.id] || ''
      }));
      
      this.$emit('send', imagesWithObservations);
    }
  }
};
</script>

<style scoped>
.image-observations-step {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.observations-header {
  background: white;
  padding: 1.5rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.header-content {
  flex: 1;
}

.header-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #344767;
  display: flex;
  align-items: center;
  margin-bottom: 0.25rem;
}

.header-subtitle {
  color: #6c757d;
  font-size: 0.9rem;
}

.header-actions {
  display: flex;
  align-items: center;
}

.observations-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.images-list {
  max-width: 800px;
  margin: 0 auto;
}

.image-observation-item {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
  transition: all 0.3s ease;
}

.image-observation-item:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.12);
}

.image-preview {
  flex-shrink: 0;
  width: 120px;
  height: 90px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.observation-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.image-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.image-title {
  font-weight: 600;
  color: #344767;
  font-size: 1rem;
}

.image-date {
  color: #6c757d;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
}

.observation-input {
  position: relative;
}

.form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control {
  border-radius: 8px;
  border: 1px solid #ced4da;
  transition: all 0.2s ease;
}

.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.char-counter {
  position: absolute;
  bottom: 0.5rem;
  right: 0.75rem;
  font-size: 0.75rem;
  color: #6c757d;
  background: white;
  padding: 0 0.25rem;
}

.item-actions {
  flex-shrink: 0;
}

.quick-actions {
  background: white;
  border-top: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
  box-shadow: 0 -2px 4px rgba(0,0,0,0.05);
}

.quick-actions-content {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 2rem;
}

.quick-action-item {
  flex: 1;
}

.quick-action-item:last-child {
  flex: 0;
  display: flex;
  align-items: flex-end;
}

.input-group {
  display: flex;
  gap: 0.5rem;
}

.input-group .form-control {
  flex: 1;
}

/* Responsividade */
@media (max-width: 768px) {
  .observations-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .image-observation-item {
    flex-direction: column;
    gap: 1rem;
  }
  
  .image-preview {
    width: 100%;
    height: 200px;
  }
  
  .quick-actions-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .quick-action-item:last-child {
    flex: 1;
    justify-content: flex-start;
  }
}

@media (max-width: 576px) {
  .observations-container {
    padding: 0.5rem;
  }
  
  .image-observation-item {
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  .quick-actions {
    padding: 1rem;
  }
}
</style>
