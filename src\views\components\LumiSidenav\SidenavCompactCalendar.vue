<template>
  <div class="sidenav-compact-calendar">
    <!-- Calendário compacto -->
    <div class="compact-calendar-container">
      <div class="calendar-header">
        <button @click="previousMonth" class="nav-btn">
          <v-icon size="16">mdi-chevron-left</v-icon>
        </button>
        <span class="month-year">
          <v-icon v-if="isLoading" size="12" class="loading-icon">mdi-loading</v-icon>
          {{ currentMonthYear }}
        </span>
        <button @click="nextMonth" class="nav-btn">
          <v-icon size="16">mdi-chevron-right</v-icon>
        </button>
      </div>
      
      <div class="calendar-grid">
        <!-- Dias da semana -->
        <div class="weekdays">
          <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
        </div>
        
        <!-- Dias do mês -->
        <div class="days-grid">
          <button
            v-for="day in calendarDays"
            :key="`${day.month}-${day.day}`"
            @click="selectDate(day)"
            class="day-btn"
            :class="{
              'other-month': !day.isCurrentMonth,
              'selected': isSelectedDate(day),
              'today': isToday(day),
              'has-events': hasEvents(day),
              'day-disabled': isDayDisabled(day),
              'day-schedulable': isDaySchedulable(day)
            }"
            :disabled="!day.isCurrentMonth"
          >
            {{ day.day }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- Labels de informação -->
    <!-- <div class="date-info">
      <div class="selected-date-label">{{ selectedDateLabel }}</div>
      <div class="relative-date-label" :class="relativeDateClass">{{ relativeDateText }}</div>
    </div> -->
  </div>
</template>

<script>
import moment from 'moment';

// Configurar moment para português
moment.locale('pt-br');

export default {
  name: 'SidenavCompactCalendar',
  props: {
    modelValue: {
      type: Date,
      default: () => new Date()
    },
    events: {
      type: Array,
      default: () => []
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'date-selected', 'month-changed'],
  data() {
    return {
      currentDate: new Date(this.modelValue),
      selectedDate: new Date(this.modelValue),
      weekdays: ['D', 'S', 'T', 'Q', 'Q', 'S', 'S']
    };
  },
  computed: {
    currentMonthYear() {
      return moment(this.currentDate).format('MMMM YYYY');
    },
    
    selectedDateLabel() {
      return moment(this.selectedDate).format('dddd, D [de] MMMM [de] YYYY');
    },
    
    relativeDateText() {
      const today = moment().startOf('day');
      const selected = moment(this.selectedDate).startOf('day');
      const diffDays = selected.diff(today, 'days');
      
      if (diffDays === 0) return 'HOJE';
      if (diffDays === 1) return 'AMANHÃ';
      if (diffDays === -1) return 'ONTEM';
      if (diffDays > 1) return `EM ${diffDays} DIAS`;
      if (diffDays < -1) return `HÁ ${Math.abs(diffDays)} DIAS`;
      
      return '';
    },
    
    relativeDateClass() {
      const today = moment().startOf('day');
      const selected = moment(this.selectedDate).startOf('day');
      const diffDays = selected.diff(today, 'days');
      
      return {
        'is-today': diffDays === 0,
        'is-future': diffDays > 0,
        'is-past': diffDays < 0
      };
    },
    
    calendarDays() {
      const days = [];
      const startOfMonth = moment(this.currentDate).startOf('month');
      const endOfMonth = moment(this.currentDate).endOf('month');
      const startOfCalendar = moment(startOfMonth).startOf('week');
      const endOfCalendar = moment(endOfMonth).endOf('week');
      
      let current = moment(startOfCalendar);
      
      while (current.isSameOrBefore(endOfCalendar)) {
        days.push({
          day: current.date(),
          date: new Date(current.toDate()),
          month: current.month(),
          year: current.year(),
          isCurrentMonth: current.month() === this.currentDate.getMonth()
        });
        current.add(1, 'day');
      }
      
      return days;
    }
  },
  watch: {
    modelValue(newValue) {
      this.selectedDate = new Date(newValue);
      this.currentDate = new Date(newValue);
    }
  },
  methods: {
    previousMonth() {
      const novoMes = moment(this.currentDate).subtract(1, 'month').toDate();
      this.currentDate = novoMes;
      console.log('📅 Navegação: Mês anterior ->', this.currentDate);

      // Emitir evento para carregar consultas do novo mês
      this.$emit('month-changed', {
        date: novoMes,
        month: `${novoMes.getFullYear()}-${String(novoMes.getMonth() + 1).padStart(2, '0')}`,
        direction: 'previous'
      });
    },

    nextMonth() {
      const novoMes = moment(this.currentDate).add(1, 'month').toDate();
      this.currentDate = novoMes;
      console.log('📅 Navegação: Próximo mês ->', this.currentDate);

      // Emitir evento para carregar consultas do novo mês
      this.$emit('month-changed', {
        date: novoMes,
        month: `${novoMes.getFullYear()}-${String(novoMes.getMonth() + 1).padStart(2, '0')}`,
        direction: 'next'
      });
    },
    
    selectDate(day) {
      if (!day.isCurrentMonth) return;

      console.log('🎯 SidenavCompactCalendar.selectDate chamado:', {
        day: day.date,
        isCurrentMonth: day.isCurrentMonth,
        previousSelectedDate: this.selectedDate,
        currentDisplayMonth: this.currentDate
      });

      this.selectedDate = new Date(day.date);
      this.$emit('update:modelValue', this.selectedDate);
      this.$emit('date-selected', this.selectedDate);

      console.log('📤 Eventos emitidos para data:', this.selectedDate);
    },
    
    isSelectedDate(day) {
      if (!day.isCurrentMonth) return false;
      const selected = moment(this.selectedDate).startOf('day');
      const dayMoment = moment(day.date).startOf('day');
      return selected.isSame(dayMoment);
    },
    
    isToday(day) {
      if (!day.isCurrentMonth) return false;
      const today = moment().startOf('day');
      const dayMoment = moment(day.date).startOf('day');
      return today.isSame(dayMoment);
    },
    
    hasEvents(day) {
      if (!day.isCurrentMonth || !this.events.length) return false;

      const dayStart = moment(day.date).startOf('day');
      const dayEnd = moment(day.date).endOf('day');

      return this.events.some(event => {
        const eventDate = moment(event.date);
        return eventDate.isBetween(dayStart, dayEnd, null, '[]');
      });
    },

    isDayDisabled(day) {
      if (!day.isCurrentMonth) return false;

      try {
        // Acessar o store da agenda
        const agendaConfig = this.$store.getters['agendaConfig/agendaConfig'];

        if (!agendaConfig || !agendaConfig.dias_semana) return false;

        // Mapear dia da semana para o formato usado na configuração
        const dayOfWeekMap = {
          0: 'domingo',
          1: 'segunda',
          2: 'terca',
          3: 'quarta',
          4: 'quinta',
          5: 'sexta',
          6: 'sabado'
        };

        const dayOfWeek = dayOfWeekMap[day.date.getDay()];

        // Retorna true se o dia NÃO está na lista de dias ativos
        return !agendaConfig.dias_semana.includes(dayOfWeek);
      } catch (error) {
        console.error('Erro ao verificar se dia está desabilitado:', error);
        return false;
      }
    },

    isDaySchedulable(day) {
      if (!day.isCurrentMonth) return false;

      try {
        // Acessar o store da agenda
        const agendaConfig = this.$store.getters['agendaConfig/agendaConfig'];

        if (!agendaConfig || !agendaConfig.dias_semana) return false;

        // Mapear dia da semana para o formato usado na configuração
        const dayOfWeekMap = {
          0: 'domingo',
          1: 'segunda',
          2: 'terca',
          3: 'quarta',
          4: 'quinta',
          5: 'sexta',
          6: 'sabado'
        };

        const dayOfWeek = dayOfWeekMap[day.date.getDay()];

        // Retorna true se o dia está na lista de dias ativos E não tem eventos
        return agendaConfig.dias_semana.includes(dayOfWeek) && !this.hasEvents(day);
      } catch (error) {
        console.error('Erro ao verificar se dia é agendável:', error);
        return false;
      }
    }
  }
};
</script>

<style scoped>
.sidenav-compact-calendar {
  width: 100%;
  padding: 0;
  margin: 0;
}

.compact-calendar-container {
  background: #ffffff;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow: none;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  min-height: 28px;
}

.month-year {
  font-size: 11px;
  font-weight: 600;
  color: #334155;
  text-transform: capitalize;
  flex: 1;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.nav-btn {
  background: none;
  border: none;
  padding: 2px;
  border-radius: 3px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  font-size: 12px;
  line-height: 1;
  min-width: auto;
}

.nav-btn:hover {
  background: #e2e8f0;
  color: #334155;
}

.calendar-grid {
  padding: 8px;
}

.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  margin-bottom: 4px;
}

.weekday {
  text-align: center;
  font-size: 10px;
  font-weight: 600;
  color: #64748b;
  padding: 4px 2px;
  text-transform: uppercase;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.day-btn {
  aspect-ratio: 1.3;
  border: none;
  background: none;
  font-size: 11px;
  font-weight: 500;
  color: #334155;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.day-btn:hover:not(:disabled):not(.selected) {
  background: #f1f5f9;
}

.day-btn.other-month {
  background: #f8fafc;
  color: #cbd5e1;
  cursor: not-allowed;
}

.day-btn.today {
  background: #dbeafe;
  color: #1d4ed8;
  font-weight: 600;
}

.day-btn.selected {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%) !important;
  color: white !important;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);
}

.day-btn.selected:hover {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%) !important;
  color: white !important;
}

.day-btn.has-events::after {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #10b981;
  border-radius: 50%;
}

.day-btn.selected.has-events::after {
  background: rgba(255, 255, 255, 0.8);
}

/* Estilos para categorização de dias */
.day-btn.day-disabled {
  background: #fef2f2;
  color: #dc2626;
  cursor: not-allowed;
}

.day-btn.day-disabled:hover {
  background: #fef2f2 !important;
  color: #dc2626 !important;
}

.day-btn.day-schedulable {
  background: #f0fdf4;
  color: #16a34a;
}

.day-btn.day-schedulable:hover {
  background: #dcfce7;
  color: #15803d;
}

/* Ajustar prioridade para dias selecionados */
.day-btn.selected.day-disabled,
.day-btn.selected.day-schedulable {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%) !important;
  color: white !important;
}

/* Remover os indicadores do canto superior direito */

.date-info {
  padding: 12px 16px;
  border-top: 1px solid #f1f5f9;
  background: #fafbfc;
}

.selected-date-label {
  font-size: 11px;
  font-weight: 600;
  color: #334155;
  text-transform: capitalize;
  margin-bottom: 4px;
  line-height: 1.3;
}

.relative-date-label {
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: color 0.2s ease;
}

.relative-date-label.is-today {
  color: #059669;
}

.relative-date-label.is-future {
  color: #0284c7;
}

.relative-date-label.is-past {
  color: #dc2626;
}
</style>
