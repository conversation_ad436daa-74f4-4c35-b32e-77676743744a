<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Cria tabela pivot para relacionar mensagens de mentoria com imagens.
     * Permite observações individuais por imagem em cada mensagem.
     */
    public function up(): void
    {
        // Criar tabela pivot
        Schema::create('mentoria_mensagem_imagem', function (Blueprint $table) {
            $table->id();
            $table->foreignId('mentoria_mensagem_id')
                ->constrained('mentoria_mensagens')
                ->onDelete('cascade');
            $table->foreignId('imagem_id')
                ->constrained('imagens')
                ->onDelete('cascade');
            $table->text('observation')->nullable(); // Observação específica desta imagem nesta mensagem
            $table->timestamps();

            // Índices para performance
            $table->index(['mentoria_mensagem_id', 'imagem_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mentoria_mensagem_imagem');
    }
};
