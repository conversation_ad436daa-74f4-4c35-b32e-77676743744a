<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clinicas', function (Blueprint $table) {
            // Adicionar campo dia_vencimento (1-31)
            $table->integer('dia_vencimento')->default(1)->after('plano_id');
            
            // Adicionar índice para performance
            $table->index('dia_vencimento');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clinicas', function (Blueprint $table) {
            // Remover índice
            $table->dropIndex(['dia_vencimento']);
            
            // Remover coluna
            $table->dropColumn('dia_vencimento');
        });
    }
};
