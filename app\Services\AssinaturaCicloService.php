<?php

namespace App\Services;

use App\Models\Assinatura;
use App\Models\Clinica;
use App\Models\Plano;
use App\Models\FaturaClinica;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AssinaturaCicloService
{
    /**
     * Criar primeira assinatura para uma clínica
     */
    public function criarPrimeiraAssinatura(Clinica $clinica, Plano $plano, string $motivoAlteracao = 'Primeira assinatura'): array
    {
        return DB::transaction(function () use ($clinica, $plano, $motivoAlteracao) {
            $dataInicio = Carbon::now();
            $resultado = [];

            // Verificar se o plano tem período trial/gratuito
            if ($plano->dias_gratuitos > 0 && $plano->trial_available) {
                // Criar período trial primeiro
                $assinaturaTrial = $this->criarPeriodoTrial($clinica, $plano, $dataInicio, $motivoAlteracao);
                $resultado['periodo_trial'] = $assinaturaTrial;
                
                // Próximo período pago começará após o trial
                $dataInicioPago = $dataInicio->copy()->addDays($plano->dias_gratuitos);
            } else {
                $dataInicioPago = $dataInicio;
            }

            // Criar primeiro período pago
            $assinaturaPaga = $this->criarPeriodoPago($clinica, $plano, $dataInicioPago, $motivoAlteracao, null);
            $resultado['periodo_pago'] = $assinaturaPaga;

            // Gerar fatura para o período pago
            $fatura = $this->gerarFaturaParaPeriodo($assinaturaPaga);
            $resultado['fatura'] = $fatura;

            return $resultado;
        });
    }

    /**
     * Alterar plano de uma clínica existente
     */
    public function alterarPlano(Clinica $clinica, Plano $novoPlano, Carbon $dataInicio, string $motivoAlteracao = 'Alteração de plano'): array
    {
        return DB::transaction(function () use ($clinica, $novoPlano, $dataInicio, $motivoAlteracao) {
            // Encerrar período atual
            $periodoAtual = $clinica->assinaturaAtiva()->first();
            $resultado = ['periodo_anterior' => $periodoAtual];

            if ($periodoAtual) {
                $diasUtilizados = $this->calcularDiasUtilizados($periodoAtual, $dataInicio);
                
                // Atualizar período atual
                $periodoAtual->update([
                    'status' => 'encerrado',
                    'data_fim' => $dataInicio->copy()->subDay()->toDateString(),
                    // 'dias_utilizados' => $diasUtilizados, // TODO: Adicionar após migração
                    'motivo_alteracao' => $motivoAlteracao,
                ]);

                // Gerar fatura proporcional se necessário
                // TODO: Implementar após migração dos novos campos
                // if (!$periodoAtual->fatura_gerada && $diasUtilizados > 0) {
                //     $faturaProporcionl = $this->gerarFaturaProporcional($periodoAtual, $diasUtilizados);
                //     $resultado['fatura_proporcional'] = $faturaProporcionl;
                // }
            }

            // Criar novo período
            $novoPeriodo = $this->criarPeriodoPago($clinica, $novoPlano, $dataInicio, $motivoAlteracao, $periodoAtual?->id);
            $resultado['novo_periodo'] = $novoPeriodo;

            // Gerar fatura para o novo período
            $fatura = $this->gerarFaturaParaPeriodo($novoPeriodo);
            $resultado['fatura'] = $fatura;

            // Atualizar plano_id na clínica
            $clinica->update(['plano_id' => $novoPlano->id]);

            return $resultado;
        });
    }

    /**
     * Criar período trial/gratuito
     */
    private function criarPeriodoTrial(Clinica $clinica, Plano $plano, Carbon $dataInicio, string $motivoAlteracao): Assinatura
    {
        return Assinatura::create([
            'clinica_id' => $clinica->id,
            'plano_id' => $plano->id,
            'data_inicio' => $dataInicio->toDateString(),
            'data_fim' => $dataInicio->copy()->addDays($plano->dias_gratuitos - 1)->toDateString(),
            'status' => 'ativo',
            'valor_mensal' => 0,
            'dia_cobranca' => $clinica->dia_vencimento ?? 1,
            'meses_fidelidade' => $plano->meses_fidelidade_minima ?? 0,
            'motivo_alteracao' => $motivoAlteracao,
            'periodo_anterior_id' => null,
        ]);
    }

    /**
     * Criar período pago
     */
    private function criarPeriodoPago(Clinica $clinica, Plano $plano, Carbon $dataInicio, string $motivoAlteracao, ?int $periodoAnteriorId = null): Assinatura
    {
        // Calcular data fim baseada no dia de vencimento da clínica (padrão: dia 1)
        $diaVencimento = $clinica->dia_vencimento ?? 1;
        $dataFim = $this->calcularProximoVencimento($dataInicio, $diaVencimento);
        
        return Assinatura::create([
            'clinica_id' => $clinica->id,
            'plano_id' => $plano->id,
            'data_inicio' => $dataInicio->toDateString(),
            'data_fim' => $dataFim->toDateString(), // Sempre definir data_fim
            'status' => 'ativo',
            'valor_mensal' => $plano->valor_mensal ?? 0,
            'dia_cobranca' => $diaVencimento,
            'meses_fidelidade' => $plano->meses_fidelidade_minima ?? 0,
            'motivo_alteracao' => $motivoAlteracao,
            'periodo_anterior_id' => $periodoAnteriorId,
        ]);
    }

    /**
     * Gerar fatura para um período
     */
    private function gerarFaturaParaPeriodo(Assinatura $assinatura): FaturaClinica
    {
        $diasPeriodo = Carbon::parse($assinatura->data_inicio)->diffInDays(Carbon::parse($assinatura->data_fim)) + 1;
        $valorProporcional = $this->calcularValorProporcional($assinatura->valor_mensal, $diasPeriodo);
        
        // Atualizar assinatura
        // TODO: Implementar após migração dos novos campos
        // $assinatura->update([
        //     'valor_proporcional' => $valorProporcional,
        //     'fatura_gerada' => true
        // ]);

        // Gerar número da fatura
        $numeroFatura = $this->gerarNumeroFatura($assinatura->clinica_id);

        return FaturaClinica::create([
            'clinica_id' => $assinatura->clinica_id,
            'assinatura_id' => $assinatura->id,
            'numero_fatura' => $numeroFatura,
            'valor_nominal' => $valorProporcional,
            'valor_desconto' => 0,
            'valor_acrescimo' => 0,
            'valor_final' => $valorProporcional,
            'data_vencimento' => Carbon::parse($assinatura->data_fim),
            'status' => 'pendente',
            'observacoes' => "Fatura referente ao período de {$assinatura->data_inicio} a {$assinatura->data_fim}",
        ]);
    }

    /**
     * Gerar fatura proporcional para período encerrado
     */
    private function gerarFaturaProporcional(Assinatura $assinatura, int $diasUtilizados): FaturaClinica
    {
        $valorProporcional = $this->calcularValorProporcional($assinatura->valor_mensal, $diasUtilizados);
        
        // Atualizar assinatura
        // TODO: Implementar após migração dos novos campos
        // $assinatura->update([
        //     'valor_proporcional' => $valorProporcional,
        //     'fatura_gerada' => true
        // ]);

        // Gerar número da fatura
        $numeroFatura = $this->gerarNumeroFatura($assinatura->clinica_id);

        return FaturaClinica::create([
            'clinica_id' => $assinatura->clinica_id,
            'assinatura_id' => $assinatura->id,
            'numero_fatura' => $numeroFatura,
            'valor_nominal' => $valorProporcional,
            'valor_desconto' => 0,
            'valor_acrescimo' => 0,
            'valor_final' => $valorProporcional,
            'data_vencimento' => Carbon::now()->addDays(7), // 7 dias para pagamento
            'status' => 'pendente',
            'observacoes' => "Fatura proporcional - {$diasUtilizados} dias utilizados",
        ]);
    }

    /**
     * Calcular dias utilizados em um período
     */
    private function calcularDiasUtilizados(Assinatura $assinatura, Carbon $dataFim): int
    {
        $dataInicio = Carbon::parse($assinatura->data_inicio);
        return $dataInicio->diffInDays($dataFim);
    }

    /**
     * Calcular valor proporcional baseado nos dias
     */
    private function calcularValorProporcional(float $valorMensal, int $dias): float
    {
        $diasMes = 30; // Padrão de 30 dias por mês
        return ($valorMensal / $diasMes) * $dias;
    }

    /**
     * Calcular próximo vencimento baseado no dia de vencimento da clínica
     */
    private function calcularProximoVencimento(Carbon $dataInicio, int $diaVencimento): Carbon
    {
        $proximoVencimento = $dataInicio->copy();
        
        // Se o dia atual é menor que o dia de vencimento, usar o mesmo mês
        if ($dataInicio->day <= $diaVencimento) {
            $proximoVencimento->day($diaVencimento);
        } else {
            // Caso contrário, usar o próximo mês
            $proximoVencimento->addMonth()->day($diaVencimento);
        }
        
        return $proximoVencimento;
    }

    /**
     * Gerar número único da fatura
     */
    private function gerarNumeroFatura(int $clinicaId): string
    {
        $ano = Carbon::now()->year;
        $mes = Carbon::now()->format('m');
        
        $ultimaFatura = FaturaClinica::where('clinica_id', $clinicaId)
            ->whereYear('created_at', $ano)
            ->whereMonth('created_at', Carbon::now()->month)
            ->orderBy('id', 'desc')
            ->first();
            
        $sequencial = $ultimaFatura ? (int)substr($ultimaFatura->numero_fatura, -4) + 1 : 1;
        
        return sprintf('%04d%s%s%04d', $clinicaId, $ano, $mes, $sequencial);
    }
}
