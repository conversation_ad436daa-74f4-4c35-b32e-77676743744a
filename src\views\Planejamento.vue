<template>
  <div>
    <div class="p-horizontal-divider my-0 d-none d-md-block" ref="metasTerapeuticasFraming"></div>

      <Transition name="carousel-fade">
        <div v-if="imagens.length > 0"
          class="p-1 flex-row img-carousel-container d-none d-md-flex"
          v-viewer="{ url: 'data-source', title: [1, (image, imageData) => `${image.alt}`], hide: resumeCarousel }"
          ref="carouselViewer"
          :class="{ 'dimmed-carousel': planejamentoTab === 'imagens' }"
        >
        <div class="carousel-controls">
          <button class="carousel-control" @click="toggleCarousel" :title="isPaused ? 'Iniciar' : 'Pausar'">
            <font-awesome-icon :icon="['fas', isPaused ? 'play' : 'pause']" />
          </button>
          <button class="carousel-control" @click="toggleCarouselVisibility" :title="isCarouselHidden ? 'Mostrar' : 'Esconder'">
            <font-awesome-icon :icon="['fas', isCarouselHidden ? 'chevron-down' : 'chevron-up']" />
          </button>
        </div>
        <!-- Botão para restaurar o carousel quando estiver escondido -->
        <Transition name="fade">
          <div v-if="isCarouselHidden" class="carousel-restore-container">
            <button class="carousel-restore-button" @click="toggleCarouselVisibility" title="Mostrar documentação">
              <font-awesome-icon :icon="['fas', 'images']" class="me-2" />
              Mostrar documentação
            </button>
          </div>
        </Transition>

        <div
          class="img-carousel-inner"
          :style="{
            animationPlayState: isPaused ? 'paused' : 'running',
            display: isCarouselHidden ? 'none' : 'flex'
          }"
        >
          <div
            v-for="imagem in imagens"
            :key="imagem.url"
            class="img-carousel-item"
          >
            <img
              :src="imagem.url_preview || imagem.url_original || imagem.url"
              :data-source="imagem.url_original || imagem.url"
              :alt="getImageDescription(imagem)"
              :title="getImageDescription(imagem)"
              @click="pauseCarousel"
            />
            <div class="img-carousel-info">
              <span class="img-time">{{ $filters.howMuchTime(imagem.data, { type: 'date' }) }}</span>
              <span class="img-desc" v-if="imagem.descricao">{{ truncateDescription(imagem.descricao, 50) }}</span>
            </div>
          </div>
          <div
            v-for="imagem in imagens"
            :key="'dup-' + imagem.url"
            class="img-carousel-item"
          >
            <img
              :src="imagem.url_preview || imagem.url_original || imagem.url"
              :data-source="imagem.url_original || imagem.url"
              :alt="getImageDescription(imagem)"
              :title="getImageDescription(imagem)"
              @click="pauseCarousel"
            />
            <div class="img-carousel-info">
              <span class="img-time">{{ $filters.howMuchTime(imagem.data, { type: 'date' }) }}</span>
              <span class="img-desc" v-if="imagem.descricao">{{ truncateDescription(imagem.descricao, 50) }}</span>
            </div>
          </div>
        </div>
      </div>
      </Transition>

    <div class="my-0" ref="metasTerapeuticasFraming" style="margin-top: 0px !important; background:rgb(228, 228, 228); height: 1px;"></div>

    <!-- Navegação de Fases Elegante -->
    <div class="phases-navigation-wrapper mb-3" :class="{ 'sticky-phases': true }">
      <div class="phases-navigation">
        <!-- Documentação Inicial -->
        <div class="phase-step"
             :class="getStepClasses('imagens')"
             @click="selectPlanejamentoTab('imagens')">
          <div class="phase-content">
            <div class="phase-icon-container">
              <div class="phase-icon">
                <font-awesome-icon
                  :icon="getStepOriginalIcon('imagens')"
                />
              </div>
            </div>
            <div class="phase-title">Documentação inicial</div>
            <div class="phase-status"
                 :class="{
                   'completed': isStepCompleted('imagens'),
                   'pending': !isStepCompleted('imagens'),
                   'active': planejamentoTab === 'imagens'
                 }">
              <template v-if="isStepCompleted('imagens')">
                <font-awesome-icon :icon="['fas', 'check-double']" />
              </template>
              <template v-else>
                <font-awesome-icon :icon="['fas', 'clock']" />
              </template>
            </div>
          </div>
          <div class="phase-chevron"></div>
        </div>

        <!-- Análise -->
        <div class="phase-step"
             :class="getStepClasses('analise')"
             @click="selectPlanejamentoTab('analise')">
          <div class="phase-content">
            <div class="phase-icon-container">
              <div class="phase-icon">
                <font-awesome-icon
                  :icon="getStepOriginalIcon('analise')"
                />
              </div>
            </div>
            <div class="phase-title">Análise clínica</div>
            <div class="phase-status"
                 :class="{
                   'completed': isStepCompleted('analise'),
                   'pending': !isStepCompleted('analise'),
                   'active': planejamentoTab === 'analise'
                 }">
              <template v-if="isStepCompleted('analise')">
                <font-awesome-icon :icon="['fas', 'check-double']" />
              </template>
              <template v-else>
                <font-awesome-icon :icon="['fas', 'clock']" />
              </template>
            </div>
          </div>
          <div class="phase-chevron"></div>
        </div>

        <!-- Diagnóstico -->
        <div class="phase-step"
             :class="getStepClasses('diagnostico')"
             @click="selectPlanejamentoTab('diagnostico')">
          <div class="phase-content">
            <div class="phase-icon-container">
              <div class="phase-icon">
                <font-awesome-icon
                  :icon="getStepOriginalIcon('diagnostico')"
                />
              </div>
            </div>
            <div class="phase-title">Diagnóstico</div>
            <div class="phase-status"
                 :class="{
                   'completed': isStepCompleted('diagnostico'),
                   'pending': !isStepCompleted('diagnostico'),
                   'active': planejamentoTab === 'diagnostico'
                 }">
              <template v-if="isStepCompleted('diagnostico')">
                <font-awesome-icon :icon="['fas', 'check-double']" />
              </template>
              <template v-else>
                <font-awesome-icon :icon="['fas', 'clock']" />
              </template>
            </div>
          </div>
          <div class="phase-chevron"></div>
        </div>

        <!-- Plano de Tratamento -->
        <div class="phase-step last-step"
             :class="getStepClasses('planoTratamento')"
             @click="selectPlanejamentoTab('planoTratamento')">
          <div class="phase-content">
            <div class="phase-icon-container">
              <div class="phase-icon">
                <font-awesome-icon
                  :icon="getStepOriginalIcon('planoTratamento')"
                />
              </div>
            </div>
            <div class="phase-title">Plano de tratamento</div>
            <div class="phase-status"
                 :class="{
                   'completed': isStepCompleted('planoTratamento'),
                   'pending': !isStepCompleted('planoTratamento'),
                   'active': planejamentoTab === 'planoTratamento'
                 }">
              <template v-if="isStepCompleted('planoTratamento')">
                <font-awesome-icon :icon="['fas', 'check-double']" />
              </template>
              <template v-else>
                <font-awesome-icon :icon="['fas', 'clock']" />
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="p-horizontal-divider my-2"></div>

    <Transition>
      <Imagens
        v-show="planejamentoTab === 'imagens'"
        :paciente="paciente"
        mode="diagnostic"
        @pacienteChange="$emit('pacienteChange')"
        @selectTab="selectPlanejamentoTab"
      />
    </Transition>

    <Transition>
      <Analise
        v-if="planejamentoTab === 'analise'"
        :paciente="paciente"
        :detalhesClinicos="detalhesClinicos"
        @pacienteChange="$emit('pacienteChange')"
        @selectTab="selectPlanejamentoTab"
      />
    </Transition>

    <Transition>
      <Diagnostico
        v-show="planejamentoTab === 'diagnostico'"
        :paciente="paciente"
        :diagnostico="paciente.diagnostico"
        :prognostico="paciente.prognostico"
        @pacienteChange="$emit('pacienteChange')"
        @selectTab="selectPlanejamentoTab"
      />
    </Transition>

    <Transition>
      <PlanoTratamento
        v-show="planejamentoTab === 'planoTratamento'"
        :paciente="paciente"
        @pacienteChange="$emit('pacienteChange')"
        @edit-mode-active="handleEditModeActive"
      />
    </Transition>


  </div>
</template>

<style scoped>
/* Transition effects elegantes */
.v-enter-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.v-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.6, 1);
}

.v-enter-from {
  opacity: 0;
  transform: translateY(15px);
}

.v-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Estilos da Navegação de Fases Compacta e Sticky */
.phases-navigation-wrapper {
  padding: 0;
  margin: 8px 0;
  border-radius: 0;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(222, 226, 230, 0.4);
  border-left: none;
  border-right: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sticky behavior apenas para desktop */
@media (min-width: 769px) {
  .phases-navigation-wrapper.sticky-phases {
    position: sticky;
    top: 0px; /* Bem no topo */
    z-index: 100;
  }

  .phases-navigation-wrapper.sticky-phases.is-stuck {
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.12),
      0 3px 10px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-bottom: 2px solid rgba(222, 226, 230, 0.6);
  }
}

.phases-navigation {
  display: flex;
  height: 42px;
  position: relative;
  z-index: 1;
}



.phase-step {
  flex: 1;
  position: relative;
  cursor: pointer;
  background: linear-gradient(135deg,
    rgba(248, 249, 250, 0.95) 0%,
    rgba(255, 255, 255, 0.98) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%, 20px 50%);
  margin-right: -20px;
  z-index: 1;
}

/* Bordas elegantes com pseudo-elementos para melhor controle */
.phase-step::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%, 20px 50%);
  border: 2px solid rgba(222, 226, 230, 0.6);
  border-radius: 0;
  z-index: -1;
  border-right: none !important;
  border-left: none !important;
}

.phase-step:first-child {
  clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%);
  margin-right: -20px;
}

.phase-step:first-child::before {
  clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%);
}

.phase-step:last-child {
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20px 50%);
  margin-right: 0;
}

.phase-step:last-child::before {
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 20px 50%);
}

.phase-step:first-child:last-child {
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
  margin-right: 0;
}

.phase-step:first-child:last-child::before {
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}

/* Efeito de hover elegante e limpo */
.phase-step {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

/* Efeito principal de hover */
.phase-step:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(59, 130, 246, 0.25);
}

/* Borda superior animada */
.phase-step::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg,
    rgba(59, 130, 246, 0) 0%,
    rgba(59, 130, 246, 0.8) 50%,
    rgba(59, 130, 246, 0) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0 0 2px 2px;
}

/* Borda inferior animada */
.phase-step::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg,
    rgba(59, 130, 246, 0) 0%,
    rgba(59, 130, 246, 0.6) 50%,
    rgba(59, 130, 246, 0) 100%);
  transform: translateX(100%);
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.1s;
  border-radius: 2px 2px 0 0;
}

/* Ativação do hover */
.phase-step:hover::before {
  transform: translateX(0);
}

.phase-step:hover::after {
  transform: translateX(0);
}

/* Efeito de propagação sutil */
.phase-step:hover + .phase-step {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
}

/* Efeito reverso para o elemento anterior (usando JavaScript seria melhor, mas vamos tentar CSS) */
.phase-step:has(+ .phase-step:hover) {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.1);
}

/* Fallback para navegadores sem suporte a :has() */
@supports not selector(:has(*)) {
  .phases-navigation:hover .phase-step:not(:hover) {
    opacity: 0.7;
    transform: translateY(-0.5px);
  }
}

/* Efeito de onda global na navegação */
.phases-navigation {
  position: relative;
  overflow: hidden;
}

.phases-navigation::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.4) 25%,
    rgba(59, 130, 246, 0.8) 50%,
    rgba(59, 130, 246, 0.4) 75%,
    transparent 100%);
  transform: translateX(-100%);
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 1px;
}

.phases-navigation:hover::before {
  transform: translateX(100%);
}

.phases-navigation::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(59, 130, 246, 0.3) 25%,
    rgba(59, 130, 246, 0.6) 50%,
    rgba(59, 130, 246, 0.3) 75%,
    transparent 100%);
  transform: translateX(100%);
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s;
  border-radius: 1px;
}

.phases-navigation:hover::after {
  transform: translateX(-100%);
}

/* Remover o chevron antigo */
.phase-chevron {
  display: none;
}

.phase-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  z-index: 2;
  position: relative;
  padding-top: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  width: 100%;
}

.phase-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.phase-icon {
  font-size: 15px;
  color: rgba(108, 117, 125, 0.85);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Status text styles */
.phase-status {
  position: absolute;
  top: 50%;
  right: 25px;
  transform: translateY(-50%);
  font-size: 0.55rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 2px;
  opacity: 0.9;
  z-index: 4;
}

.phase-status svg {
  font-size: 14pt;
  margin-right: 2px;
  margin-bottom: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.15));
}

.phase-status.completed {
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.phase-status.pending {
  color: rgba(108, 117, 125, 0.8);
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);
}

.phase-status.active.pending {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.phase-status .fa-check-double {
  color: rgba(255, 255, 255, 0.95);
  font-size: 15pt;
  transform: scale(1.1);
}

/* Efeito especial no ícone de status quando ativo e completo */
.phase-step.active.completed .phase-status .fa-check-double {
  color: rgba(255, 255, 255, 1);
  font-size: 15pt;
  transform: scale(1.15);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 4;
}

.phase-status .fa-clock {
  color: #d97706;
  font-size: 13pt;
  animation: subtle-pulse 2s ease-in-out infinite;
}

@keyframes subtle-pulse {
  0%, 100% { opacity: 0.8; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.05); }
}

.phase-title {
  font-weight: 600;
  color: rgba(73, 80, 87, 0.9);
  text-align: center;
  line-height: 1.0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.05px;
  margin-top: 0;
  text-transform: uppercase;
  font-size: 0.62rem;
}

/* Estados das fases - Robustos e Elegantes */

/* Estado Ativo - Azul Sutil e Elegante */
.phase-step.active {
  background: linear-gradient(135deg,
    #3b82f6 0%,
    #60a5fa 50%,
    #93c5fd 100%);
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.2),
    0 3px 10px rgba(96, 165, 250, 0.15),
    0 0 0 2px rgba(147, 197, 253, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  z-index: 5;
}

.phase-step.active::before {
  border: 2px solid rgba(96, 165, 250, 0.4);
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.8),
    inset 0 0 0 1px rgba(147, 197, 253, 0.1);
}

.phase-step.active .phase-icon {
  color: #ffffff !important;
  font-size: 17px !important;
  transform: scale(1.05);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.phase-step.active .phase-title {
  color: #ffffff !important;
  font-weight: 700;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  font-size: 0.68rem;
}

.phase-step.active svg {
  color: #ffffff !important;
  transform: scale(1.05);
}

/* Estado Completo - Verde Médico Sutil e Elegante */
.phase-step.completed {
  background: linear-gradient(135deg,
    #059669 0%,
    #10b981 50%,
    #34d399 100%);
  box-shadow:
    0 4px 14px rgba(5, 150, 105, 0.15),
    0 2px 6px rgba(16, 185, 129, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  z-index: 3;
}

/* Bordas mais definidas para tabs concluídas */
.phase-step.completed::before {
  border: 2px solid rgba(16, 185, 129, 0.3);
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.75),
    inset 0 0 0 1px rgba(52, 211, 153, 0.08);
}

.phase-step.completed .phase-icon {
  color: #ffffff;
  font-size: 17px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.phase-step.completed .phase-title {
  color: #ffffff;
  font-weight: 650;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Estado Ativo + Completo - Verde com efeito de brilho elegante */
.phase-step.active.completed {
  background: linear-gradient(135deg, #51ffc8 0%, #32cb98 50%, #34d399 100%);
  box-shadow:
    0 5px 18px rgba(5, 150, 105, 0.22),
    0 2px 8px rgba(16, 185, 129, 0.18),
    0 0 0 2px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
  z-index: 6;
  position: relative;
  overflow: hidden;
}

/* Borda sutil para indicar ativo */
.phase-step.active.completed::before {
  border: 2px solid rgba(59, 130, 246, 0.4);
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.8),
    inset 0 0 0 1px rgba(96, 165, 250, 0.1);
}

/* Efeito de brilho que passa sobre o conteúdo */
.phase-step.active.completed::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%);
  clip-path: inherit;
  z-index: 3;
  animation: shine-sweep 3s ease-in-out infinite;
  animation-delay: 1s;
}

/* Animação do brilho que varre o elemento */
@keyframes shine-sweep {
  0% {
    left: -100%;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

.phase-step.active.completed .phase-icon {
  color: #ffffff;
  font-size: 22px;
  transform: scale(1.05);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
  position: relative;
  z-index: 4;
}

.phase-step.active.completed .phase-title {
  color: #ffffff;
  font-weight: 700;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  font-size: 0.8rem;
  position: relative;
  z-index: 4;
}

/* Estado Pendente - Levemente apagado mas acessível */
.phase-step.pending:not(.active) {
  opacity: 0.75;
}

.phase-step.pending:not(.active) .phase-icon {
  color: rgba(108, 117, 125, 0.7);
}

.phase-step.pending:not(.active) .phase-title {
  color: rgba(73, 80, 87, 0.7);
}

/* Efeitos de Hover Elegantes */
.phase-step:hover:not(.active):not(.completed) {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 249, 250, 0.95) 100%);
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.12),
    0 3px 10px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  z-index: 4;
  opacity: 1; /* Remove a opacidade reduzida no hover */
}

.phase-step:hover:not(.active):not(.completed)::before {
  border: 2px solid rgba(96, 165, 250, 0.35);
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.8),
    inset 0 0 0 1px rgba(147, 197, 253, 0.08);
}

/* Hover específico para elementos completed */
.phase-step.completed:hover:not(.active) {
  background: linear-gradient(135deg,
    #047857 0%,
    #059669 50%,
    #10b981 100%);
  box-shadow:
    0 5px 18px rgba(4, 120, 87, 0.18),
    0 2px 8px rgba(5, 150, 105, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.phase-step.completed:hover:not(.active)::before {
  border: 2px solid rgba(5, 150, 105, 0.35);
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.8),
    inset 0 0 0 1px rgba(16, 185, 129, 0.1);
}

/* Hover para elementos não completed e não active */
.phase-step:hover:not(.active):not(.completed) .phase-status {
  color: rgba(59, 130, 246, 0.85);
  text-shadow: 0 1px 2px rgba(96, 165, 250, 0.2);
}

.phase-step:hover:not(.active):not(.completed) .phase-icon {
  transform: scale(1.1);
  color: rgba(59, 130, 246, 0.85);
  text-shadow: 0 1px 2px rgba(96, 165, 250, 0.2);
}

.phase-step:hover:not(.active):not(.completed) .phase-title {
  color: rgba(59, 130, 246, 0.9);
  text-shadow: 0 1px 2px rgba(96, 165, 250, 0.15);
}

/* Hover para elementos completed */
.phase-step.completed:hover:not(.active) .phase-icon {
  transform: scale(1.1);
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.phase-step.completed:hover:not(.active) .phase-title {
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Responsividade Elegante */
@media (max-width: 768px) {
  .phases-navigation-wrapper {
    margin: 8px 0;
    border-radius: 0;
    /* Não aplicar sticky no mobile */
    position: relative !important;
    top: auto !important;
  }

  .phases-navigation {
    height: 40px;
  }

  .phase-content {
    gap: 3px;
    padding: 6px 8px 3px 8px;
  }

  .phase-icon {
    font-size: 16px;
    margin-bottom: 0;
  }

  .phase-step.active .phase-icon {
    font-size: 18px;
  }

  .phase-step.active {
    box-shadow:
      0 4px 14px rgba(59, 130, 246, 0.2),
      0 2px 8px rgba(96, 165, 250, 0.15),
      0 0 0 2px rgba(147, 197, 253, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }

  .phase-status {
    font-size: 0.5rem;
    right: 6px;
    gap: 1px;
  }

  .phase-status .fa-check-double {
    font-size: 12pt;
  }

  .phase-status .fa-clock {
    font-size: 11pt;
  }

  .phase-title {
    font-size: 0.6rem;
    line-height: 1.0;
    letter-spacing: 0.05px;
    margin-top: 0;
  }

  .phase-step:hover:not(.active):not(.completed) {
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.06);
    opacity: 1; /* Remove a opacidade reduzida no hover em mobile também */
  }

  .phase-step.completed:hover:not(.active) {
    box-shadow:
      0 4px 14px rgba(4, 120, 87, 0.18),
      0 2px 8px rgba(5, 150, 105, 0.15);
  }

  .phase-step:hover:not(.active) .phase-icon {
    transform: scale(1.05);
  }

  .phase-step:hover:not(.active) .phase-title {
    transform: translateY(-1px);
  }

  /* Ajustar clip-path para mobile */
  .phase-step {
    clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 50%, calc(100% - 15px) 100%, 0 100%, 15px 50%);
    margin-right: -15px;
  }

  .phase-step:first-child {
    clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 50%, calc(100% - 15px) 100%, 0 100%);
    margin-right: -15px;
  }

  .phase-step:last-child {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%, 15px 50%);
    margin-right: 0;
  }

  .phase-step:first-child:last-child {
    clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    margin-right: 0;
  }
}

.planejamento-content {
  padding: 20px;
  padding-top: 5px;
}

.img-carousel-container {
  overflow-y: hidden;
  overflow-x: hidden; /* hide scrollbar */
  background: #f8f9fa;
  border-width: 0px 1px 0px 1px;
  border-style: solid;
  border-color: #e2e2e2;
  gap: 0px;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.03);
  padding: 10px 0;
}

.img-carousel-container.dimmed-carousel {
  background: rgba(0, 0, 0, 0.03);
  position: relative;
}

.img-carousel-container.dimmed-carousel::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(1px);
  z-index: 5;
  pointer-events: none;
}

.carousel-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  gap: 5px;
}

.carousel-control {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #dee2e6;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #495057;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.carousel-control:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.carousel-restore-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  min-height: 60px;
}

.carousel-restore-button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #dee2e6;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 0.85rem;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.carousel-restore-button:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  color: #2152ff;
}

.img-carousel-inner {
  display: flex;
  flex-wrap: nowrap;
  animation: scroll-left 40s linear infinite;
  padding: 5px 0;
}

.img-carousel-item {
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 8px;
  flex-shrink: 0;
  margin-right: 8px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.img-carousel-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.85), transparent);
  color: white;
  padding: 12px 8px 8px;
  font-size: 0.7rem;
  text-align: center;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.img-carousel-info .img-time {
  font-weight: 600;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.img-carousel-info .img-desc {
  font-size: 0.65rem;
  opacity: 0.9;
  font-style: italic;
  line-height: 1.2;
  max-height: 2.4em;
  overflow: hidden;
}

.img-carousel-item:hover .img-carousel-info {
  transform: translateY(0);
}

.img-carousel-item img {
  width: 160px;
  height: auto;
  aspect-ratio: 9/6;
  object-fit: cover;
  filter: brightness(90%);
  transition: all 0.3s ease;
}

.img-carousel-item:hover {
  transform: translateY(-5px) scale(1.03);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.img-carousel-item:hover img {
  filter: brightness(110%);
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.empty-photo {
  width: 300px;
  height: 96px;
}

/* Carousel fade transition */
.carousel-fade-enter-active,
.carousel-fade-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.carousel-fade-enter-from,
.carousel-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Fade transition for buttons */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}



</style>

<script>
import MaterialInput from "@/components/MaterialInput.vue";
import Analise from "./Planejamento/components/Analise.vue";
import Diagnostico from "./Planejamento/components/Diagnostico.vue";

import Imagens from "./Planejamento/components/Imagens.vue";
import PlanoTratamento from "./Planejamento/components/PlanoTratamento.vue";
import { getImageDescription } from "@/helpers/utils";

const items = [];
var isEditing = [];

export default {
  name: "planejamento",
  props: {
    paciente: {
      type: Object,
    },
  },
  data() {
    return {
      isEditing,
      items,
      planejamentoTab: "imagens",

      isPaused: false,
      isCarouselHidden: false,
      editModeActive: false,
    };
  },
  methods: {
    getImageDescription,
    selectPlanejamentoTab(tab) {
      // Atualizar a variável de estado
      this.planejamentoTab = tab;

      // Handle carousel visibility based on selected tab
      if (tab === 'imagens') {
        // When on Documentação inicial tab, dim the carousel but don't hide it completely
        // This prevents the layout from jumping when switching tabs
      } else {
        // When on other tabs, ensure carousel is visible
        this.isCarouselHidden = false;
      }

    },

    // Métodos para a barra de progresso
    getStepClasses(step) {
      const classes = [];

      if (this.planejamentoTab === step) {
        classes.push('active');
      }

      if (this.isStepCompleted(step)) {
        classes.push('completed');
      } else {
        // Adiciona classe 'pending' para passos não concluídos (mas não bloqueia acesso)
        classes.push('pending');
      }

      return classes;
    },

    getStepIcon(step) {
      const icons = {
        'imagens': ['fas', 'image'],
        'analise': ['fas', 'magnifying-glass'],
        'diagnostico': ['fas', 'book-medical'],
        'planoTratamento': ['fas', 'file-pen']
      };

      // Se a etapa está completa, mostra um check
      if (this.isStepCompleted(step)) {
        return ['fas', 'check'];
      }

      return icons[step] || ['fas', 'circle'];
    },

    getStepOriginalIcon(step) {
      const icons = {
        'imagens': ['fas', 'image'],
        'analise': ['fas', 'magnifying-glass'],
        'diagnostico': ['fas', 'book-medical'],
        'planoTratamento': ['fas', 'file-pen']
      };

      return icons[step] || ['fas', 'circle'];
    },



    isStepCompleted(step) {
      switch (step) {
        case 'imagens':
          // Considera completa se tem pelo menos algumas imagens de diagnóstico
          // Mesma lógica do botão "INICIAR ANÁLISE" que verifica safePatientImages.length > 0
          return this.safePatientImages.length > 0;

        case 'analise': {
          // As análises não ficam no paciente.analises, são carregadas separadamente
          // Vamos verificar se existem análises salvas no banco de dados
          // Para isso, vamos usar uma abordagem mais simples: verificar se o botão "GERAR DIAGNÓSTICO" deveria estar habilitado
          // Se o usuário já passou pela etapa de análise e salvou dados, provavelmente há análises

          // Verificação alternativa: se há diagnóstico, provavelmente as análises foram feitas
          // Ou se há fatores clínicos definidos (que são gerados após análises)
          const temFatoresClinicosDefinidos = this.paciente.fatores_clinicos && this.paciente.fatores_clinicos.length > 0;
          const temTratamentosSugeridos = this.paciente.tratamentos_sugeridos && this.paciente.tratamentos_sugeridos.length > 0;

          // Se tem fatores clínicos ou tratamentos sugeridos, significa que as análises foram processadas
          return temFatoresClinicosDefinidos || temTratamentosSugeridos;
        }

        case 'diagnostico': {
          // Considera completa se tem prognóstico preenchido
          // O campo "diagnostico" (Observações clínicas) é opcional
          // Mesma lógica que habilita o botão "GERAR PLANO DE TRATAMENTO"
          const temPrognostico = this.paciente.prognostico && this.paciente.prognostico.trim().length > 0;
          return temPrognostico;
        }

        case 'planoTratamento': {
          // Considera completa se tem pelo menos algumas metas terapêuticas definidas
          // E se não está mais na fase de planejamento (já iniciou o tratamento)
          const temMetas = this.paciente.metas_terapeuticas && this.paciente.metas_terapeuticas.length > 0;
          const tratamentoIniciado = this.paciente.data_inicio_tratamento && this.paciente.data_inicio_tratamento.trim().length > 0;
          return temMetas && tratamentoIniciado;
        }

        default:
          return false;
      }
    },

    toggleEditMode(section) {
      this.isEditing[section] = !this.isEditing[section];
    },
    /**
     * Pause the carousel of images when an image is clicked. This helps
     * prevent the user from accidentally advancing to the next image while
     * they are viewing an image.
     */
    pauseCarousel() {
      this.isPaused = true;
    },
    resumeCarousel() {
      this.isPaused = false;
    },
    /**
     * Toggle the carousel between paused and playing states
     */
    toggleCarousel() {
      this.isPaused = !this.isPaused;
    },
    /**
     * Toggle the visibility of the carousel
     */
    toggleCarouselVisibility() {
      this.isCarouselHidden = !this.isCarouselHidden;
    },
    /**
     * Truncate a description to a specified length and add ellipsis if needed
     */
    truncateDescription(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    },


    // Método para lidar com o evento edit-mode-active do componente PlanoTratamento
    handleEditModeActive(isActive) {
      this.editModeActive = isActive;
      // Propagar o evento para o componente pai
      this.$emit('edit-mode-active', isActive);
    },

    // Setup sticky detection para desktop
    setupStickyDetection() {
      const phasesWrapper = document.querySelector('.phases-navigation-wrapper.sticky-phases');
      if (!phasesWrapper) return;

      // Usar scroll listener mais simples
      const handleScroll = () => {
        const rect = phasesWrapper.getBoundingClientRect();
        const isStuck = rect.top <= 0; // Quando chega no topo

        if (isStuck && !phasesWrapper.classList.contains('is-stuck')) {
          phasesWrapper.classList.add('is-stuck');
          console.log('Phases navigation is now stuck at top:', rect.top);
        } else if (!isStuck && phasesWrapper.classList.contains('is-stuck')) {
          phasesWrapper.classList.remove('is-stuck');
          console.log('Phases navigation is no longer stuck:', rect.top);
        }
      };

      window.addEventListener('scroll', handleScroll);

      // Cleanup no beforeUnmount
      this._scrollHandler = handleScroll;
    },
  },
  components: {
    Analise,
    Diagnostico,
    Imagens,
    PlanoTratamento,
    MaterialInput,
  },
  computed: {
    imagens() {
      const allImages = this.paciente.imagens.filter((imagem) => imagem.dir !== "profile_pic");

      // Separar imagens de diagnóstico das regulares
      const diagnosticImages = allImages.filter(img => img.is_diagnostico === true || img.is_diagnostico === 1);
      const regularImages = allImages.filter(img => img.is_diagnostico !== true && img.is_diagnostico !== 1);

      // Ordenar imagens de diagnóstico: Extra-bucais, Intra-bucais, Radiografias
      const orderedDiagnosticImages = [];

      // 1. Extra-bucais
      const extraBucais = diagnosticImages.filter(img => {
        const tag = img.tag_diagnostico || '';
        return tag.startsWith('extra_');
      });
      orderedDiagnosticImages.push(...extraBucais);

      // 2. Intra-bucais
      const intraBucais = diagnosticImages.filter(img => {
        const tag = img.tag_diagnostico || '';
        return tag.startsWith('intra_');
      });
      orderedDiagnosticImages.push(...intraBucais);

      // 3. Radiografias (por último)
      const radiografias = diagnosticImages.filter(img => {
        const tag = img.tag_diagnostico || '';
        return tag.startsWith('radio_');
      });
      orderedDiagnosticImages.push(...radiografias);

      // Retornar imagens de diagnóstico ordenadas + imagens regulares
      return [...orderedDiagnosticImages, ...regularImages];
    },

    // Computed properties para a barra de progresso
    safePatientImages() {
      return this.paciente?.imagens?.filter(img =>
        img.is_diagnostico === true || img.is_diagnostico === 1
      ) || [];
    },


    detalhesClinicos() {
      return this.paciente.detalhes_paciente
        ? this.paciente.detalhes_paciente.filter((detalhe) => detalhe.tipo == "clinico")
        : [];
    },
    // Computed property to determine if carousel should be dimmed
    shouldDimCarousel() {
      return this.planejamentoTab === 'imagens';
    },
    ultimaFase() {
      return this.paciente.fases_tratamento[this.paciente.fases_tratamento.length - 1]
        .data_fim;
    },
  },

  mounted() {
    // Add a global reference to the component for debugging
    window.planejamentoComponent = this;

    this.$nextTick(() => {
      // Garantir que a tab inicial (documentação inicial) esteja ativa
      this.planejamentoTab = 'imagens';

      // Initialize carousel state based on current tab
      this.isPaused = false;
      this.isCarouselHidden = this.planejamentoTab === 'imagens'; // 'imagens' é o valor da variável, mas representa a tab "Documentação inicial"

      // Add event listener to watch for tab changes and update carousel state
      this.$watch('planejamentoTab', (newTab) => {
        // When switching to Documentação inicial tab, dim the carousel
        if (newTab === 'imagens') {
          // Optional: automatically hide carousel when on Documentação inicial tab
          // this.isCarouselHidden = true;
        } else {
          // When switching away from Documentação inicial tab, ensure carousel is visible
          this.isCarouselHidden = false;
        }
      });

      // Setup sticky behavior detection (apenas desktop)
      if (window.innerWidth > 768) {
        this.setupStickyDetection();
      }
    });
  },
  beforeUnmount() {
    // Cleanup do scroll handler
    if (this._scrollHandler) {
      window.removeEventListener('scroll', this._scrollHandler);
    }
  },
};
</script>
