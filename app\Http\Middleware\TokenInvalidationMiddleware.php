<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;

class TokenInvalidationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            // Tentar obter o payload do token sem autenticação completa
            $token = JWTAuth::getToken();
            if (!$token) {
                return $next($request);
            }

            $payload = JWTAuth::getPayload($token);

            // Verificar se o token tem timestamp de geração
            if (!$payload->hasKey('iat')) {
                return $next($request);
            }

            $tokenIssuedAt = $payload->get('iat');
            $clinicaId = $payload->get('clinica')['id'] ?? null;

            // Verificar invalidação global primeiro
            $globalInvalidationTimestamp = Cache::get("token_invalidation:global");

            if ($globalInvalidationTimestamp && $tokenIssuedAt < $globalInvalidationTimestamp) {
                return response()->json([
                    'error' => 'Token inválido - sessão expirada',
                    'message' => 'Sua sessão foi invalidada. Faça login novamente.'
                ], 401);
            }

            // Verificar invalidação por clínica (se aplicável)
            if ($clinicaId) {
                $clinicaInvalidationTimestamp = Cache::get("token_invalidation:{$clinicaId}");

                if ($clinicaInvalidationTimestamp && $tokenIssuedAt < $clinicaInvalidationTimestamp) {
                    return response()->json([
                        'error' => 'Token inválido - sessão expirada',
                        'message' => 'Sua sessão foi invalidada. Faça login novamente.'
                    ], 401);
                }
            }

        } catch (JWTException $e) {
            // Se houver erro ao processar o token, continua normalmente
            // O middleware de autenticação JWT vai lidar com tokens inválidos
        }

        return $next($request);
    }
}
