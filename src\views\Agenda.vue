<template>
  <lumi-sidenav
    class="fixed-end lumi-sidenav"
    icon="mdi-calendar-month"
    v-if="showSidenav"
    :config="sidenavConfig"
    @action="handleSidenavAction"
  >
    <template #header>
      <!-- Calendário compacto para seleção de data -->
      <sidenav-compact-calendar
        v-model="selectedDate"
        :events="events"
        :is-loading="isLoading.consultas"
        @date-selected="onDateSelected"
        @month-changed="onMonthChanged"
      />

      <!-- Toggle compacto para visualização -->
      <sidenav-compact-toggle
        v-model="currentView"
        :date="selectedDate"
        :events="events"
        @view-changed="onViewChanged"
      />
    </template>
  </lumi-sidenav>
  <!-- <div
    class="bg-gradient-secondary shadow-success pt-1 pb-1"
  >
    <h6 class="text-white text-center ps-3">{{data}}</h6>
  </div> -->
  <div class="main-page-content calendar-container border-radius-2xl">
    <div class="row">
      <div class="col-12 position-relative">
          <!-- Mostrar loading enquanto carrega configurações iniciais -->
          <div v-if="isLoading.configuracoes" class="d-flex justify-content-center align-items-center" style="min-height: 500px;">
            <div class="text-center">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Carregando...</span>
              </div>
              <p class="mt-3 text-muted">Carregando agenda...</p>
            </div>
          </div>

          <!-- Calendário só renderiza após configurações carregadas -->
          <lumi-calendar
            v-else
            ref="lumiCalendar"
            :key="`calendar-${calendarForceRerender}`"
            :events="events"
            :loading="false"
            :config="cfg"
            :view="currentView"
            :consultorio-id="selectedConsultorioId"
            :date="selectedDate"
            @calendarClosed="void 0"
            @fetchEvents="void 0"
            @dateSelected="onDateSelected"
            @calendar:view-changed="onViewChanged"
          />
      </div>
    </div>
  </div>





  <!-- Componente ConsultaModal -->
  <ConsultaModal ref="consultaModal" @consulta-salva="forcarAtualizacaoConsultas" />
</template>

<script>
import LumiSidenav from "@/views/components/LumiSidenav/index.vue";
import SidenavCompactCalendar from "@/views/components/LumiSidenav/SidenavCompactCalendar.vue";
import SidenavCompactToggle from "@/views/components/LumiSidenav/SidenavCompactToggle.vue";
import ConsultaModal from "@/components/ConsultaModal.vue";
import moment from 'moment';
import { mapMutations, mapState, mapGetters } from "vuex";
import { getDentistas } from "@/services/dentistasService";
import { searchPacientes } from "@/services/pacientesService";
import { getConsultas, getConsulta, getConsultasByPeriodo } from "@/services/consultasService";
import { generateCalendarPDF } from "@/services/pdfService";
import cSwal from "@/utils/cSwal.js";

const cfg = {
  viewEvent: {
    icon: true,
    text: "Ver consulta",
  },
  // reportEvent: {
  //   icon: true,
  //   text: "Reagendar",
  // },
  openPatientEvent: {
    icon: true,
    text: "Abrir paciente",
  },
  searchPlaceholder: "",
  eventName: "",
  closeText: "",
  nativeDatepicker: true,
  todayButton: true,
  firstDayOfWeek: 1,
};

// Eventos de exemplo
const events = [
  // {
  //   date: "2025-05-15T12:00:00.000Z",
  //   comment: "comment",
  //   id: "cl32rbkjk1700101o53e3e3uhn",
  //   keywords: "Consulta paga",
  //   name: "Elizangela Alves de Morais",
  // },
  // {
  //   date: "2024-09-13T15:30:00.000Z",
  //   comment: "comment",
  //   id: "cl32rbkjk1700101o53e3e3uhn",
  //   keywords: "Consulta confirmada",
  //   name: "Angélica Ribeiro",
  // },
  // {
  //   date: "2024-09-13T13:00:00.000Z",
  //   comment: "comment",
  //   id: "cl32rbkjk1700101o53e3e3uhn",
  //   keywords: "Confirmação pendente",
  //   name: "Antônio Lopes",
  // },
  // //...
]

const data = new Date()

export default {
  name: "agenda",
  components: {
    LumiSidenav,
    SidenavCompactCalendar,
    SidenavCompactToggle,
    ConsultaModal,
  },
  computed: {
    ...mapState([
      "showSidenav",
    ]),
    ...mapState('agendaConfig', {
      isAgendaConfigLoaded: 'isLoaded'
    }),
    ...mapGetters('agendaConfig', [
      'consultorios',
      'selectedConsultorioId',
      'selectedConsultorio',
      'agendaConfig'
    ]),

    // Verificar se o calendário está pronto para ser renderizado
    isCalendarReady() {
      return this.isAgendaConfigLoaded &&
             !this.isLoading.configuracoes &&
             this.selectedConsultorioId &&
             this.consultorios.length > 0;
    },

    // Gerar botões dos consultórios dinamicamente
    consultorioButtons() {
      if (!this.consultorios || this.consultorios.length === 0) {
        return [{
          text: "Carregando...",
          icon: "hourglass_empty",
          iconType: "material",
          action: "loading",
          disabled: true
        }];
      }

      // Usar uma chave estável para evitar re-renderizações desnecessárias
      return this.consultorios.map(consultorio => {
        const isSelected = this.selectedConsultorioId === consultorio.id;
        const isLoadingThisConsultorio = isSelected && this.isLoading.consultorio;

        return {
          text: consultorio.nome || `Consultório ${consultorio.id}`,
          // Mostrar loading spinner se estiver carregando este consultório
          icon: isLoadingThisConsultorio ? "hourglass_empty" : (consultorio.icone || "fas fa-clinic-medical"),
          iconType: isLoadingThisConsultorio ? "material" : "fontawesome", // Usar FontAwesome para ícones dos consultórios
          action: "selectConsultorio",
          consultorioId: consultorio.id,
          variant: isSelected ? "primary" : "outline-secondary",
          color: isSelected ? consultorio.cor : null, // Usar cor do consultório quando selecionado
          active: isSelected, // Indicar se está ativo
          autoCollapse: false, // Não fechar o sidenav ao selecionar consultório
          // Adicionar classe para transição suave
          class: isSelected ? 'consultorio-selected' : 'consultorio-unselected',
          // Adicionar classe de loading se estiver carregando
          iconClass: isLoadingThisConsultorio ? 'loading-icon' : ''
        };
      });
    },

    // Nome do consultório selecionado para exibição
    selectedConsultorioName() {
      if (this.selectedConsultorio) {
        return this.selectedConsultorio.nome || `Consultório ${this.selectedConsultorio.id}`;
      }
      return 'Nenhum consultório selecionado';
    },

    // Configuração dinâmica do sidenav
    sidenavConfig() {
      return {
        groups: [
          {
            title: "CONSULTÓRIOS",
            buttons: this.consultorioButtons
          },
          {
            title: "AGENDA",
            buttons: [
              {
                text: "Agendar consulta",
                icon: "add",
                iconType: "material",
                action: "newAppointment"
              },
              {
                text: "Imprimir",
                icon: "print",
                iconType: "material",
                action: "print"
              }
            ]
          }
        ]
      };
    }
  },
  data() {
    return {
      date: new Date(),
      cfg,
      events,
      data,
      selectedDate: new Date(), // Data selecionada como objeto Date
      currentView: 'week', // Visualização atual (day, week, month)
      dentistas: [],
      pacientes: [],
      isLoading: {
        consultas: true, // Começar com o estado de carregamento ativo
        form: false,
        configuracoes: true, // Estado de carregamento das configurações
        consultorio: false // Estado de carregamento específico do consultório
      },
      initialLoad: true, // Flag para controlar o carregamento inicial
      calendarForceRerender: 0, // Contador para forçar re-renderização do calendário

      // Cache simples para consultas (apenas mês atual)
      consultasCache: null, // {month: string, data: Array, timestamp: number}
      currentMonth: null, // Mês atual sendo exibido (YYYY-MM)
      pollingInterval: null // Intervalo para polling automático
    };
  },
  async created() {
    // Restaurar último consultório selecionado do localStorage
    this.restaurarUltimoConsultorioSelecionado();

    // Carregar configurações primeiro para evitar re-renderização do calendário
    await this.garantirConfiguracoesCarregadas();

    // Carregar dados em paralelo para melhor performance
    await Promise.all([
      this.carregarDentistas(),
      this.carregarPacientes(),
      this.carregarConsultas()
    ]);
  },

  mounted() {
    // Adicionar event listener para o evento de visualização de consulta
    document.body.addEventListener('calendar.request.view', this.handleCalendarViewEvent);
    document.body.addEventListener('calendar.request.report', this.handleCalendarReportEvent);
    document.body.addEventListener('calendar.request.open_patient', this.handleCalendarOpenPatientEvent);

    // Adicionar event listener para cliques nas células do calendário
    document.addEventListener('calendar:cell-clicked', this.handleCalendarCellClick);

    // Iniciar sistema de cache automático para configurações da agenda
    console.log('🚀 Iniciando sistema de cache automático para configurações da agenda');
    this.$store.dispatch('agendaConfig/startCacheAutoUpdate');

    // Iniciar polling automático para consultas (60 segundos)
    this.iniciarPolling();
  },

  beforeUnmount() {
    // Limpar event listeners
    document.body.removeEventListener('calendar.request.view', this.handleCalendarViewEvent);
    document.body.removeEventListener('calendar.request.report', this.handleCalendarReportEvent);
    document.body.removeEventListener('calendar.request.open_patient', this.handleCalendarOpenPatientEvent);
    document.removeEventListener('calendar:cell-clicked', this.handleCalendarCellClick);

    // Parar polling
    this.pararPolling();

    // Parar sistema de cache automático
    console.log('🛑 Parando sistema de cache automático');
    this.$store.dispatch('agendaConfig/stopCacheAutoUpdate');
  },


  methods: {
    ...mapMutations(["navbarMinimize"]),

    /**
     * Selecionar um consultório e carregar suas configurações
     */
    async selecionarConsultorio(consultorioId) {
      try {
        console.log('🔄 Selecionando consultório:', consultorioId);

        // ⚡ MUDANÇA IMEDIATA - Limpar eventos da tela PRIMEIRO para feedback visual instantâneo
        this.events = [];

        // ⚡ MUDANÇA IMEDIATA - Selecionar o consultório no store
        // Isso faz o toggle mudar instantaneamente
        this.$store.commit('agendaConfig/SET_SELECTED_CONSULTORIO', consultorioId);

        // Salvar no localStorage para persistência
        this.salvarUltimoConsultorioSelecionado(consultorioId);

        // Ativar loading sutil no ícone do consultório
        this.isLoading.consultorio = true;

        // Agora fazer o carregamento em background
        // SEMPRE carregar configurações específicas do consultório usando cache inteligente
        console.log('📡 Carregando configurações para consultório (com cache):', consultorioId);
        const loadResult = await this.$store.dispatch('agendaConfig/loadAgendaConfigForConsultorioCached', { consultorioId });

        if (loadResult) {
          console.log('✅ Configurações carregadas da API com sucesso');
        } else {
          console.log('⚠️ Falha ao carregar configurações da API, usando padrão');
        }

        // Verificar configuração carregada
        const config = this.$store.getters['agendaConfig/agendaConfig'];
        console.log('📋 Configuração atual no store:', config);

        // Gerar time slots para o novo consultório
        this.$store.dispatch('agendaConfig/generateTimeSlotsForConsultorio', consultorioId);

        // O LumiCalendar agora detectará automaticamente a mudança de consultório
        // através do watcher que criamos, sem necessidade de forçar re-render

        // Recarregar consultas para o novo consultório
        await this.carregarConsultasDoMes(true); // Force reload para novo consultório

        console.log('✅ Consultório selecionado com sucesso:', consultorioId);
        console.log(`✅ Consultório alterado para: ${this.selectedConsultorioName}`);

      } catch (error) {
        console.error('❌ Erro ao selecionar consultório:', error);
        console.error('❌ Erro ao alterar consultório. Tente novamente.');
      } finally {
        // Desativar loading do consultório
        this.isLoading.consultorio = false;
      }
    },

    /**
     * Carregar consultas sem ativar o loading visual (para manter células visíveis)
     */
    async carregarConsultasSilencioso() {
      try {
        console.log('🔄 Carregando consultas silenciosamente...');

        // Determinar o período baseado na visualização atual
        let startDate, endDate;

        if (this.currentView === 'day') {
          // Para visualização diária, carregar apenas o dia selecionado
          startDate = moment(this.selectedDate).startOf('day').format('YYYY-MM-DD HH:mm:ss');
          endDate = moment(this.selectedDate).endOf('day').format('YYYY-MM-DD HH:mm:ss');
        } else if (this.currentView === 'week') {
          // Para visualização semanal, carregar a semana inteira
          startDate = moment(this.selectedDate).startOf('week').format('YYYY-MM-DD HH:mm:ss');
          endDate = moment(this.selectedDate).endOf('week').format('YYYY-MM-DD HH:mm:ss');
        } else if (this.currentView === 'month') {
          // Para visualização mensal, carregar o mês inteiro
          startDate = moment(this.selectedDate).startOf('month').format('YYYY-MM-DD HH:mm:ss');
          endDate = moment(this.selectedDate).endOf('month').format('YYYY-MM-DD HH:mm:ss');
        }

        console.log(`📅 Carregando consultas de ${startDate} até ${endDate} para consultório ${this.selectedConsultorioId}`);

        // Carregar consultas do período
        const response = await getConsultasByPeriodo(startDate, endDate, this.selectedConsultorioId);

        console.log(`Total de consultas carregadas da API: ${response ? response.length : 0}`);

        if (response && response.length > 0) {
          // Transformar os dados da API para o formato esperado pelo componente LumiCalendar
          this.events = response.map(consulta => {

            // O horário vem como "YYYY-MM-DD HH:MM:SS" do backend
            let dataHora;

            try {
              // Verificar se temos o campo horario
              if (consulta.horario) {
                // Converter o timestamp do MySQL para um objeto Date
                // Não adicionar 'Z' para evitar que a data seja interpretada como UTC
                // Isso preserva o fuso horário local (GMT-3)
                dataHora = moment(consulta.horario).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else if (consulta.date) {
                // Alternativa: usar o campo date se disponível
                dataHora = moment(consulta.date).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else if (consulta.data) {
                // Alternativa: usar o campo data se disponível
                dataHora = moment(consulta.data).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else {
                // Fallback para data atual se não houver nenhum campo de data
                console.error("Consulta sem campo de data/hora:", consulta);
                dataHora = moment().format('YYYY-MM-DDTHH:mm:ss.SSS');
              }
            } catch (e) {
              // Fallback para data atual se houver erro
              console.error("Erro ao formatar data/hora:", e, consulta);
              dataHora = moment().format('YYYY-MM-DDTHH:mm:ss.SSS');
            }

            // Verificar se temos o nome do paciente
            let nomePaciente = '';
            if (consulta.paciente_nome) {
              nomePaciente = consulta.paciente_nome;
            } else if (consulta.name) {
              nomePaciente = consulta.name;
            } else {
              console.warn('Consulta sem nome de paciente:', consulta);
              nomePaciente = 'Paciente não identificado';
            }

            // Verificar se temos o status da consulta
            let statusConsulta = 'agendada';
            if (consulta.status) {
              statusConsulta = consulta.status;
            } else if (consulta.keywords) {
              // Tentar extrair o status das keywords
              const match = consulta.keywords.match(/Consulta\s+(\w+)/i);
              if (match && match[1]) {
                statusConsulta = match[1].toLowerCase();
              }
            }

            // Verificar se temos o valor da consulta
            let valorConsulta = '0,00';
            if (consulta.valor) {
              valorConsulta = consulta.valor;
            } else if (consulta.comment) {
              // Tentar extrair o valor do comentário
              const match = consulta.comment.match(/R\$\s*([0-9,.]+)/);
              if (match && match[1]) {
                valorConsulta = match[1];
              }
            }

            // Criar o objeto de evento para o calendário
            const eventoCalendario = {
              id: consulta.id,
              date: dataHora,
              name: nomePaciente,
              keywords: `Consulta ${statusConsulta}`,
              comment: `Valor: R$ ${valorConsulta}`,
              // Armazenar dados adicionais que podem ser úteis
              _original: {
                paciente_id_ficha: consulta.paciente?.id_ficha || null,
                dentista_id: consulta.dentista_id,
                observacoes: consulta.observacoes,
                status: statusConsulta,
                valor: valorConsulta,
                confirmada: consulta.confirmada || false
              }
            };

            return eventoCalendario;
          });

          console.log(`✅ ${this.events.length} eventos criados para o calendário`);
        } else {
          console.log('ℹ️ Nenhuma consulta encontrada para o período');
          this.events = [];
        }
      } catch (error) {
        console.error('❌ Erro ao carregar consultas silenciosamente:', error);
        this.events = [];
      }
    },

    async garantirConfiguracoesCarregadas() {
      this.isLoading.configuracoes = true;

      try {
        // PASSO 1: Carregar consultórios PRIMEIRO (isso já restaura do localStorage)
        if (!this.$store.getters['agendaConfig/consultorios'] || this.$store.getters['agendaConfig/consultorios'].length === 0) {
          console.log('📋 Carregando consultórios...');
          await this.$store.dispatch('agendaConfig/loadConsultorios');
        }

        // PASSO 2: Verificar se há um consultório selecionado
        let selectedConsultorioId = this.$store.getters['agendaConfig/selectedConsultorioId'];
        console.log('🔍 Consultório selecionado após loadConsultorios:', selectedConsultorioId);

        // PASSO 3: Se não houver consultório selecionado, tentar restaurar do localStorage
        if (!selectedConsultorioId) {
          const consultorios = this.$store.getters['agendaConfig/consultorios'];
          if (consultorios && consultorios.length > 0) {
            // Tentar restaurar do localStorage
            const ultimoConsultorioId = this.obterUltimoConsultorioSelecionado();
            const consultorioExiste = ultimoConsultorioId && consultorios.some(c => c.id === ultimoConsultorioId);

            if (consultorioExiste) {
              console.log('📂 Restaurando último consultório do localStorage:', ultimoConsultorioId);
              this.$store.commit('agendaConfig/SET_SELECTED_CONSULTORIO', ultimoConsultorioId);
              selectedConsultorioId = ultimoConsultorioId;
            } else {
              console.log('🔄 Selecionando primeiro consultório disponível (fallback)...');
              this.$store.commit('agendaConfig/SET_SELECTED_CONSULTORIO', consultorios[0].id);
              selectedConsultorioId = consultorios[0].id;
              this.salvarUltimoConsultorioSelecionado(selectedConsultorioId);
            }
          }
        }

        // PASSO 4: Carregar configurações do token (em paralelo agora que já temos consultório)
        const usuariosService = (await import('@/services/usuariosService.js')).default;
        await usuariosService.loadAgendaConfigAfterLogin();

        // PASSO 5: Carregar configurações da agenda
        console.log('🔍 isAgendaConfigLoaded:', this.isAgendaConfigLoaded);
        console.log('🔍 selectedConsultorioId:', selectedConsultorioId);
        console.log('🔍 Configuração atual no store:', this.$store.getters['agendaConfig/agendaConfig']);

        if (!this.isAgendaConfigLoaded && selectedConsultorioId) {
          console.log('📡 Configurações da agenda não carregadas, tentando carregar...');

          // Tentar bulk load primeiro (mais eficiente)
          console.log('📦 Tentando bulk load de todas as configurações...');
          const bulkSuccess = await this.$store.dispatch('agendaConfig/loadAllAgendaConfigsBulk');

          if (!bulkSuccess) {
            console.log('⚠️ Bulk load falhou, tentando carregamento individual...');
            // Fallback para carregamento individual se bulk load falhar
            await this.$store.dispatch('agendaConfig/loadAgendaConfigForConsultorio', {
              consultorioId: selectedConsultorioId
            });
          }

          // Gerar time slots para o consultório selecionado
          console.log('⏰ Gerando time slots para consultório:', selectedConsultorioId);
          this.$store.dispatch('agendaConfig/generateTimeSlotsForConsultorio', selectedConsultorioId);

          // Verificar se a configuração foi carregada
          console.log('✅ Configuração após carregamento:', this.$store.getters['agendaConfig/agendaConfig']);
        } else if (selectedConsultorioId) {
          // Se já está carregado, apenas garantir que os time slots estão gerados
          console.log('✅ Configurações já carregadas, gerando time slots...');
          this.$store.dispatch('agendaConfig/generateTimeSlotsForConsultorio', selectedConsultorioId);
        } else {
          console.warn('⚠️ Nenhum consultório selecionado!');
        }

        console.log('✅ Configurações da agenda carregadas com sucesso');
      } catch (error) {
        console.error('Erro ao carregar configurações da agenda:', error);

        // Em caso de erro, tentar carregar configurações padrão
        try {
          console.log('Tentando carregar configurações padrão...');
          const selectedConsultorioId = this.$store.getters['agendaConfig/selectedConsultorioId'] || 1;
          this.$store.commit('agendaConfig/RESET_AGENDA_CONFIG_FOR_CONSULTORIO', selectedConsultorioId);
          this.$store.dispatch('agendaConfig/generateTimeSlotsForConsultorio', selectedConsultorioId);
        } catch (fallbackError) {
          console.error('Erro ao carregar configurações padrão:', fallbackError);
        }
      } finally {
        this.isLoading.configuracoes = false;
      }
    },

    // Método para fechar a sidenav
    closeSidenav() {
      // Verificar se a sidenav está aberta
      const sidenavElement = document.querySelector(".g-sidenav-show");
      if (sidenavElement && sidenavElement.classList.contains("g-sidenav-pinned")) {
        // Usar o método do Vuex para fechar a sidenav
        this.navbarMinimize();
      }
    },

    /**
     * Configura a detecção manual de mudança de visualização
     */
    setupViewChangeDetection() {
      // Adicionar event listeners aos botões de visualização
      setTimeout(() => {
        const toggleButtons = document.querySelectorAll('.calendar-toggle-btn');
        if (toggleButtons.length > 0) {
          console.log('Configurando detecção de mudança de visualização para', toggleButtons.length, 'botões');

          toggleButtons.forEach(button => {
            button.addEventListener('click', () => {
              // Dar um tempo para a mudança de visualização ser aplicada
              setTimeout(() => {
                this.detectCurrentView();
              }, 100);
            });
          });
        }
      }, 1000); // Aguardar 1 segundo para garantir que os botões estejam renderizados
    },

    /**
     * Detecta a visualização atual com base no botão ativo
     */
    detectCurrentView() {
      const activeButton = document.querySelector('.calendar-toggle-btn.active');
      if (activeButton) {
        const buttonText = activeButton.textContent.trim().toLowerCase();
        console.log('DETECÇÃO - Texto do botão ativo:', buttonText);

        let detectedView = this.currentView;

        if (buttonText.includes('dia')) {
          detectedView = 'day';
        } else if (buttonText.includes('semana')) {
          detectedView = 'week';
        } else if (buttonText.includes('mês') || buttonText.includes('mes')) {
          detectedView = 'month';
        }

        // Atualizar a propriedade currentView se for diferente
        if (this.currentView !== detectedView) {
          console.log(`DETECÇÃO - Atualizando visualização: de ${this.currentView} para ${detectedView}`);
          this.currentView = detectedView;
        }
      } else {
        console.log('DETECÇÃO - Nenhum botão de visualização ativo encontrado');
      }
    },

    async carregarDentistas() {
      try {
        const response = await getDentistas();
        if (response) {
          this.dentistas = response;
        }
      } catch (error) {
        console.error("Erro ao carregar dentistas:", error);
        cSwal.cError("Erro ao carregar a lista de dentistas.");
      }
    },

    async carregarPacientes() {
      try {
        const response = await searchPacientes();
        if (response) {
          this.pacientes = response;
        }
      } catch (error) {
        console.error("Erro ao carregar pacientes:", error);
        cSwal.cError("Erro ao carregar a lista de pacientes.");
      }
    },

    /**
     * Carrega consultas do mês inteiro com cache inteligente
     */
    async carregarConsultasDoMes(forceReload = false) {
      try {
        const dataAtual = this.selectedDate ? new Date(this.selectedDate) : new Date();
        const mesAtual = `${dataAtual.getFullYear()}-${String(dataAtual.getMonth() + 1).padStart(2, '0')}`;

        console.log('🗓️ Carregando consultas do mês:', mesAtual, 'Force reload:', forceReload);

        // Verificar cache simples (apenas mês atual, 5 minutos de duração)
        const cacheKey = `${mesAtual}-${this.selectedConsultorioId}`;
        const agora = Date.now();
        const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

        if (!forceReload && this.consultasCache &&
            this.consultasCache.month === cacheKey &&
            (agora - this.consultasCache.timestamp) < CACHE_DURATION) {
          console.log('📦 Usando consultas do cache para', mesAtual);
          this.events = this.consultasCache.data;
          this.isLoading.consultas = false;
          return;
        }

        // Calcular primeiro e último dia do mês
        const primeiroDiaDoMes = new Date(dataAtual.getFullYear(), dataAtual.getMonth(), 1);
        const ultimoDiaDoMes = new Date(dataAtual.getFullYear(), dataAtual.getMonth() + 1, 0);

        const formatoData = (data) => {
          return data.toISOString().split('T')[0];
        };

        const dataInicioStr = formatoData(primeiroDiaDoMes);
        const dataFimStr = formatoData(ultimoDiaDoMes);

        console.log(`📅 Carregando consultas do mês completo: ${dataInicioStr} até ${dataFimStr} para consultório ${this.selectedConsultorioId}`);

        // Carregar consultas do mês inteiro
        const response = await getConsultasByPeriodo(dataInicioStr, dataFimStr, this.selectedConsultorioId);

        console.log(`✅ Total de consultas carregadas da API: ${response ? response.length : 0}`);

        if (response) {
          // Transformar os dados da API para o formato esperado pelo componente LumiCalendar
          const eventosFormatados = response.map(consulta => {
            // O horário vem como "YYYY-MM-DD HH:MM:SS" do backend
            let dataHora;

            try {
              // Verificar se temos o campo horario
              if (consulta.horario) {
                // Converter o timestamp do MySQL para um objeto Date
                // Não adicionar 'Z' para evitar que a data seja interpretada como UTC
                // Isso preserva o fuso horário local (GMT-3)
                dataHora = moment(consulta.horario).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else if (consulta.date) {
                // Alternativa: usar o campo date se disponível
                dataHora = moment(consulta.date).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else if (consulta.data) {
                // Alternativa: usar o campo data se disponível
                dataHora = moment(consulta.data).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else {
                // Fallback para data atual se não houver nenhum campo de data
                console.error("Consulta sem campo de data/hora:", consulta);
                dataHora = moment().format('YYYY-MM-DDTHH:mm:ss.SSS');
              }
            } catch (e) {
              // Fallback para data atual se houver erro
              console.error("Erro ao formatar data/hora:", e, consulta);
              dataHora = moment().format('YYYY-MM-DDTHH:mm:ss.SSS');
            }

            // Verificar se temos o nome do paciente
            let nomePaciente = '';
            if (consulta.paciente_nome) {
              nomePaciente = consulta.paciente_nome;
            } else if (consulta.name) {
              nomePaciente = consulta.name;
            } else if (consulta.paciente && consulta.paciente.nome) {
              nomePaciente = consulta.paciente.nome;
            } else {
              console.warn('Consulta sem nome de paciente:', consulta);
              nomePaciente = 'Paciente não identificado';
            }

            // Verificar se temos o status da consulta
            let statusConsulta = 'agendada';
            if (consulta.status) {
              statusConsulta = consulta.status;
            } else if (consulta.keywords) {
              // Tentar extrair o status das keywords
              const match = consulta.keywords.match(/Consulta\s+(\w+)/i);
              if (match && match[1]) {
                statusConsulta = match[1].toLowerCase();
              }
            }

            // Verificar se temos o valor da consulta
            let valorConsulta = '0,00';
            if (consulta.valor) {
              valorConsulta = consulta.valor;
            } else if (consulta.comment) {
              // Tentar extrair o valor do comentário
              const match = consulta.comment.match(/R\$\s*([0-9,.]+)/);
              if (match && match[1]) {
                valorConsulta = match[1];
              }
            }

            // Criar o objeto de evento para o calendário (formato esperado pelo LumiCalendar)
            return {
              id: consulta.id,
              date: dataHora,
              name: nomePaciente,
              keywords: `Consulta ${statusConsulta}`,
              comment: `Valor: R$ ${valorConsulta}`,
              // Armazenar dados adicionais que podem ser úteis
              _original: {
                paciente_id_ficha: consulta.paciente?.id_ficha || null,
                dentista_id: consulta.dentista_id,
                observacoes: consulta.observacoes,
                status: statusConsulta,
                valor: valorConsulta
              }
            };
          });

          // Atualizar cache simples (substitui o anterior)
          this.consultasCache = {
            month: cacheKey,
            data: eventosFormatados,
            timestamp: agora
          };

          this.events = eventosFormatados;
          this.currentMonth = mesAtual;

          console.log(`🎯 Consultas do mês ${mesAtual} carregadas e cacheadas com sucesso!`);
        } else {
          this.events = [];
        }

        this.isLoading.consultas = false;

      } catch (error) {
        console.error("❌ Erro ao carregar consultas do mês:", error);
        this.events = [];
        this.isLoading.consultas = false;
        cSwal.cError("Erro ao carregar as consultas do mês.");
      }
    },

    async carregarConsultas() {
      // Redirecionar para o novo método de carregamento por mês
      return this.carregarConsultasDoMes();
    },



    /**
     * Detecta mudança de mês e recarrega consultas se necessário
     */
    async verificarMudancaMes() {
      const dataAtual = this.selectedDate ? new Date(this.selectedDate) : new Date();
      const mesAtual = `${dataAtual.getFullYear()}-${String(dataAtual.getMonth() + 1).padStart(2, '0')}`;

      // Se não temos mês atual definido ou se o mês mudou
      if (!this.currentMonth || this.currentMonth !== mesAtual) {
        console.log('📅 Mudança de mês detectada:', this.currentMonth || 'undefined', '->', mesAtual);
        this.isLoading.consultas = true;
        await this.carregarConsultasDoMes(true); // Force reload para novo mês
        return true; // Indica que houve mudança
      }
      return false; // Indica que não houve mudança
    },

    /**
     * Inicia polling automático para atualizar consultas
     */
    iniciarPolling() {
      console.log('⏰ Iniciando polling automático (60s)');
      this.pollingInterval = setInterval(async () => {
        try {
          console.log('🔄 Polling: Atualizando consultas...');
          await this.carregarConsultasDoMes(true); // Force reload no polling
        } catch (error) {
          console.error('❌ Erro no polling:', error);
        }
      }, 60000); // 60 segundos
    },

    /**
     * Para o polling automático
     */
    pararPolling() {
      if (this.pollingInterval) {
        console.log('⏹️ Parando polling automático');
        clearInterval(this.pollingInterval);
        this.pollingInterval = null;
      }
    },

    /**
     * Força atualização das consultas (usado em ações como salvar consulta)
     */
    async forcarAtualizacaoConsultas() {
      console.log('🔄 Forçando atualização das consultas...');
      this.isLoading.consultas = true;
      await this.carregarConsultasDoMes(true);
    },

    /**
     * Manipula mudança de mês no calendário da sidenav
     */
    async onMonthChanged(monthData) {
      console.log('📅 Mês alterado no calendário:', monthData);

      // Atualizar a data selecionada para o primeiro dia do novo mês
      const primeiroDia = new Date(monthData.date.getFullYear(), monthData.date.getMonth(), 1);
      this.selectedDate = primeiroDia;

      // Formatar a data para exibição
      let dataFormatada = moment(primeiroDia).locale("pt-br").format('dddd, D [de] MMMM [de] YYYY');
      this.data = dataFormatada;

      // Carregar consultas do novo mês imediatamente
      this.isLoading.consultas = true;
      await this.carregarConsultasDoMes(true); // Force reload para novo mês
    },

    async carregarConsultasAntigo() {
      // Método antigo mantido como backup
      try {
        const dataAtual = this.selectedDate ? new Date(this.selectedDate) : new Date();
        let dataInicio, dataFim;

        if (this.currentView === 'day') {
          // Para visualização de dia: apenas o dia atual
          dataInicio = new Date(dataAtual);
          dataInicio.setHours(0, 0, 0, 0);
          dataFim = new Date(dataAtual);
          dataFim.setHours(23, 59, 59, 999);
        } else if (this.currentView === 'week') {
          // Para visualização de semana: início da semana até fim da semana
          const diaSemana = dataAtual.getDay();
          dataInicio = new Date(dataAtual);
          dataInicio.setDate(dataAtual.getDate() - diaSemana);
          dataInicio.setHours(0, 0, 0, 0);
          dataFim = new Date(dataInicio);
          dataFim.setDate(dataInicio.getDate() + 6);
          dataFim.setHours(23, 59, 59, 999);
        } else {
          // Para visualização de mês: início do mês até fim do mês
          dataInicio = new Date(dataAtual.getFullYear(), dataAtual.getMonth(), 1);
          dataFim = new Date(dataAtual.getFullYear(), dataAtual.getMonth() + 1, 0, 23, 59, 59, 999);
        }

        // Formatar as datas para o formato esperado pela API (YYYY-MM-DD)
        const formatoData = (data) => {
          return data.toISOString().split('T')[0];
        };

        const dataInicioStr = formatoData(dataInicio);
        const dataFimStr = formatoData(dataFim);

        console.log(`Carregando consultas do período: ${dataInicioStr} até ${dataFimStr} para consultório ${this.selectedConsultorioId} (visualização: ${this.currentView})`);

        // Tentar usar a função otimizada por período, passando o consultório selecionado
        const response = await getConsultasByPeriodo(dataInicioStr, dataFimStr, this.selectedConsultorioId);

        console.log(`Total de consultas carregadas da API: ${response ? response.length : 0}`);

        if (response) {
          // Transformar os dados da API para o formato esperado pelo componente LumiCalendar
          this.events = response.map(consulta => {

            // O horário vem como "YYYY-MM-DD HH:MM:SS" do backend
            let dataHora;

            try {
              // Verificar se temos o campo horario
              if (consulta.horario) {
                // Converter o timestamp do MySQL para um objeto Date
                // Não adicionar 'Z' para evitar que a data seja interpretada como UTC
                // Isso preserva o fuso horário local (GMT-3)
                dataHora = moment(consulta.horario).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else if (consulta.date) {
                // Alternativa: usar o campo date se disponível
                dataHora = moment(consulta.date).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else if (consulta.data) {
                // Alternativa: usar o campo data se disponível
                dataHora = moment(consulta.data).format('YYYY-MM-DDTHH:mm:ss.SSS');
              } else {
                // Fallback para data atual se não houver nenhum campo de data
                console.error("Consulta sem campo de data/hora:", consulta);
                dataHora = moment().format('YYYY-MM-DDTHH:mm:ss.SSS');
              }
            } catch (e) {
              // Fallback para data atual se houver erro
              console.error("Erro ao formatar data/hora:", e, consulta);
              dataHora = moment().format('YYYY-MM-DDTHH:mm:ss.SSS');
            }

            // Verificar se temos o nome do paciente
            let nomePaciente = '';
            if (consulta.paciente_nome) {
              nomePaciente = consulta.paciente_nome;
            } else if (consulta.name) {
              nomePaciente = consulta.name;
            } else {
              console.warn('Consulta sem nome de paciente:', consulta);
              nomePaciente = 'Paciente não identificado';
            }

            // Verificar se temos o status da consulta
            let statusConsulta = 'agendada';
            if (consulta.status) {
              statusConsulta = consulta.status;
            } else if (consulta.keywords) {
              // Tentar extrair o status das keywords
              const match = consulta.keywords.match(/Consulta\s+(\w+)/i);
              if (match && match[1]) {
                statusConsulta = match[1].toLowerCase();
              }
            }

            // Verificar se temos o valor da consulta
            let valorConsulta = '0,00';
            if (consulta.valor) {
              valorConsulta = consulta.valor;
            } else if (consulta.comment) {
              // Tentar extrair o valor do comentário
              const match = consulta.comment.match(/R\$\s*([0-9,.]+)/);
              if (match && match[1]) {
                valorConsulta = match[1];
              }
            }

            // Criar o objeto de evento para o calendário
            const eventoCalendario = {
              id: consulta.id,
              date: dataHora,
              name: nomePaciente,
              keywords: `Consulta ${statusConsulta}`,
              comment: `Valor: R$ ${valorConsulta}`,
              // Armazenar dados adicionais que podem ser úteis
              _original: {
                paciente_id_ficha: consulta.paciente?.id_ficha || null,
                dentista_id: consulta.dentista_id,
                observacoes: consulta.observacoes,
                status: statusConsulta,
                valor: valorConsulta
              }
            };

            return eventoCalendario;
          });

          console.log(`Total de eventos criados para o calendário: ${this.events.length}`);
        } else {
          this.events = [];
        }
      } catch (error) {
        console.error("Erro ao carregar consultas:", error);
        cSwal.cError("Erro ao carregar a lista de consultas.");
        this.events = [];
      } finally {
        // Desativar o estado de carregamento após carregar os dados
        this.isLoading.consultas = false;
      }
    },



    /**
     * Manipula o evento de visualização de consulta do calendário
     */
    async handleCalendarViewEvent(event) {
      console.log('Evento de visualização recebido:', event.detail);

      // Verificar se o evento contém um ID válido
      if (!event.detail || !event.detail.id) {
        console.error('Evento de visualização sem ID válido:', event.detail);
        cSwal.cError("Erro ao identificar a consulta. Por favor, tente novamente.");
        return;
      }

      // Obter o ID da consulta
      const consultaId = event.detail.id;
      console.log('ID da consulta extraído do evento:', consultaId);

      // Buscar os dados da consulta
      await this.carregarConsultaParaEdicao(consultaId);
    },

    /**
     * Manipula o evento de relatório de consulta do calendário
     */
    handleCalendarReportEvent(event) {
      console.log('Evento de relatório recebido:', event.detail);
      // Implementação futura para relatórios
    },

    /**
     * Manipula o evento de abrir paciente do calendário
     */
    async handleCalendarOpenPatientEvent(event) {
      console.log('Evento de abrir paciente recebido:', event.detail);

      // Verificar se o evento contém um ID válido
      if (!event.detail || !event.detail.id) {
        console.error('Evento de abrir paciente sem ID válido:', event.detail);
        cSwal.cError("Erro ao identificar a consulta. Por favor, tente novamente.");
        return;
      }

      // Obter o ID da consulta
      const consultaId = event.detail.id;
      console.log('ID da consulta extraído do evento:', consultaId);

      try {
        // Buscar os dados da consulta para obter o ID do paciente
        const consulta = await getConsulta(consultaId);
        console.log('Resposta da API para consulta:', consulta);

        if (!consulta) {
          console.error('Consulta não encontrada ou retornou dados vazios');
          cSwal.cError("Erro ao carregar dados da consulta. Por favor, tente novamente.");
          return;
        }

        // Verificar se a consulta é um objeto
        if (typeof consulta !== 'object') {
          console.error('Resposta da API não é um objeto:', consulta);
          cSwal.cError("Erro: Formato de resposta inválido. Por favor, tente novamente.");
          return;
        }

        // Verificar/extrair o ID do paciente
        let pacienteIdFicha = null;

        if (consulta.paciente.id_ficha) {
          pacienteIdFicha = consulta.paciente.id_ficha;
        } else {
          // Tentar extrair do evento do calendário
          const eventoCalendario = this.events.find(e => e.id == consultaId);
          if (eventoCalendario && eventoCalendario._original && eventoCalendario._original.paciente.id_ficha) {
            pacienteIdFicha = eventoCalendario._original.paciente.id_ficha;
          } else if (eventoCalendario && eventoCalendario.name) {
            // Tentar encontrar o ID do paciente pelo nome
            const pacienteEncontrado = this.pacientes.find(p => p.nome === eventoCalendario.name);
            if (pacienteEncontrado) {
              pacienteIdFicha = pacienteEncontrado.id_ficha;
            }
          }
        }

        if (!pacienteIdFicha) {
          console.error('Não foi possível determinar o ID do paciente:', consulta);
          cSwal.cError("Erro: Não foi possível identificar o paciente. Por favor, tente novamente.");
          return;
        }

        // Navegar para a página do paciente usando helper
        console.log('Navegando para a página do paciente:', pacienteIdFicha);
        const { getPacienteUrl } = require('@/helpers/patientUrlHelper');
        const targetUrl = getPacienteUrl(consulta.paciente);
        this.$router.push(targetUrl);
      } catch (error) {
        console.error("Erro ao abrir paciente:", error);
        cSwal.cError("Erro ao abrir a página do paciente. Por favor, tente novamente.");
      }
    },

    /**
     * Carrega os dados da consulta para edição
     */
    async carregarConsultaParaEdicao(consultaId) {
      try {
        console.log('Iniciando carregamento da consulta para edição, ID:', consultaId);
        this.isLoading.form = true;

        // Buscar os dados da consulta
        const consulta = await getConsulta(consultaId);
        console.log('Resposta da API para consulta:', consulta);

        if (!consulta) {
          console.error('Consulta não encontrada ou retornou dados vazios');
          cSwal.cError("Erro ao carregar dados da consulta. Por favor, tente novamente.");
          return;
        }

        // Verificar se a consulta é um objeto
        if (typeof consulta !== 'object') {
          console.error('Resposta da API não é um objeto:', consulta);
          cSwal.cError("Erro: Formato de resposta inválido. Por favor, tente novamente.");
          return;
        }

        // Verificar se temos os dados necessários para a consulta
        // Primeiro, vamos tentar encontrar o horário da consulta
        let horarioConsulta = null;

        // Verificar diferentes formatos possíveis para o horário
        if (consulta.horario) {
          console.log('Horário encontrado diretamente:', consulta.horario);
          horarioConsulta = consulta.horario;
        } else if (consulta.date) {
          console.log('Usando campo date como horário:', consulta.date);
          horarioConsulta = consulta.date;
        } else if (consulta.data) {
          console.log('Usando campo data como horário:', consulta.data);
          horarioConsulta = consulta.data;
        } else {
          // Tentar extrair do evento do calendário
          const eventoCalendario = this.events.find(e => e.id == consultaId);
          if (eventoCalendario && eventoCalendario.date) {
            console.log('Usando data do evento do calendário:', eventoCalendario.date);
            horarioConsulta = eventoCalendario.date;
          }
        }

        if (!horarioConsulta) {
          console.error('Não foi possível determinar o horário da consulta:', consulta);
          cSwal.cError("Erro: Não foi possível determinar o horário da consulta. Por favor, tente novamente.");
          return;
        }

        // Extrair a data e o horário do timestamp
        console.log('Horário da consulta a ser usado:', horarioConsulta);
        const dataHora = moment(horarioConsulta);

        if (!dataHora.isValid()) {
          console.error('Data/hora inválida:', horarioConsulta);
          cSwal.cError("Erro: Data/hora inválida. Por favor, tente novamente.");
          return;
        }

        console.log('Data/hora parseada:', dataHora.format('YYYY-MM-DD HH:mm:ss'));

        // Verificar/extrair o ID do paciente
        let pacienteId = null;
        let pacienteNome = '';

        if (consulta.paciente_id) {
          pacienteId = consulta.paciente_id;
          pacienteNome = consulta.paciente_nome || '';
        } else if (consulta.name) {
          // Se temos apenas o nome do paciente do evento do calendário
          pacienteNome = consulta.name;

          // Tentar encontrar o ID do paciente pelo nome
          const pacienteEncontrado = this.pacientes.find(p => p.nome === consulta.name);
          if (pacienteEncontrado) {
            pacienteId = pacienteEncontrado.id;
          }
        }

        if (!pacienteId && !pacienteNome) {
          // Tentar extrair do evento do calendário
          const eventoCalendario = this.events.find(e => e.id == consultaId);
          if (eventoCalendario && eventoCalendario.name) {
            pacienteNome = eventoCalendario.name;

            // Tentar encontrar o ID do paciente pelo nome
            const pacienteEncontrado = this.pacientes.find(p => p.nome === eventoCalendario.name);
            if (pacienteEncontrado) {
              pacienteId = pacienteEncontrado.id;
            }
          }
        }

        // Extrair valor da consulta
        let valorConsulta = '';
        if (consulta.valor !== undefined) {
          valorConsulta = consulta.valor;
        } else if (consulta.comment) {
          // Tentar extrair o valor do comentário (ex: "Valor: R$ 100,00")
          const match = consulta.comment.match(/R\$\s*([0-9,.]+)/);
          if (match && match[1]) {
            valorConsulta = match[1].replace(',', '.');
          }
        }

        // Não precisamos mais preencher o formulário de edição manualmente
        // O componente ConsultaModal vai buscar os dados da consulta

        // Usar o componente ConsultaModal para editar a consulta
        console.log('Abrindo modal de edição via componente ConsultaModal');
        this.$refs.consultaModal.abrirModalEditarConsulta(consultaId);
        console.log('Modal aberto via componente');

      } catch (error) {
        console.error("Erro ao carregar consulta para edição:", error);
        cSwal.cError("Erro ao carregar dados da consulta. Por favor, tente novamente.");
      } finally {
        this.isLoading.form = false;
      }
    },



    /**
     * Atualiza a visualização atual do calendário
     */
    onViewChanged(view) {
      // Só atualizar se a visualização realmente mudou (evitar loops)
      if (view !== this.currentView) {
        console.log('EVENTO RECEBIDO - Visualização alterada para:', view);
        console.log('Visualização anterior:', this.currentView);
        this.currentView = view;
        console.log('Visualização atualizada para:', this.currentView);

        // Para mudança de visualização, não precisamos recarregar consultas
        // pois já temos o mês inteiro carregado. O LumiCalendar se encarrega da visualização
        console.log('✅ Visualização alterada - consultas já estão carregadas para o mês');

        // Exibir alerta para confirmar a mudança de visualização (apenas para depuração)
        // alert(`Visualização alterada para: ${view}`);
      }
    },

    /**
     * Atualiza a data selecionada no calendário
     */
    async onDateSelected(dateSelected) {
      const newDate = new Date(dateSelected);

      console.log('🎯 onDateSelected chamado:', {
        dateSelected,
        newDate: newDate.toISOString(),
        currentSelectedDate: this.selectedDate ? this.selectedDate.toISOString() : 'null',
        currentMonth: this.currentMonth
      });

      // Só atualizar se a data realmente mudou (evitar loops)
      const datasMudaram = !this.selectedDate || newDate.getTime() !== this.selectedDate.getTime();
      console.log('🔍 Verificando mudança de data:', {
        selectedDateExists: !!this.selectedDate,
        selectedDateTime: this.selectedDate ? this.selectedDate.getTime() : null,
        newDateTime: newDate.getTime(),
        datasMudaram
      });

      if (datasMudaram) {
        // Armazenar a data selecionada como objeto Date
        this.selectedDate = newDate;

        // Formatar a data para exibição
        // Usar locale() em vez de lang() que está depreciado
        let dataFormatada = moment(dateSelected).locale("pt-br").format('dddd, D [de] MMMM [de] YYYY')
        this.data = dataFormatada

        // Apenas carregar consultas se for o primeiro carregamento
        if (this.initialLoad || !this.events || this.events.length === 0) {
          console.log('🔄 Primeiro carregamento, carregando consultas...');
          this.isLoading.consultas = true;
          await this.carregarConsultasDoMes(true);
        }

        // Desativar a flag após qualquer interação do usuário
        if (this.initialLoad) {
          this.initialLoad = false;
        }
      }
    },

    /**
     * Handle actions from the sidenav
     */
    handleSidenavAction(action, button) {
      console.log(`Action: ${action}`, button);

      // Implementar as ações da sidenav
      switch (action) {
        case 'selectConsultorio':
          this.selecionarConsultorio(button.consultorioId);
          break;
        case 'newAppointment':
          // Usar o componente ConsultaModal
          this.$refs.consultaModal.abrirModalNovaConsulta();
          break;
        case 'reschedule':
          // Lógica para reagendar
          alert('Funcionalidade: Reagendar');
          break;
        case 'print':
          // Lógica para imprimir
          this.printCalendar();
          break;
        case 'newRecord':
          // Lógica para criar novo prontuário
          alert('Funcionalidade: Novo prontuário');
          break;
        case 'payment':
          // Lógica para pagamento
          alert('Funcionalidade: Pagamento');
          break;
        case 'certificate':
          // Lógica para atestado
          alert('Funcionalidade: Atestado');
          break;
        case 'loading':
          // Não fazer nada para botões de loading
          break;
      }
    },

    /**
     * Generate a PDF of the current calendar view
     */
    async printCalendar() {
      try {
        // Verificar a visualização atual antes de gerar o PDF
        console.log('IMPRESSÃO - Visualização atual:', this.currentView);
        console.log('IMPRESSÃO - Botões de visualização:', document.querySelectorAll('.calendar-toggle-btn.active').length > 0 ?
          document.querySelector('.calendar-toggle-btn.active').textContent.trim() : 'Nenhum botão ativo');

        // Ativar o estado de carregamento (já deve estar ativo, mas garantimos aqui)
        this.isLoading.consultas = true;

        // Get the calendar element
        const calendarElement = document.querySelector('[data-widget-item="widget-calendar-comp"]');

        if (!calendarElement) {
          cSwal.cError("Não foi possível encontrar o calendário para impressão.");
          return;
        }

        // Detectar a visualização atual de duas formas
        let viewType = this.currentView;

        // Método alternativo: verificar qual botão de visualização está ativo
        const activeButton = document.querySelector('.calendar-toggle-btn.active');
        if (activeButton) {
          const buttonText = activeButton.textContent.trim().toLowerCase();
          console.log('Texto do botão ativo:', buttonText);

          if (buttonText.includes('dia')) {
            viewType = 'day';
          } else if (buttonText.includes('semana')) {
            viewType = 'week';
          } else if (buttonText.includes('mês') || buttonText.includes('mes')) {
            viewType = 'month';
          }

          // Atualizar a propriedade currentView se for diferente
          if (this.currentView !== viewType) {
            console.log(`Corrigindo visualização: de ${this.currentView} para ${viewType}`);
            this.currentView = viewType;
          }
        }

        console.log('Tipo de visualização detectada:', viewType);

        // Filter events based on the current view
        let filteredEvents = [...this.events];

        // Prepare date variables for filtering
        const currentDay = moment(this.selectedDate).format('YYYY-MM-DD');
        const startOfWeek = moment(this.selectedDate).startOf('week');
        const endOfWeek = moment(this.selectedDate).endOf('week');
        const startOfMonth = moment(this.selectedDate).startOf('month');
        const endOfMonth = moment(this.selectedDate).endOf('month');

        // Filtrar eventos com base na visualização atual
        console.log(`Filtrando eventos para a visualização: ${viewType}`);

        if (viewType === 'day') {
          // Para visualização diária, incluir apenas eventos do dia atual
          filteredEvents = this.events.filter(event => {
            const eventDateStr = moment(event.date).format('YYYY-MM-DD');
            return eventDateStr === currentDay;
          });
          console.log(`Total de eventos para o dia ${currentDay}: ${filteredEvents.length}`);
        }
        else if (viewType === 'week') {
          // Para visualização semanal, incluir apenas eventos da semana atual
          filteredEvents = this.events.filter(event => {
            const eventDate = moment(event.date);
            return eventDate.isSameOrAfter(startOfWeek) && eventDate.isSameOrBefore(endOfWeek);
          });
          console.log(`Total de eventos para a semana (${startOfWeek.format('DD/MM/YYYY')} a ${endOfWeek.format('DD/MM/YYYY')}): ${filteredEvents.length}`);
        }
        else if (viewType === 'month') {
          // Para visualização mensal, incluir apenas eventos do mês atual
          filteredEvents = this.events.filter(event => {
            const eventDate = moment(event.date);
            return eventDate.isSameOrAfter(startOfMonth) && eventDate.isSameOrBefore(endOfMonth);
          });
          console.log(`Total de eventos para o mês de ${startOfMonth.format('MMMM/YYYY')}: ${filteredEvents.length}`);
        }

        // Se não houver eventos filtrados, exibir mensagem
        if (filteredEvents.length === 0) {
          console.log('Nenhum evento encontrado para o período selecionado.');
        }

        // Log para depuração
        console.log('Total de eventos antes da filtragem:', this.events.length);
        console.log('Total de eventos após a filtragem:', filteredEvents.length);
        console.log('Eventos filtrados:', filteredEvents);

        // Verificar a visualização uma última vez antes de gerar o PDF
        this.detectCurrentView();

        // Usar a visualização mais recente
        if (this.currentView !== viewType) {
          console.log(`Atualizando visualização antes de gerar PDF: de ${viewType} para ${this.currentView}`);
          viewType = this.currentView;

          // Refiltragem dos eventos com a visualização correta
          if (viewType === 'day') {
            filteredEvents = this.events.filter(event => {
              const eventDateStr = moment(event.date).format('YYYY-MM-DD');
              return eventDateStr === currentDay;
            });
          } else if (viewType === 'week') {
            filteredEvents = this.events.filter(event => {
              const eventDate = moment(event.date);
              return eventDate.isSameOrAfter(startOfWeek) && eventDate.isSameOrBefore(endOfWeek);
            });
          } else if (viewType === 'month') {
            filteredEvents = this.events.filter(event => {
              const eventDate = moment(event.date);
              return eventDate.isSameOrAfter(startOfMonth) && eventDate.isSameOrBefore(endOfMonth);
            });
          }
        }

        // Generate the PDF and open in new tab
        await generateCalendarPDF(calendarElement, viewType, this.selectedDate, filteredEvents, true);

        // Show success message
        let mensagem = '';
        if (viewType === 'day') {
          mensagem = `O PDF com as consultas do dia ${moment(this.selectedDate).format('DD/MM/YYYY')} foi aberto em uma nova guia!`;
        } else if (viewType === 'week') {
          const inicio = moment(startOfWeek).format('DD/MM');
          const fim = moment(endOfWeek).format('DD/MM/YYYY');
          mensagem = `O PDF com as consultas da semana (${inicio} a ${fim}) foi aberto em uma nova guia!`;
        } else {
          const mes = moment(this.selectedDate).locale('pt-br').format('MMMM/YYYY');
          mensagem = `O PDF com as consultas do mês de ${mes} foi aberto em uma nova guia!`;
        }
        cSwal.cSuccess(mensagem);
      } catch (error) {
        console.error("Erro ao gerar PDF:", error);
        cSwal.cError("Erro ao gerar o PDF. Por favor, tente novamente.");
      } finally {
        // Hide loading indicator
        this.isLoading.consultas = false;
      }
    },

    /**
     * Método para abrir modal de nova consulta com data e horário pré-preenchidos
     */
    abrirModalConsultaComHorario(data, horario = null) {
      console.log('Abrindo modal de consulta com:', { data, horario });
      this.$refs.consultaModal.abrirModalNovaConsultaComHorario(data, horario);
    },

    /**
     * Manipula cliques nas células do calendário
     */
    handleCalendarCellClick(event) {
      console.log('Clique na célula do calendário:', event.detail);

      const { date, time, view } = event.detail;

      // Converter a data para o formato correto se necessário
      let dataFormatada = date;
      if (typeof date === 'string') {
        dataFormatada = new Date(date);
      }

      // Abrir modal com data e horário pré-preenchidos
      if (view === 'month') {
        // Para visualização mensal, apenas a data (sem horário específico)
        this.abrirModalConsultaComHorario(dataFormatada);
      } else {
        // Para visualizações diária e semanal, incluir o horário
        this.abrirModalConsultaComHorario(dataFormatada, time);
      }
    },

    /**
     * Salvar o último consultório selecionado no localStorage
     */
    salvarUltimoConsultorioSelecionado(consultorioId) {
      try {
        localStorage.setItem('agenda_ultimo_consultorio', consultorioId.toString());
        console.log('💾 Último consultório salvo no localStorage:', consultorioId);
      } catch (error) {
        console.warn('⚠️ Erro ao salvar último consultório no localStorage:', error);
      }
    },

    /**
     * Obter o último consultório selecionado do localStorage
     */
    obterUltimoConsultorioSelecionado() {
      try {
        const consultorioId = localStorage.getItem('agenda_ultimo_consultorio');
        if (consultorioId) {
          const id = parseInt(consultorioId, 10);
          console.log('📂 Último consultório recuperado do localStorage:', id);
          return id;
        }
      } catch (error) {
        console.warn('⚠️ Erro ao obter último consultório do localStorage:', error);
      }
      return null;
    },

    /**
     * Restaurar o último consultório selecionado (chamado no created)
     */
    restaurarUltimoConsultorioSelecionado() {
      const ultimoConsultorioId = this.obterUltimoConsultorioSelecionado();
      if (ultimoConsultorioId) {
        console.log('🔄 Tentando restaurar último consultório selecionado:', ultimoConsultorioId);
        // A seleção real será feita em garantirConfiguracoesCarregadas
        // após os consultórios serem carregados
      }
    }
  }
};
</script>

<style scoped>
/* Modal styling */
.modal-content {
  border-radius: 0.75rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: none;
}

.modal-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
}

.modal-header .modal-title {
  font-weight: 600;
  font-size: 1.25rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1rem 1.5rem;
}

/* Form styling */
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.input-group {
  margin-bottom: 0.5rem;
  border-radius: 0.5rem;
  overflow: hidden;
}

.input-group-text {
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  color: #6c757d;
}

.form-control, .form-select {
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button styling */
.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.btn-primary:hover {
  background-color: #0069d9;
  border-color: #0062cc;
  transform: translateX(2px);
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: transform 0.2s ease, opacity 0.2s ease;
}

.btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
  transform: translateX(2px);
}

/* Helper text */
small.text-muted {
  display: block;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  color: #6c757d;
}

/* Date and time input styling */
.date-time-input {
  font-size: 1rem !important;
  font-weight: 500;
}

/* Input MD customization */
.input-md {
  height: calc(var(--lumi-input-height) + 5px) !important;
  font-size: 0.95rem !important;
}

/* Relative time badge */
.relative-time-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(var(--lumi-input-height) + 5px);
  padding: 6px 12px;
  border-radius: 6px;
  background-color: #1470e9;
  color: #EEE;
  font-weight: 500;
  font-size: 0.95rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  width: 100%;
}

/* Observações textarea */
.observacoes-textarea {
  resize: none;
  min-height: 80px;
  font-size: 0.9rem !important;
  line-height: 1.4;
  border-color: #ced4da;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.observacoes-textarea:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Status button group styling */
.btn-group .btn {
  font-size: 0.85rem;
  padding: 0.375rem 0.5rem;
  transition: transform 0.2s ease, opacity 0.2s ease;
  border-width: 1px;
}

.status-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 0.35rem !important;
  padding-bottom: 0.35rem !important;
}

.status-icon {
  margin-bottom: 3px;
  font-size: 1rem;
}

.btn-group .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Mobile status buttons in footer */
.mobile-status-group {
  margin-top: -0.5rem;
}

.mobile-status-group .btn {
  font-size: 0.8rem;
  padding: 0.3rem 0.5rem;
  height: 38px;
}

@keyframes fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(3px);
  }
}

@keyframes float-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* PDF printing styles */
@media print {
  .calendar-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .calendar-view-container {
    page-break-inside: avoid;
  }

  .lumi-sidenav,
  .navbar,
  .footer {
    display: none !important;
  }
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
  .modal-dialog {
    margin: 0.5rem auto;
    max-width: 92%;
    height: auto;
  }

  .modal-dialog-scrollable .modal-content {
    max-height: 85vh;
  }

  .modal-header {
    padding: 0.75rem 1rem;
  }

  .modal-body {
    padding: 0.75rem;
    overflow-y: auto;
    max-height: calc(85vh - 130px); /* Subtract header and footer height */
  }

  .modal-footer {
    padding: 0.75rem 1rem;
  }

  .btn-group .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.35rem;
    height: auto;
  }

  .status-icon {
    display: none;
  }

  .input-md {
    height: calc(var(--lumi-input-height) + 2px) !important;
    font-size: 0.9rem !important;
  }

  .relative-time-badge {
    height: calc(var(--lumi-input-height) + 2px);
    font-size: 0.85rem;
  }

  .observacoes-textarea {
    min-height: 60px;
    font-size: 0.85rem !important;
  }

}

/* Estilos para o header do consultório */
.consultorio-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.consultorio-header h5 {
  color: #495057;
  font-weight: 600;
}

.consultorio-header .material-icons {
  font-size: 1.5rem;
}

.consultorio-header .spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Transições suaves para os botões de consultório */
:deep(.nav-btn-container) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

:deep(.nav-btn) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  will-change: background-color, color, box-shadow, transform;
}

:deep(.consultorio-selected .nav-btn) {
  transform: scale(1.02);
}

:deep(.consultorio-unselected .nav-btn) {
  transform: scale(1);
}

/* Evitar "flash" durante a transição */
:deep(.nav-btn[style*="background-color"]) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Suavizar mudanças de cor do ícone e texto */
:deep(.nav-btn .material-icons-round),
:deep(.nav-btn .fa-solid),
:deep(.nav-btn .v-icon),
:deep(.nav-btn span) {
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Animação de loading para o ícone do consultório */
@keyframes pulse-rotate {
  0% {
    transform: rotate(0deg);
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: rotate(360deg);
    opacity: 0.6;
  }
}

:deep(.loading-icon .material-icons-round) {
  animation: pulse-rotate 1.5s ease-in-out infinite;
  display: inline-block;
}
</style>
