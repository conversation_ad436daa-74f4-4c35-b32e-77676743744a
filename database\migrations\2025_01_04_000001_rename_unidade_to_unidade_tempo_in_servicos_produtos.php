<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Primeiro, atualizar valores existentes de 'un' para 'minutos'
        DB::table('servicos_produtos')
            ->where('unidade', 'un')
            ->update(['unidade' => 'minutos']);

        // Depois, renomear a coluna e alterar o default
        DB::statement('ALTER TABLE `servicos_produtos` CHANGE `unidade` `unidade_tempo` VARCHAR(20) NOT NULL DEFAULT "minutos"');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverter valores de 'minutos' para 'un'
        DB::table('servicos_produtos')
            ->where('unidade_tempo', 'minutos')
            ->update(['unidade_tempo' => 'un']);

        // Reverter renomeação e default
        DB::statement('ALTER TABLE `servicos_produtos` CHANGE `unidade_tempo` `unidade` VARCHAR(20) NOT NULL DEFAULT "un"');
    }
};

