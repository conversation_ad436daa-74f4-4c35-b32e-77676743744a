<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Imagem;
use App\Models\Paciente;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;

class GenerateImagePreviews extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:generate-previews
                            {--force : Regenerar previews mesmo para imagens que já possuem}
                            {--limit= : Limitar o número de imagens a processar}
                            {--dry-run : Simular execução sem fazer alterações}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Gera previews (thumbnails) para todas as imagens que ainda não possuem';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');
        $limit = $this->option('limit');
        $dryRun = $this->option('dry-run');

        $this->info('🖼️  Iniciando geração de previews de imagens...');
        $this->newLine();

        if ($dryRun) {
            $this->warn('⚠️  MODO DRY-RUN: Nenhuma alteração será feita');
            $this->newLine();
        }

        // Processar imagens da tabela 'imagens'
        $this->processImagens($force, $limit, $dryRun);

        // Processar fotos de perfil de pacientes
        $this->processProfilePictures($force, $limit, $dryRun);

        $this->newLine();
        $this->info('✅ Processo concluído!');
    }

    /**
     * Processar imagens da tabela 'imagens'
     */
    private function processImagens($force, $limit, $dryRun)
    {
        $this->info('📸 Processando imagens da tabela "imagens"...');

        $query = Imagem::query();

        if (!$force) {
            $query->where('has_preview', false);
        }

        if ($limit) {
            $query->limit((int)$limit);
        }

        $imagens = $query->get();
        $total = $imagens->count();

        if ($total === 0) {
            $this->warn('   Nenhuma imagem encontrada para processar.');
            $this->newLine();
            return;
        }

        $this->info("   Encontradas {$total} imagens para processar");
        $this->newLine();

        $bar = $this->output->createProgressBar($total);
        $bar->start();

        $success = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($imagens as $imagem) {
            try {
                // Decodificar a URL para obter o caminho relativo
                $decodedUrl = urldecode(urldecode($imagem->url));
                $imagePath = 'images/' . $decodedUrl;

                // Verificar se a imagem original existe
                if (!Storage::disk('local')->exists($imagePath)) {
                    $this->newLine();
                    $this->warn("   ⚠️  Imagem não encontrada: {$imagePath}");
                    $skipped++;
                    $bar->advance();
                    continue;
                }

                // Verificar se já existe preview (se não for force)
                $previewPath = 'images/preview/' . $decodedUrl;
                if (!$force && Storage::disk('local')->exists($previewPath)) {
                    $skipped++;
                    $bar->advance();
                    continue;
                }

                if (!$dryRun) {
                    // Gerar o preview
                    $this->generatePreview($imagePath, $previewPath);

                    // Atualizar o banco de dados
                    $imagem->has_preview = true;
                    $imagem->save();
                }

                $success++;
            } catch (\Exception $e) {
                $this->newLine();
                $this->error("   ❌ Erro ao processar imagem ID {$imagem->id}: " . $e->getMessage());
                $errors++;
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->newLine();

        $this->info("   ✅ Sucesso: {$success}");
        if ($skipped > 0) {
            $this->warn("   ⏭️  Ignoradas: {$skipped}");
        }
        if ($errors > 0) {
            $this->error("   ❌ Erros: {$errors}");
        }
        $this->newLine();
    }

    /**
     * Processar fotos de perfil de pacientes
     */
    private function processProfilePictures($force, $limit, $dryRun)
    {
        $this->info('👤 Processando fotos de perfil de pacientes...');

        $query = Paciente::whereNotNull('profile_picture_url');

        if (!$force) {
            $query->where('profile_picture_has_preview', false);
        }

        if ($limit) {
            $query->limit((int)$limit);
        }

        $pacientes = $query->get();
        $total = $pacientes->count();

        if ($total === 0) {
            $this->warn('   Nenhuma foto de perfil encontrada para processar.');
            $this->newLine();
            return;
        }

        $this->info("   Encontradas {$total} fotos de perfil para processar");
        $this->newLine();

        $bar = $this->output->createProgressBar($total);
        $bar->start();

        $success = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($pacientes as $paciente) {
            try {
                // Decodificar a URL para obter o caminho relativo
                $decodedUrl = urldecode(urldecode($paciente->profile_picture_url));
                $imagePath = 'images/' . $decodedUrl;

                // Verificar se a imagem original existe
                if (!Storage::disk('local')->exists($imagePath)) {
                    $this->newLine();
                    $this->warn("   ⚠️  Foto de perfil não encontrada: {$imagePath}");
                    $skipped++;
                    $bar->advance();
                    continue;
                }

                // Verificar se já existe preview (se não for force)
                $previewPath = 'images/preview/' . $decodedUrl;
                if (!$force && Storage::disk('local')->exists($previewPath)) {
                    $skipped++;
                    $bar->advance();
                    continue;
                }

                if (!$dryRun) {
                    // Gerar o preview
                    $this->generatePreview($imagePath, $previewPath);

                    // Atualizar o banco de dados
                    $paciente->profile_picture_has_preview = true;
                    $paciente->save();
                }

                $success++;
            } catch (\Exception $e) {
                $this->newLine();
                $this->error("   ❌ Erro ao processar paciente ID {$paciente->id}: " . $e->getMessage());
                $errors++;
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->newLine();

        $this->info("   ✅ Sucesso: {$success}");
        if ($skipped > 0) {
            $this->warn("   ⏭️  Ignoradas: {$skipped}");
        }
        if ($errors > 0) {
            $this->error("   ❌ Erros: {$errors}");
        }
        $this->newLine();
    }

    /**
     * Gerar preview de uma imagem
     */
    private function generatePreview($originalPath, $previewPath)
    {
        // Carregar a imagem original
        $img = Image::make(Storage::disk('local')->path($originalPath));

        // Aplicar orientação automática baseada nos dados EXIF
        $img->orientate();

        // Redimensionar mantendo proporção (200px no lado maior)
        $img->resize(200, 200, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });

        // Salvar o preview
        Storage::disk('local')->put($previewPath, $img->encode(null, 85));
    }
}
