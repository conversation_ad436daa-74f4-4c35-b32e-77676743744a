<?php

namespace App\Http\Controllers;

use App\Models\ServicoProduto;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ServicoProdutoController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $query = ServicoProduto::daClinica($clinicaId);

        // Filtros

        if ($request->has('tipo')) {
            $query->porTipo($request->tipo);
        }

        if ($request->has('ativo')) {
            if ($request->ativo == '1') {
                $query->ativos();
            } else {
                $query->where('ativo', false);
            }
        }

        if ($request->has('busca')) {
            $query->buscar($request->busca);
        }

        $servicosProdutos = $query->orderBy('nome')
                                 ->paginate(20);

        return responseSuccess(['data' => $servicosProdutos]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $validator = Validator::make($request->all(), [
            'dentista_id' => 'nullable|exists:dentistas,id',
            'codigo' => 'nullable|string|max:50|unique:servicos_produtos,codigo,NULL,id,clinica_id,' . $clinicaId,
            'nome' => 'required|string|max:255',
            'descricao' => 'nullable|string',
            'tipo' => 'required|in:servico,produto,procedimento',
            'valor_base' => 'required|numeric|min:0.01',
            'valor_minimo' => 'nullable|numeric|min:0',
            'valor_maximo' => 'nullable|numeric|min:0',
            'tempo_estimado' => 'nullable|integer|min:1',
            'unidade_tempo' => 'nullable|string|max:20',
            'observacoes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        // Validar se valor_minimo <= valor_base <= valor_maximo
        if ($request->valor_minimo && $request->valor_minimo > $request->valor_base) {
            return responseError([
                'message' => 'Valor mínimo não pode ser maior que o valor base',
                'statusCode' => 422
            ]);
        }

        if ($request->valor_maximo && $request->valor_maximo < $request->valor_base) {
            return responseError([
                'message' => 'Valor máximo não pode ser menor que o valor base',
                'statusCode' => 422
            ]);
        }

        try {
            $servicoProduto = ServicoProduto::create([
                'clinica_id' => $clinicaId,
                'dentista_id' => $request->dentista_id,
                'codigo' => $request->codigo,
                'nome' => $request->nome,
                'descricao' => $request->descricao,
                'tipo' => $request->tipo,
                'valor_base' => $request->valor_base,
                'valor_minimo' => $request->valor_minimo,
                'valor_maximo' => $request->valor_maximo,
                'tempo_estimado' => $request->tempo_estimado,
                'unidade_tempo' => $request->unidade_tempo ?? 'minutos',
                'observacoes' => $request->observacoes,
                'ativo' => true,
            ]);

            return responseSuccess([
                'message' => 'Serviço/Produto criado com sucesso',
                'data' => $servicoProduto
            ]);

        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao criar serviço/produto: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $servicoProduto = ServicoProduto::daClinica($clinicaId)
            ->findOrFail($id);

        return responseSuccess(['data' => $servicoProduto]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $servicoProduto = ServicoProduto::daClinica($clinicaId)->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'dentista_id' => 'nullable|exists:dentistas,id',
            'codigo' => 'nullable|string|max:50|unique:servicos_produtos,codigo,' . $id . ',id,clinica_id,' . $clinicaId,
            'nome' => 'sometimes|string|max:255',
            'descricao' => 'nullable|string',
            'tipo' => 'sometimes|in:servico,produto,procedimento',
            'valor_base' => 'sometimes|numeric|min:0.01',
            'valor_minimo' => 'nullable|numeric|min:0',
            'valor_maximo' => 'nullable|numeric|min:0',
            'tempo_estimado' => 'nullable|integer|min:1',
            'unidade_tempo' => 'nullable|string|max:20',
            'observacoes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return responseError([
                'message' => 'Dados inválidos',
                'data' => $validator->errors(),
                'statusCode' => 422
            ]);
        }

        // Validar faixa de valores
        $valorBase = $request->get('valor_base', $servicoProduto->valor_base);
        $valorMinimo = $request->get('valor_minimo', $servicoProduto->valor_minimo);
        $valorMaximo = $request->get('valor_maximo', $servicoProduto->valor_maximo);

        if ($valorMinimo && $valorMinimo > $valorBase) {
            return responseError([
                'message' => 'Valor mínimo não pode ser maior que o valor base',
                'statusCode' => 422
            ]);
        }

        if ($valorMaximo && $valorMaximo < $valorBase) {
            return responseError([
                'message' => 'Valor máximo não pode ser menor que o valor base',
                'statusCode' => 422
            ]);
        }

        try {
            $servicoProduto->update($request->only([
                'codigo', 'nome', 'descricao', 'tipo',
                'valor_base', 'valor_minimo', 'valor_maximo',
                'tempo_estimado', 'unidade_tempo', 'observacoes'
            ]));

            return responseSuccess([
                'message' => 'Serviço/Produto atualizado com sucesso',
                'data' => $servicoProduto
            ]);

        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao atualizar serviço/produto: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $servicoProduto = ServicoProduto::daClinica($clinicaId)->findOrFail($id);

        // Verificar se está sendo usado em orçamentos
        if ($servicoProduto->getQuantidadeUsadaOrcamentos() > 0) {
            return responseError([
                'message' => 'Este serviço/produto não pode ser excluído pois está sendo usado em orçamentos',
                'statusCode' => 422
            ]);
        }

        try {
            $servicoProduto->delete();

            return responseSuccess(['message' => 'Serviço/Produto excluído com sucesso']);
        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao excluir serviço/produto: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Ativar/Desativar serviço/produto
     */
    public function toggleStatus(string $id)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $servicoProduto = ServicoProduto::daClinica($clinicaId)->findOrFail($id);

        try {
            if ($servicoProduto->ativo) {
                $servicoProduto->desativar();
                $message = 'Serviço/Produto desativado com sucesso';
            } else {
                $servicoProduto->ativar();
                $message = 'Serviço/Produto ativado com sucesso';
            }

            return responseSuccess([
                'message' => $message,
                'data' => $servicoProduto->fresh()
            ]);

        } catch (\Exception $e) {
            return responseError([
                'message' => 'Erro ao alterar status: ' . $e->getMessage(),
                'statusCode' => 500
            ]);
        }
    }

    /**
     * Obter tipos disponíveis
     */
    public function tipos()
    {
        return responseSuccess(['data' => ServicoProduto::getTipos()]);
    }

    /**
     * Buscar serviços/produtos para orçamento
     */
    public function buscarParaOrcamento(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $query = ServicoProduto::daClinica($clinicaId)
            ->ativos();

        if ($request->has('busca')) {
            $query->buscar($request->busca);
        }

        if ($request->has('tipo')) {
            $query->porTipo($request->tipo);
        }

        $servicos = $query->orderBy('nome')
                         ->limit(50)
                         ->get();

        return responseSuccess(['data' => $servicos]);
    }

    /**
     * Importar lote de serviços/produtos
     */
    public function importarLote(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $validator = Validator::make($request->all(), [
            'itens' => 'required|array|min:1',
            'itens.*.nome' => 'required|string|max:255',
            'itens.*.tipo' => 'required|in:servico,produto,procedimento',
            'itens.*.valor_base' => 'required|numeric|min:0.01',
        ]);

        if ($validator->fails()) {
            return responseError('Dados inválidos', $validator->errors(), 422);
        }

        $itens = $request->input('itens');
        $sucesso = 0;
        $erros = 0;
        $mensagensErro = [];

        foreach ($itens as $index => $item) {
            try {
                // Verificar se já existe um item com o mesmo código
                if (!empty($item['codigo'])) {
                    $existe = ServicoProduto::daClinica($clinicaId)
                        ->where('codigo', $item['codigo'])
                        ->exists();

                    if ($existe) {
                        $erros++;
                        $mensagensErro[] = "Linha " . ($index + 1) . ": Código '{$item['codigo']}' já existe";
                        continue;
                    }
                }

                // Normalizar campo ativo para inteiro (0 ou 1)
                $ativo = 1;
                if (isset($item['ativo'])) {
                    if (is_bool($item['ativo'])) {
                        $ativo = $item['ativo'] ? 1 : 0;
                    } else {
                        $ativo = $item['ativo'] ? 1 : 0;
                    }
                }

                // Criar o serviço/produto
                ServicoProduto::create([
                    'clinica_id' => $clinicaId,
                    'codigo' => $item['codigo'] ?? null,
                    'nome' => $item['nome'],
                    'descricao' => $item['descricao'] ?? null,
                    'tipo' => $item['tipo'],
                    'valor_base' => $item['valor_base'],
                    'valor_minimo' => $item['valor_minimo'] ?? null,
                    'valor_maximo' => $item['valor_maximo'] ?? null,
                    'tempo_estimado' => $item['tempo_estimado'] ?? null,
                    'unidade_tempo' => $item['unidade_tempo'] ?? 'minutos',
                    'observacoes' => $item['observacoes'] ?? null,
                    'ativo' => $ativo,
                ]);

                $sucesso++;
            } catch (\Exception $e) {
                $erros++;
                $mensagensErro[] = "Linha " . ($index + 1) . ": " . $e->getMessage();
            }
        }

        return responseSuccess([
            'data' => [
                'sucesso' => $sucesso,
                'erros' => $erros,
                'mensagens_erro' => $mensagensErro
            ]
        ]);
    }

    /**
     * Exportar serviços/produtos para Excel
     */
    public function exportar(Request $request)
    {
        $user = auth()->payload();
        $clinicaId = $user['clinica']['id'];

        $query = ServicoProduto::daClinica($clinicaId);

        // Aplicar filtros
        if ($request->has('tipo')) {
            $query->porTipo($request->tipo);
        }

        if ($request->has('ativo')) {
            if ($request->ativo == '1') {
                $query->ativos();
            } else {
                $query->where('ativo', false);
            }
        }

        if ($request->has('busca')) {
            $query->buscar($request->busca);
        }

        $servicosProdutos = $query->orderBy('nome')->get();

        // Criar planilha Excel
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Cabeçalhos
        $headers = [
            'Código',
            'Nome',
            'Descrição',
            'Tipo',
            'Valor Base',
            'Valor Mínimo',
            'Valor Máximo',
            'Tempo Estimado',
            'Unidade Tempo',
            'Observações',
            'Ativo'
        ];

        $sheet->fromArray($headers, null, 'A1');

        // Estilizar cabeçalhos
        $headerStyle = [
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID, 'startColor' => ['rgb' => '007bff']],
            'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER],
        ];
        $sheet->getStyle('A1:K1')->applyFromArray($headerStyle);

        // Dados
        $row = 2;
        foreach ($servicosProdutos as $item) {
            $sheet->fromArray([
                $item->codigo ?? '',
                $item->nome,
                $item->descricao ?? '',
                $item->tipo,
                $item->valor_base,
                $item->valor_minimo ?? '',
                $item->valor_maximo ?? '',
                $item->tempo_estimado ?? '',
                $item->unidade_tempo ?? 'minutos',
                $item->observacoes ?? '',
                $item->ativo ? 'Sim' : 'Não'
            ], null, 'A' . $row);
            $row++;
        }

        // Ajustar largura das colunas
        foreach (range('A', 'K') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Gerar arquivo
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $fileName = 'tabela-precos-' . date('Y-m-d') . '.xlsx';

        $temp_file = tempnam(sys_get_temp_dir(), $fileName);
        $writer->save($temp_file);

        return response()->download($temp_file, $fileName)->deleteFileAfterSend(true);
    }
}
