<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

// Agendar processamento de ciclos de assinatura diariamente às 8h
// Schedule::command('assinaturas:processar-ciclos')->dailyAt('08:00');

// Agendar geração de faturas diariamente às 9h
// Schedule::command('faturas:gerar-clinicas')->dailyAt('09:00');
