<template>
  <div class="modal fade" id="modalFinanceiro" tabindex="-1" aria-labelledby="modalFinanceiroLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="modalFinanceiroLabel">
            {{ isEditing ? 'Editar Fatura' : 'Nova Fatura' }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveFatura">
            <div class="row g-3">
              <!-- Paciente -->
              <div class="col-md-6">
                <label class="form-label">Paciente *</label>
                <select class="form-select" v-model="form.paciente_id" :class="{ 'is-invalid': errors.paciente_id }">
                  <option value="">Selecione um paciente</option>
                  <option v-for="paciente in pacientes" :key="paciente.id" :value="paciente.id">
                    {{ paciente.nome }}
                  </option>
                </select>
                <div class="invalid-feedback" v-if="errors.paciente_id">
                  {{ errors.paciente_id }}
                </div>
              </div>

              <!-- Dentista -->
              <div class="col-md-6">
                <label class="form-label">Profissional</label>
                <select class="form-select" v-model="form.dentista_id">
                  <option value="">Selecione um profissional</option>
                  <option v-for="dentista in dentistas" :key="dentista.id" :value="dentista.id">
                    {{ dentista.nome }}
                  </option>
                </select>
              </div>

              <!-- Descrição -->
              <div class="col-12">
                <label class="form-label">Descrição *</label>
                <input type="text" class="form-control" v-model="form.descricao" 
                       :class="{ 'is-invalid': errors.descricao }"
                       placeholder="Descrição da fatura">
                <div class="invalid-feedback" v-if="errors.descricao">
                  {{ errors.descricao }}
                </div>
              </div>

              <!-- Valor Nominal -->
              <div class="col-md-4">
                <label class="form-label">Valor Nominal *</label>
                <div class="input-group">
                  <span class="input-group-text">R$</span>
                  <input type="number" step="0.01" class="form-control" v-model="form.valor_nominal"
                         :class="{ 'is-invalid': errors.valor_nominal }"
                         @input="calculateFinalValue">
                </div>
                <div class="invalid-feedback" v-if="errors.valor_nominal">
                  {{ errors.valor_nominal }}
                </div>
              </div>

              <!-- Data de Vencimento -->
              <div class="col-md-4">
                <label class="form-label">Data de Vencimento *</label>
                <input type="date" class="form-control" v-model="form.data_vencimento"
                       :class="{ 'is-invalid': errors.data_vencimento }">
                <div class="invalid-feedback" v-if="errors.data_vencimento">
                  {{ errors.data_vencimento }}
                </div>
              </div>

              <!-- Parcelas -->
              <div class="col-md-4">
                <label class="form-label">Número de Parcelas</label>
                <input type="number" min="1" max="60" class="form-control" v-model="form.parcelas_total"
                       :class="{ 'is-invalid': errors.parcelas_total }">
                <div class="invalid-feedback" v-if="errors.parcelas_total">
                  {{ errors.parcelas_total }}
                </div>
              </div>

              <!-- Descontos -->
              <div class="col-md-6">
                <label class="form-label">Desconto</label>
                <div class="row g-2">
                  <div class="col-6">
                    <div class="input-group">
                      <input type="number" step="0.01" class="form-control" v-model="form.percentual_desconto"
                             placeholder="%" @input="calculateFinalValue">
                      <span class="input-group-text">%</span>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="input-group">
                      <span class="input-group-text">R$</span>
                      <input type="number" step="0.01" class="form-control" v-model="form.valor_desconto"
                             @input="calculateFinalValue">
                    </div>
                  </div>
                </div>
              </div>

              <!-- Acréscimos -->
              <div class="col-md-6">
                <label class="form-label">Acréscimo</label>
                <div class="row g-2">
                  <div class="col-6">
                    <div class="input-group">
                      <input type="number" step="0.01" class="form-control" v-model="form.percentual_acrescimo"
                             placeholder="%" @input="calculateFinalValue">
                      <span class="input-group-text">%</span>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="input-group">
                      <span class="input-group-text">R$</span>
                      <input type="number" step="0.01" class="form-control" v-model="form.valor_acrescimo"
                             @input="calculateFinalValue">
                    </div>
                  </div>
                </div>
              </div>

              <!-- Valor Final -->
              <div class="col-md-6">
                <label class="form-label">Valor Final</label>
                <div class="input-group">
                  <span class="input-group-text">R$</span>
                  <input type="text" class="form-control bg-light" :value="formatCurrency(calculatedFinalValue)" readonly>
                </div>
              </div>

              <!-- Observações -->
              <div class="col-12">
                <label class="form-label">Observações</label>
                <textarea class="form-control" rows="3" v-model="form.observacoes"
                          placeholder="Observações adicionais"></textarea>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="button" class="btn btn-primary" @click="saveFatura" :disabled="saving">
            <span v-if="saving" class="spinner-border spinner-border-sm me-2" role="status"></span>
            {{ saving ? 'Salvando...' : (isEditing ? 'Atualizar' : 'Criar') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';
import { openModal, closeModal } from '@/utils/modalHelper';
import cSwal from '@/utils/cSwal.js';

export default {
  name: 'FinanceiroModal',
  data() {
    return {
      isEditing: false,
      saving: false,
      currentFaturaId: null,
      pacientes: [],
      dentistas: [],
      form: {
        paciente_id: '',
        dentista_id: '',
        descricao: '',
        valor_nominal: '',
        data_vencimento: '',
        parcelas_total: 1,
        percentual_desconto: '',
        valor_desconto: '',
        percentual_acrescimo: '',
        valor_acrescimo: '',
        observacoes: ''
      },
      errors: {}
    };
  },
  computed: {
    calculatedFinalValue() {
      return financeiroService.calculateFinalValue(
        this.form.valor_nominal,
        this.form.percentual_desconto,
        this.form.valor_desconto,
        this.form.percentual_acrescimo,
        this.form.valor_acrescimo
      );
    }
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,

    openCreate() {
      this.isEditing = false;
      this.currentFaturaId = null;
      this.resetForm();
      this.clearErrors();
      openModal('modalFinanceiro');
    },

    openEdit(fatura) {
      this.isEditing = true;
      this.currentFaturaId = fatura.id;
      this.fillForm(fatura);
      this.clearErrors();
      openModal('modalFinanceiro');
    },

    resetForm() {
      this.form = {
        paciente_id: '',
        dentista_id: '',
        descricao: '',
        valor_nominal: '',
        data_vencimento: '',
        parcelas_total: 1,
        percentual_desconto: '',
        valor_desconto: '',
        percentual_acrescimo: '',
        valor_acrescimo: '',
        observacoes: ''
      };
    },

    fillForm(fatura) {
      this.form = {
        paciente_id: fatura.paciente_id || '',
        dentista_id: fatura.dentista_id || '',
        descricao: fatura.descricao || '',
        valor_nominal: fatura.valor_nominal || '',
        data_vencimento: fatura.data_vencimento || '',
        parcelas_total: fatura.parcelas_total || 1,
        percentual_desconto: fatura.percentual_desconto || '',
        valor_desconto: fatura.valor_desconto || '',
        percentual_acrescimo: fatura.percentual_acrescimo || '',
        valor_acrescimo: fatura.valor_acrescimo || '',
        observacoes: fatura.observacoes || ''
      };
    },

    clearErrors() {
      this.errors = {};
    },

    calculateFinalValue() {
      // O valor é calculado automaticamente via computed property
    },

    validateForm() {
      const validation = financeiroService.validateFaturaData(this.form);
      this.errors = validation.errors;
      return validation.isValid;
    },

    async saveFatura() {
      if (!this.validateForm()) {
        return;
      }

      this.saving = true;
      try {
        if (this.isEditing) {
          await financeiroService.updateFatura(this.currentFaturaId, this.form);
          cSwal.cSuccess('Fatura atualizada com sucesso');
        } else {
          await financeiroService.createFatura(this.form);
          cSwal.cSuccess('Fatura criada com sucesso');
        }

        closeModal('modalFinanceiro');
        this.$emit('saved');
      } catch (error) {
        console.error('Erro ao salvar fatura:', error);
        
        if (error.response && error.response.status === 422) {
          this.errors = error.response.data.data || {};
        } else {
          cSwal.cError('Erro ao salvar fatura');
        }
      } finally {
        this.saving = false;
      }
    },

    async loadPacientes() {
      try {
        // TODO: Implementar carregamento de pacientes
        // const response = await pacientesService.getPacientes();
        // this.pacientes = response.data.data;
      } catch (error) {
        console.error('Erro ao carregar pacientes:', error);
      }
    },

    async loadDentistas() {
      try {
        // TODO: Implementar carregamento de dentistas
        // const response = await dentistasService.getDentistas();
        // this.dentistas = response.data.data;
      } catch (error) {
        console.error('Erro ao carregar dentistas:', error);
      }
    }
  },

/*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Lifecycle hook: mounted
   *
   * Carrega os pacientes e dentistas assim que o componente for montado.

/*******  dcc7a9f9-11cf-4102-b02c-8de6d9ba4853  *******/
  mounted() {
    this.loadPacientes();
    this.loadDentistas();
  }
};
</script>

<style scoped>
.modal-content {
  border-radius: 15px;
}

.form-control:focus,
.form-select:focus {
  border-color: #5e72e4;
  box-shadow: 0 0 0 0.2rem rgba(94, 114, 228, 0.25);
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

.bg-light {
  background-color: #f8f9fa !important;
}

.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: block;
}
</style>
