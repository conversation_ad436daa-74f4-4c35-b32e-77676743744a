<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MentoriaMensagemImagem extends Model
{
    use HasFactory;

    protected $table = 'mentoria_mensagem_imagem';

    protected $fillable = [
        'mentoria_mensagem_id',
        'imagem_id',
        'observation',
    ];

    /**
     * Relacionamento com a mensagem
     */
    public function mensagem(): BelongsTo
    {
        return $this->belongsTo(MentoriaMensagem::class, 'mentoria_mensagem_id');
    }

    /**
     * Relacionamento com a imagem
     */
    public function imagem(): BelongsTo
    {
        return $this->belongsTo(Imagem::class, 'imagem_id');
    }
}

