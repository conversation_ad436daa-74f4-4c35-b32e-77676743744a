import axios from '@/services/axios'

export async function adicionarClinica(clinica) {
    try {
        const response = await axios.post('/clinicas', clinica)

        if (!response || !response.data)
            return false;

        return response.data

    } catch (error) {
        console.error('Erro ao adicionar clínica:', error);

        // Retornar erro específico se disponível
        if (error.response && error.response.data && error.response.data.message) {
            throw new Error(error.response.data.message);
        }

        throw error;
    }
}

export async function getClinicas() {
    try {
        const response = await axios.get('/clinicas')

        if (!response || !response.data)
            return false;

        return response.data

    } catch (error) {
        console.error('Erro ao consultar clínicas:', error);
    }

    return false

}

export async function getClinicasWithCounts() {
    try {
        const response = await axios.get('/clinicas/with-counts')

        if (!response || !response.data)
            return false;

        // Mapear users_count para usuarios_count para compatibilidade com o front-end
        const clinicas = response.data.map(clinica => ({
            ...clinica,
            usuarios_count: clinica.users_count
        }));

        return clinicas

    } catch (error) {
        console.error('Erro ao consultar clínicas com contadores:', error);
    }

    return false

}

export async function searchClinicas(search = '') {
    try {
        const response = await axios.post('/clinicas/search', {
            search: search
        })

        if (!response || !response.data)
            return [];

        return response.data

    } catch (error) {
        console.error('Erro ao buscar clínicas:', error);
    }

    return []

}

export async function getClinica(id) {
    try {
        const response = await axios.get(`/clinicas/${id}`)

        if (!response || !response.data)
            return false;

        return response.data

    } catch (error) {
        console.error('Erro ao consultar clínica:', error);
    }

    return false

}

export async function updateClinica(clinica) {
    try {
        const response = await axios.put(`/clinicas/${clinica.id}`, clinica)

        if (!response || response.status !== 200)
            return false;

        return response.data

    } catch (error) {
        console.error('Erro ao atualizar clínica:', error);
    }

    return false
}

export async function updateClinicaField(clinicaId, field, value) {
    try {
        const data = {};
        data[field] = value;

        const response = await axios.patch(`/clinicas/${clinicaId}`, data);

        if (!response || response.status !== 200) {
            return false;
        }

        return response.data;
    } catch (error) {
        console.error('Erro ao atualizar campo da clínica:', error);
        return false;
    }
}

export async function createTrialAccount(trialData) {
    try {
        const response = await axios.post('/trial-signup', trialData);

        if (!response || !response.data)
            return { success: false, message: 'Resposta inválida do servidor' };

        return response.data;

    } catch (error) {
        console.error('Erro ao criar conta trial:', error);

        if (error.response && error.response.data) {
            const errorData = error.response.data;

            // Se houver erros de validação específicos
            if (errorData.errors) {
                // Formatar mensagens de erro de validação
                const errorMessages = [];

                for (const [field, messages] of Object.entries(errorData.errors)) {
                    // Traduzir campos para português
                    const fieldTranslations = {
                        'dentista.email': 'E-mail',
                        'dentista.nome': 'Nome do profissional',
                        'dentista.senha': 'Senha',
                        'clinica.nome': 'Nome da clínica',
                        'plano_id': 'Plano'
                    };

                    const fieldName = fieldTranslations[field] || field;

                    // Traduzir mensagens de erro comuns
                    messages.forEach(msg => {
                        if (msg.includes('has already been taken')) {
                            errorMessages.push(`${fieldName} já está em uso`);
                        } else if (msg.includes('required')) {
                            errorMessages.push(`${fieldName} é obrigatório`);
                        } else if (msg.includes('invalid')) {
                            errorMessages.push(`${fieldName} é inválido`);
                        } else {
                            errorMessages.push(msg);
                        }
                    });
                }

                throw new Error(errorMessages.join('. '));
            }

            // Se houver mensagem de erro genérica
            if (errorData.message) {
                throw new Error(errorData.message);
            }
        }

        throw error;
    }
}
