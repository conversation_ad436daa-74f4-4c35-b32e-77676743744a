<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Clinica;
use App\Models\Consultorio;
use App\Models\Consulta;
use Illuminate\Support\Facades\DB;

class MigrarConsultoriosClinicas extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'consultorios:migrar-clinicas';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cria consultórios padrão para clínicas que ainda não possuem e vincula consultas existentes';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🏥 Iniciando migração de consultórios...');
        $this->newLine();

        // Buscar todas as clínicas
        $clinicas = Clinica::all();
        $totalClinicas = $clinicas->count();

        if ($totalClinicas === 0) {
            $this->warn('⚠️  Nenhuma clínica encontrada no sistema.');
            return 0;
        }

        $this->info("📊 Total de clínicas encontradas: {$totalClinicas}");
        $this->newLine();

        $clinicasSemConsultorio = 0;
        $consultoriosCriados = 0;
        $consultasVinculadas = 0;

        $progressBar = $this->output->createProgressBar($totalClinicas);
        $progressBar->start();

        foreach ($clinicas as $clinica) {
            // Verificar se a clínica já possui consultórios
            $consultoriosExistentes = Consultorio::withoutGlobalScopes()
                ->where('clinica_id', $clinica->id)
                ->count();

            if ($consultoriosExistentes > 0) {
                // Clínica já possui consultórios, pular
                $progressBar->advance();
                continue;
            }

            $clinicasSemConsultorio++;

            // Usar transação para garantir consistência
            DB::beginTransaction();

            try {
                // Criar consultório padrão
                $consultorio = Consultorio::withoutGlobalScopes()->create([
                    'clinica_id' => $clinica->id,
                    'nome' => 'Consultório 1',
                    'descricao' => 'Consultório padrão',
                    'cor' => '#0674e9', // Azul escuro primary
                    'icone' => 'fas fa-clinic-medical', // Ícone de clínica
                    'ativo' => true,
                    'ordem' => 1,
                ]);

                $consultoriosCriados++;

                // Buscar todas as consultas "soltas" desta clínica (sem consultorio_id)
                // Precisamos fazer isso através dos pacientes da clínica
                $consultasSoltas = Consulta::whereNull('consultorio_id')
                    ->whereHas('paciente', function ($query) use ($clinica) {
                        $query->where('clinica_id', $clinica->id);
                    })
                    ->get();

                // Vincular todas as consultas ao novo consultório
                $quantidadeConsultas = $consultasSoltas->count();
                
                if ($quantidadeConsultas > 0) {
                    foreach ($consultasSoltas as $consulta) {
                        $consulta->update(['consultorio_id' => $consultorio->id]);
                    }
                    
                    $consultasVinculadas += $quantidadeConsultas;
                }

                DB::commit();

            } catch (\Exception $e) {
                DB::rollBack();
                $this->newLine();
                $this->error("❌ Erro ao processar clínica {$clinica->nome} (ID: {$clinica->id}): {$e->getMessage()}");
                continue;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // Exibir resumo
        $this->info('✅ Migração concluída!');
        $this->newLine();
        
        $this->table(
            ['Métrica', 'Quantidade'],
            [
                ['Total de clínicas', $totalClinicas],
                ['Clínicas sem consultório', $clinicasSemConsultorio],
                ['Consultórios criados', $consultoriosCriados],
                ['Consultas vinculadas', $consultasVinculadas],
            ]
        );

        if ($clinicasSemConsultorio === 0) {
            $this->info('🎉 Todas as clínicas já possuem consultórios cadastrados!');
        } else {
            $this->info("🎉 {$consultoriosCriados} consultórios criados com sucesso!");
            $this->info("📋 {$consultasVinculadas} consultas vinculadas aos novos consultórios!");
        }

        return 0;
    }
}

