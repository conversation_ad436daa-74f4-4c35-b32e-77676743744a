<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MentoriaMensagem;
use App\Models\Imagem;
use Illuminate\Support\Facades\DB;

class MigrateMentoriaMensagensImagens extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mentoria:migrate-images
                            {--dry-run : Simula a migração sem fazer alterações}
                            {--limit= : Limita o número de mensagens a processar}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migra imagens antigas do campo JSON "imagens" para a tabela pivot "mentoria_mensagem_imagem"';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $limit = $this->option('limit');

        $this->info('🔄 Iniciando migração de imagens de mensagens de mentoria...');
        $this->newLine();

        if ($dryRun) {
            $this->warn('⚠️  MODO DRY-RUN: Nenhuma alteração será feita no banco de dados');
            $this->newLine();
        }

        // Buscar mensagens que têm imagens no formato JSON antigo
        $query = MentoriaMensagem::whereNotNull('imagens')
            ->where('imagens', '!=', '[]')
            ->where('imagens', '!=', 'null');

        if ($limit) {
            $query->limit((int)$limit);
        }

        $mensagens = $query->get();
        $totalMensagens = $mensagens->count();

        if ($totalMensagens === 0) {
            $this->info('✅ Nenhuma mensagem com imagens antigas encontrada!');
            return 0;
        }

        $this->info("📊 Encontradas {$totalMensagens} mensagens com imagens no formato antigo");
        $this->newLine();

        $processadas = 0;
        $erros = 0;
        $imagensProcessadas = 0;
        $imagensNaoEncontradas = 0;

        $progressBar = $this->output->createProgressBar($totalMensagens);
        $progressBar->start();

        foreach ($mensagens as $mensagem) {
            try {
                // Decodificar o JSON de imagens
                $imagensJson = $mensagem->imagens;

                if (!is_array($imagensJson) || empty($imagensJson)) {
                    $progressBar->advance();
                    continue;
                }

                $imagensParaMigrar = [];
                $imagensInvalidas = [];

                // Validar cada imagem
                foreach ($imagensJson as $imagemData) {
                    if (!isset($imagemData['id'])) {
                        $imagensInvalidas[] = 'ID não encontrado';
                        continue;
                    }

                    $imagemId = $imagemData['id'];

                    // Verificar se a imagem existe no banco
                    $imagemExiste = Imagem::find($imagemId);

                    if (!$imagemExiste) {
                        $imagensInvalidas[] = "ID {$imagemId} não existe";
                        $imagensNaoEncontradas++;
                        continue;
                    }

                    $imagensParaMigrar[] = [
                        'id' => $imagemId,
                        'observation' => $imagemData['observation'] ?? null
                    ];
                }

                // Se não há imagens válidas para migrar, pular
                if (empty($imagensParaMigrar)) {
                    if (!$dryRun) {
                        // Limpar o campo imagens mesmo que não haja imagens válidas
                        DB::table('mentoria_mensagens')
                            ->where('id', $mensagem->id)
                            ->update(['imagens' => null]);
                    }
                    $progressBar->advance();
                    continue;
                }

                if (!$dryRun) {
                    DB::transaction(function () use ($mensagem, $imagensParaMigrar) {
                        // Inserir na tabela pivot
                        foreach ($imagensParaMigrar as $imagemData) {
                            // Verificar se já não existe (evitar duplicatas)
                            $existe = DB::table('mentoria_mensagem_imagem')
                                ->where('mentoria_mensagem_id', $mensagem->id)
                                ->where('imagem_id', $imagemData['id'])
                                ->exists();

                            if (!$existe) {
                                DB::table('mentoria_mensagem_imagem')->insert([
                                    'mentoria_mensagem_id' => $mensagem->id,
                                    'imagem_id' => $imagemData['id'],
                                    'observation' => $imagemData['observation'],
                                    'created_at' => now(),
                                    'updated_at' => now(),
                                ]);
                            }
                        }

                        // Limpar o campo imagens (migração completa)
                        DB::table('mentoria_mensagens')
                            ->where('id', $mensagem->id)
                            ->update(['imagens' => null]);
                    });
                }

                $processadas++;
                $imagensProcessadas += count($imagensParaMigrar);

            } catch (\Exception $e) {
                $erros++;
                $this->newLine();
                $this->error("❌ Erro ao processar mensagem ID {$mensagem->id}: {$e->getMessage()}");
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // Relatório final
        $this->info('📊 RELATÓRIO FINAL');
        $this->info('═══════════════════════════════════════');
        $this->info("✅ Mensagens processadas: {$processadas}/{$totalMensagens}");
        $this->info("🖼️  Imagens migradas: {$imagensProcessadas}");

        if ($imagensNaoEncontradas > 0) {
            $this->warn("⚠️  Imagens não encontradas no banco: {$imagensNaoEncontradas}");
        }

        if ($erros > 0) {
            $this->error("❌ Erros: {$erros}");
        }

        if ($dryRun) {
            $this->newLine();
            $this->warn('⚠️  MODO DRY-RUN: Nenhuma alteração foi feita');
            $this->info('💡 Execute sem --dry-run para aplicar as mudanças');
        } else {
            $this->newLine();
            $this->info('✅ Migração concluída com sucesso!');
        }

        return 0;
    }
}
