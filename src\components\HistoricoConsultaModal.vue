<template>
  <div class="modal fade lumi-fade" tabindex="-1" id="modalHistoricoConsulta">
    <div class="modal-dialog modal-dialog-centered modal-lg modal-dialog-scrollable">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Histórico da Consulta</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
            ref="closeModalHistoricoConsulta"
          ></button>
        </div>
        <div class="modal-body px-4">
          <div class="row mb-4">
            <div class="col-12">
              <div class="consulta-info p-3 rounded border">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h6 class="mb-0">Detalhes da Consulta</h6>
                  <div class="d-flex gap-2 align-items-center">
                    <span v-if="consulta.categoria" class="categoria-badge" :class="getCategoriaClass(consulta.categoria)">
                      <font-awesome-icon :icon="['fas', getCategoriaIcon(consulta.categoria)]" class="me-1" />
                      {{ getCategoriaNome(consulta.categoria) }}
                    </span>
                    <span class="badge" :class="getStatusClass(consulta.status)">
                      {{ getStatusText(consulta.status) }}
                    </span>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <p class="mb-1"><strong>Paciente:</strong> {{ consulta.paciente_nome }}</p>
                    <p class="mb-1"><strong>Data:</strong> {{ $filters.dateDmy(consulta.horario) }}</p>
                    <p class="mb-1"><strong>Horário:</strong> {{ formatTime(consulta.horario) }}</p>
                  </div>
                  <div class="col-md-6">
                    <p class="mb-1"><strong>Profissional:</strong> {{ getDentistaName(consulta.dentista_id) }}</p>
                    <p class="mb-1"><strong>Valor:</strong> {{ formatCurrency(consulta.valor) }}</p>
                    <p class="mb-1"><strong>Observações:</strong> {{ consulta.observacoes || '-' }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-12">
              <h6 class="mb-0">Registros da Consulta</h6>
            </div>
          </div>

          <!-- Lista de registros -->
          <div v-if="isLoading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status"></div>
            <p class="mt-2">Carregando registros...</p>
          </div>

          <div class="timeline">
            <div v-for="(historico, index) in historicos" :key="index" class="timeline-item">
              <div class="timeline-badge">
                <font-awesome-icon :icon="['fas', getTimelineIcon(historico.titulo)]" />
              </div>
              <div class="timeline-panel">
                <div class="timeline-heading">
                  <div class="d-flex justify-content-between align-items-center">
                    <h6 class="timeline-title">
                      {{ getTituloExibicao(historico) }}
                    </h6>
                    <div class="timeline-actions">
                      <button
                        class="btn btn-sm btn-link text-success"
                        @click="salvarRegistro(historico)"
                        :disabled="isLoading"
                        title="Salvar alterações"
                      >
                        <span v-if="isLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        <font-awesome-icon v-else :icon="['fas', 'save']" />
                      </button>
                    </div>
                  </div>
                </div>
                <div class="timeline-body">
                  <!-- Campo sempre editável -->
                  <div>
                    <textarea
                      class="form-control"
                      v-model="historico.descricao"
                      rows="6"
                      :placeholder="getPlaceholderText(historico.titulo)"
                      @keydown.ctrl.enter="salvarRegistro(historico)"
                    ></textarea>
                    <small class="text-muted mt-1 d-block">
                      Pressione Ctrl+Enter para salvar
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
          <button
            type="button"
            class="btn btn-primary"
            @click="salvarTodosRegistros"
            :disabled="isLoading"
          >
            <span v-if="isLoading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            <font-awesome-icon :icon="['fas', 'save']" class="me-2" />
            Salvar Alterações
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import { getDentistas } from "@/services/dentistasService";
import { getConsulta } from "@/services/consultasService";
import {
  getHistoricosConsulta,
  criarHistoricoPaciente,
  atualizarHistoricoPaciente
} from "@/services/historicoPacienteService";
import cSwal from "@/utils/cSwal.js";
import { openModal } from "@/utils/modalHelper.js";

export default {
  name: "HistoricoConsultaModal",
  data() {
    return {
      isLoading: false,
      consulta: {
        id: null,
        paciente_id: "",
        paciente_nome: "",
        dentista_id: "",
        horario: null,
        valor: "",
        observacoes: "",
        status: "agendada"
      },
      dentistas: [],
      historicos: [],
      historicoPacienteOriginal: null, // Armazena o objeto HistoricoPaciente original
      // Títulos fixos dos dois registros
      titulosFixos: [
        "Histórico da consulta",
        "O que fazer na próxima consulta"
      ],
      // Categorias das consultas (mesmo array do ConsultasTable)
      categorias: [
        { valor: 'acompanhamento', nome: 'Acompanhamento/ativação', cor: 'info', icone: 'wrench' },
        { valor: 'primeira_consulta', nome: 'Primeira consulta', cor: 'success', icone: 'clipboard-check' },
        { valor: 'emergencia', nome: 'Emergência', cor: 'danger', icone: 'exclamation-triangle' },
        { valor: 'montagem', nome: 'Montagem', cor: 'secondary', icone: 'tools' },
        { valor: 'remocao', nome: 'Remoção', cor: 'secondary', icone: 'minus-circle' },
        { valor: 'replanejamento', nome: 'Replanejamento', cor: 'dark', icone: 'sync-alt' },
        { valor: 'pos_tratamento', nome: 'Pós-tratamento', cor: 'secondary', icone: 'check-double' }
      ]
    };
  },
  computed: {
    // Removido computed isRegistroValido pois não é mais necessário
  },
  async created() {
    await this.carregarDentistas();
  },
  methods: {
    async carregarDentistas() {
      try {
        const response = await getDentistas();
        if (response) {
          this.dentistas = response;
        }
      } catch (error) {
        console.error("Erro ao carregar dentistas:", error);
      }
    },
    getDentistaName(dentistaId) {
      if (!dentistaId) return '-';

      const dentista = this.dentistas.find(d => d.id == dentistaId);
      return dentista ? dentista.nome : `Profissional #${dentistaId}`;
    },
    formatCurrency(value) {
      if (!value && value !== 0) return '-';

      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    },
    formatTime(dateTime) {
      if (!dateTime) return '-';

      try {
        // Se for uma string de data completa, extrair apenas a parte da hora
        if (typeof dateTime === 'string') {
          // Verificar se é uma data completa (YYYY-MM-DD HH:MM:SS)
          if (dateTime.includes('T') || dateTime.includes(' ')) {
            const date = new Date(dateTime);
            if (!isNaN(date.getTime())) {
              return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
            }
          }

          // Se for apenas um horário (HH:MM:SS)
          if (dateTime.includes(':')) {
            const timeParts = dateTime.split(':');
            if (timeParts.length >= 2) {
              return `${timeParts[0]}:${timeParts[1]}`;
            }
          }
        }

        // Se for um objeto Date
        if (dateTime instanceof Date) {
          return dateTime.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
        }

        // Tentar com moment como último recurso
        return moment(dateTime).format('HH:mm');
      } catch (error) {
        console.error('Erro ao formatar horário:', error);
        return '-';
      }
    },
    getStatusClass(status) {
      const statusClasses = {
        'agendada': 'bg-info',
        'realizada': 'bg-success',
        'cancelada': 'bg-danger',
        'reagendada': 'bg-warning',
        'confirmada': 'bg-primary'
      };

      return statusClasses[status] || 'bg-secondary';
    },
    getStatusText(status) {
      const statusTexts = {
        'agendada': 'AGENDADA',
        'realizada': 'REALIZADA',
        'cancelada': 'CANCELADA',
        'reagendada': 'REAGENDADA',
        'confirmada': 'CONFIRMADA'
      };

      return statusTexts[status] || status.toUpperCase();
    },
    getTituloExibicao(historico) {
      return historico.titulo;
    },
    getPlaceholderText(titulo) {
      const placeholders = {
        "Histórico da consulta": "Descreva o que foi realizado durante esta consulta...",
        "O que fazer na próxima consulta": "Descreva o que deve ser feito na próxima consulta..."
      };
      return placeholders[titulo] || "Digite a descrição...";
    },
    getTimelineIcon(titulo) {
      const icons = {
        "Histórico da consulta": "clipboard-list",
        "O que fazer na próxima consulta": "arrow-right"
      };
      return icons[titulo] || "circle";
    },
    // Métodos para categorias (copiados do ConsultasTable)
    getCategoriaData(categoria) {
      return this.categorias.find(cat => cat.valor === categoria) ||
             { valor: categoria, nome: categoria, cor: 'secondary', icone: 'question' };
    },
    getCategoriaNome(categoria) {
      if (!categoria) return '-';
      return this.getCategoriaData(categoria).nome;
    },
    getCategoriaIcon(categoria) {
      if (!categoria) return 'question';
      return this.getCategoriaData(categoria).icone;
    },
    getCategoriaClass(categoria) {
      if (!categoria) return 'categoria-badge-secondary';
      const cor = this.getCategoriaData(categoria).cor;
      return `categoria-badge-${cor}`;
    },
    async abrirModal(consultaId) {
      try {
        this.isLoading = true;

        // Buscar os dados da consulta
        const consulta = await getConsulta(consultaId);

        if (!consulta) {
          cSwal.cError("Erro ao carregar dados da consulta. Por favor, tente novamente.");
          return;
        }

        this.consulta = consulta;

        // Buscar históricos da consulta
        await this.carregarHistoricos(consultaId);

        // Abrir o modal
        openModal('modalHistoricoConsulta');
      } catch (error) {
        console.error("Erro ao carregar consulta:", error);
        cSwal.cError("Erro ao carregar dados da consulta. Por favor, tente novamente.");
      } finally {
        this.isLoading = false;
      }
    },
    async carregarHistoricos(consultaId) {
      try {
        const response = await getHistoricosConsulta(this.consulta.paciente_id, consultaId);

        // Inicializar array com os dois registros fixos
        let modificacoesExistentes = [];

        if (response && response.length > 0) {
          // Pegar o primeiro (e único) objeto HistoricoPaciente
          const historicoPaciente = response[0];

          // Armazenar o objeto original para uso posterior
          this.historicoPacienteOriginal = historicoPaciente;

          // Processar as modificações que estão em JSON
          if (historicoPaciente.modificacoes) {
            try {
              // Converter as modificações para array se for string
              if (typeof historicoPaciente.modificacoes === 'string') {
                modificacoesExistentes = JSON.parse(historicoPaciente.modificacoes);
              } else {
                modificacoesExistentes = historicoPaciente.modificacoes;
              }

              // Se modificacoes não for um array, converter para array
              if (!Array.isArray(modificacoesExistentes)) {
                // Se for um objeto com titulo, é uma única modificação
                if (modificacoesExistentes.titulo) {
                  modificacoesExistentes = [modificacoesExistentes];
                } else {
                  modificacoesExistentes = [];
                }
              }
            } catch (e) {
              console.error('Erro ao processar modificações:', e);
              modificacoesExistentes = [];
            }
          }
        }

        // Garantir que sempre existam os dois registros fixos
        const modificacoesFinais = [];

        this.titulosFixos.forEach((titulo, index) => {
          // Procurar se já existe uma modificação com este título
          const modificacaoExistente = modificacoesExistentes.find(mod => mod.titulo === titulo);

          modificacoesFinais.push({
            id: index, // ID local para identificação
            historicoPacienteId: this.historicoPacienteOriginal?.id || null,
            titulo: titulo,
            descricao: modificacaoExistente?.descricao || ''
          });
        });

        this.historicos = modificacoesFinais;

        // Se não existe histórico no banco e temos registros vazios, criar o histórico
        if (!this.historicoPacienteOriginal) {
          await this.criarHistoricoInicial();
        }
      } catch (error) {
        console.error("Erro ao carregar históricos:", error);
        cSwal.cError("Erro ao carregar histórico da consulta.");
        this.historicos = [];
      }
    },
    async criarHistoricoInicial() {
      try {
        // Criar o histórico inicial com os dois registros vazios
        const modificacoesIniciais = this.titulosFixos.map(titulo => ({
          titulo: titulo,
          descricao: ''
        }));

        const historicoData = {
          paciente_id: this.consulta.paciente_id,
          consulta_id: this.consulta.id,
          data: moment().format('YYYY-MM-DD'),
          horario: moment().format('HH:mm:ss'),
          codigo_acao: 'alteracao_consulta',
          descricao: 'Histórico de alterações da consulta',
          modificacoes: JSON.stringify(modificacoesIniciais)
        };

        const response = await criarHistoricoPaciente(historicoData);
        if (response) {
          this.historicoPacienteOriginal = response;
        }
      } catch (error) {
        console.error("Erro ao criar histórico inicial:", error);
      }
    },
    async salvarRegistro(historico) {
      this.isLoading = true;

      try {
        // Obter todas as modificações existentes ou criar array inicial
        let modificacoes = [];

        if (this.historicoPacienteOriginal && this.historicoPacienteOriginal.modificacoes) {
          try {
            // Converter as modificações para array se for string
            if (typeof this.historicoPacienteOriginal.modificacoes === 'string') {
              modificacoes = JSON.parse(this.historicoPacienteOriginal.modificacoes);
            } else {
              modificacoes = this.historicoPacienteOriginal.modificacoes;
            }

            // Se modificacoes não for um array, converter para array
            if (!Array.isArray(modificacoes)) {
              modificacoes = [];
            }
          } catch (e) {
            console.error('Erro ao processar modificações:', e);
            modificacoes = [];
          }
        }

        // Garantir que temos os dois registros fixos
        if (modificacoes.length === 0) {
          modificacoes = this.titulosFixos.map(titulo => ({
            titulo: titulo,
            descricao: ''
          }));
        }

        // Atualizar o registro específico
        const indexRegistro = historico.id;
        if (indexRegistro !== null && indexRegistro !== undefined && indexRegistro < modificacoes.length) {
          modificacoes[indexRegistro] = {
            titulo: historico.titulo,
            descricao: historico.descricao
          };
        }

        // Preparar os dados para salvar
        const historicoData = {
          paciente_id: this.consulta.paciente_id,
          consulta_id: this.consulta.id,
          data: moment().format('YYYY-MM-DD'),
          horario: moment().format('HH:mm:ss'),
          codigo_acao: 'alteracao_consulta',
          descricao: 'Histórico de alterações da consulta',
          modificacoes: JSON.stringify(modificacoes)
        };

        let response;

        // Se já existe um histórico, atualizar
        if (this.historicoPacienteOriginal) {
          response = await atualizarHistoricoPaciente(this.historicoPacienteOriginal.id, historicoData);
          if (response) {
            cSwal.cSuccess("Registro atualizado com sucesso!");
          }
        } else {
          // Criar um novo histórico
          response = await criarHistoricoPaciente(historicoData);
          if (response) {
            cSwal.cSuccess("Registro salvo com sucesso!");
          }
        }

        // Atualizar o historicoPacienteOriginal com a resposta
        if (response) {
          this.historicoPacienteOriginal = response;
        }
      } catch (error) {
        console.error("Erro ao salvar registro:", error);
        cSwal.cError("Erro ao salvar o registro. Por favor, tente novamente.");
      } finally {
        this.isLoading = false;
      }
    },
    async salvarTodosRegistros() {
      this.isLoading = true;

      try {
        // Preparar todas as modificações
        const modificacoes = this.titulosFixos.map((titulo, index) => {
          const historico = this.historicos.find(h => h.id === index);
          return {
            titulo: titulo,
            descricao: historico ? historico.descricao : ''
          };
        });

        // Preparar os dados para salvar
        const historicoData = {
          paciente_id: this.consulta.paciente_id,
          consulta_id: this.consulta.id,
          data: moment().format('YYYY-MM-DD'),
          horario: moment().format('HH:mm:ss'),
          codigo_acao: 'alteracao_consulta',
          descricao: 'Histórico de alterações da consulta',
          modificacoes: JSON.stringify(modificacoes)
        };

        let response;

        // Se já existe um histórico, atualizar
        if (this.historicoPacienteOriginal) {
          response = await atualizarHistoricoPaciente(this.historicoPacienteOriginal.id, historicoData);
          if (response) {
            cSwal.cSuccess("Alterações salvas com sucesso!");
          }
        } else {
          // Criar um novo histórico
          response = await criarHistoricoPaciente(historicoData);
          if (response) {
            cSwal.cSuccess("Histórico salvo com sucesso!");
          }
        }

        // Atualizar o historicoPacienteOriginal com a resposta
        if (response) {
          this.historicoPacienteOriginal = response;
        }
      } catch (error) {
        console.error("Erro ao salvar alterações:", error);
        cSwal.cError("Erro ao salvar as alterações. Por favor, tente novamente.");
      } finally {
        this.isLoading = false;
      }
    }
  }
};
</script>

<style scoped>
.consulta-info {
  background-color: #f8f9fa;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.empty-state-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem;
  text-align: center;
  background-color: #f8fafc;
  border-radius: 8px;
  margin: 0.5rem 0;
  border: 1px dashed #d1dce8;
  width: 100%;
}

.empty-state-message .icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e6f2ff, #d1e6ff);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.empty-state-message .empty-state-icon {
  font-size: 1.5rem;
  color: #5a9bd5;
}

.timeline {
  position: relative;
  padding: 20px 0;
  max-width: 800px;
  margin: 0 auto;
}

.timeline:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 18px;
  width: 2px;
  background: linear-gradient(to bottom, rgba(0, 123, 255, 0.2), rgba(0, 123, 255, 0.6));
  border-radius: 1px;
}

.timeline-item {
  position: relative;
  margin-bottom: 24px;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.timeline-badge {
  position: absolute;
  top: 0;
  left: 0;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  text-align: center;
  font-size: 0.9em;
  line-height: 36px;
  background-color: white;
  border: 2px solid #007bff;
  color: #007bff;
  z-index: 100;
  box-shadow: 0 2px 6px rgba(0, 123, 255, 0.2);
  transition: all 0.2s ease;
}

.timeline-item:hover .timeline-badge {
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(0, 123, 255, 0.3);
}

.timeline-panel {
  position: relative;
  margin-left: 60px;
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.timeline-panel:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #d1dce8;
}

.timeline-panel:before {
  content: '';
  position: absolute;
  top: 10px;
  left: -10px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid #e9ecef;
}

.timeline-panel:after {
  content: '';
  position: absolute;
  top: 10px;
  left: -9px;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 10px solid #fff;
}

.timeline-title {
  margin-top: 0;
  margin-bottom: 8px;
  color: #333;
  font-weight: 600;
  font-size: 1rem;
}

.timeline-body > p {
  margin-bottom: 0;
  color: #495057;
  font-size: 0.9rem;
  line-height: 1.5;
}

.timeline-actions {
  display: flex;
  gap: 8px;
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.timeline-panel:hover .timeline-actions {
  opacity: 1;
}

.timeline-actions .btn {
  padding: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.timeline-actions .btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Estilos para campos sempre editáveis */
.timeline-body textarea.form-control {
  border: 1px solid #007bff;
  border-radius: 6px;
  resize: vertical;
  min-height: 120px;
  transition: all 0.2s ease;
}

.timeline-body textarea.form-control:focus {
  border-color: #0056b3;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Estilos para categorias */
.categoria-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.categoria-badge-info {
  background-color: rgba(13, 202, 240, 0.1);
  color: #0dcaf0;
  border: 1px solid rgba(13, 202, 240, 0.2);
}

.categoria-badge-success {
  background-color: rgba(25, 135, 84, 0.1);
  color: #198754;
  border: 1px solid rgba(25, 135, 84, 0.2);
}

.categoria-badge-danger {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.categoria-badge-secondary {
  background-color: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

.categoria-badge-dark {
  background-color: rgba(33, 37, 41, 0.1);
  color: #212529;
  border: 1px solid rgba(33, 37, 41, 0.2);
}

.timeline-actions .btn-link {
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.timeline-actions .btn-link:hover {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

/* Animação de fade para o modal */
.lumi-fade.modal-closing .modal-dialog {
  transform: translate(0, -25px);
  transition: transform 0.3s ease-out;
}

.lumi-fade.modal-closing {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}
</style>
