<template>
  <div class="modal fade" tabindex="-1" id="modalMentoria" ref="modalMentoria">
    <div class="modal-dialog modal-fullscreen-lg-down modal-dialog-centered mentoria-modal-dialog">
      <div class="modal-content mentoria-modal">
        <div class="modal-header bg-gradient-primary">
          <h5 class="modal-title d-flex align-items-center text-white">
            <i class="fas fa-handshake-angle me-2 text-white"></i>
            Mentoria - {{ mentoria?.paciente?.nome || 'Carregando...' }}
          </h5>
          <button
            type="button"
            class="btn-close btn-close-white"
            data-bs-dismiss="modal"
            aria-label="Close"
            style="filter: brightness(0) invert(1);"
          ></button>
        </div>
        
        <div class="modal-body p-0" v-if="mentoria">
          <!-- Tabs para Mobile -->
          <div class="mentoria-tabs d-lg-none">
            <button
              class="mentoria-tab"
              :class="{ active: activeTab === 'info' }"
              @click="activeTab = 'info'"
            >
              <i class="fas fa-info-circle me-2"></i>
              Informações
            </button>
            <button
              class="mentoria-tab"
              :class="{ active: activeTab === 'chat' }"
              @click="activeTab = 'chat'"
            >
              <i class="fas fa-comments me-2"></i>
              Chat
              <span v-if="mensagensNaoLidas > 0" class="badge bg-danger ms-2">{{ mensagensNaoLidas }}</span>
            </button>
          </div>

          <div class="mentoria-container">
            <!-- Lado Esquerdo - Informações da Mentoria -->
            <div
              class="mentoria-info-side bg-light border-end"
              :class="{ 'd-none d-lg-grid': activeTab === 'chat' }"
            >
              <!-- Header do Card com Informações do Paciente -->
              <div class="paciente-header bg-white border-bottom">
                <div class="d-flex align-items-center mb-3">
                  <div class="paciente-avatar me-3">
                    <div class="avatar-circle bg-primary text-white d-flex align-items-center justify-content-center">
                      <i class="fas fa-user fa-lg"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h5 class="mb-1 fw-bold text-dark">{{ mentoria.paciente?.nome || 'Nome não disponível' }}</h5>
                    <p class="mb-0 text-muted small">Paciente de {{ mentoria.paciente?.clinica?.nome || 'Clínica não disponível' }}</p>
                  </div>
                  <div class="d-flex flex-column align-items-end">
                    <button
                      class="btn btn-sm btn-outline-primary"
                      @click="abrirPaciente"
                      title="Abrir prontuário do paciente"
                    >
                      <i class="fas fa-external-link-alt me-1"></i>
                      Abrir
                    </button>
                  </div>
                </div>
              </div>

              <!-- Header do Mentor (quando há mentor atribuído) -->
              <div v-if="mentoria.mentor" class="mentor-header p-3">
                <div class="d-flex align-items-center">
                  <div class="mentor-avatar me-3">
                    <div class="avatar-circle d-flex align-items-center justify-content-center">
                      <i class="fas fa-handshake-angle fa-lg"></i>
                    </div>
                  </div>
                  <div class="flex-grow-1">
                    <h6 class="mb-1 fw-bold">{{ mentoria.mentor?.nome || 'Mentor não atribuído' }}</h6>
                    <p class="mb-0 small text-muted">Mentor responsável</p>
                  </div>
                  <div class="text-end">
                    <small class="text-muted">Iniciada em</small>
                    <div class="fw-bold small">{{ $filters.dateTime(mentoria.iniciada_em) }}</div>
                  </div>
                </div>
              </div>

              <!-- Conteúdo do Card -->
              <div class="mentoria-info-content p-3 conteudo-card">
                <div class="mentoria-info flex-grow-1 d-flex flex-column">
                  <div class="info-section mb-3 flex-grow-1 d-flex flex-column">
                    <h6 class="text-primary mb-2 d-flex align-items-center">
                      <i class="fas fa-info-circle me-2"></i>
                      Detalhes
                    </h6>

                    <div class="info-item mb-2">
                      <div class="row g-2">
                        <div class="col-7">
                          <label class="form-label text-muted small mb-1">Solicitação da mentoria:</label>
                          <div class="fw-bold small">{{ $filters.dateTime(mentoria.created_at) }}</div>
                        </div>
                        <div class="col-5">
                          <label class="form-label text-muted small mb-1">Status:</label>
                          <div>
                            <span class="badge" :class="getStatusClass(mentoria.status)">
                              {{ getStatusText(mentoria.status) }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="info-item mb-2">
                      <label class="form-label text-muted small me-2 mb-0">Solicitante:</label>
                      <span class="fw-bold small">{{ mentoria.solicitante?.nome || 'Nome não disponível' }}</span>
                    </div>

                    <div class="info-item flex-grow-1 d-flex flex-column">
                      <label class="form-label text-muted small mb-1">Observações:</label>
                      <div class="observacoes-box p-2 bg-white border rounded flex-grow-1">
                        {{ mentoria.observacao || 'Nenhuma observação fornecida.' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Botões de Ação (visível em desktop sempre, e em mobile apenas na aba info) -->
              <div
                class="mentoria-info-footer acoes-mentoria p-2 px-3 bg-white border-top"
                v-if="podeInteragir()"
                :class="{ 'd-none d-lg-block': activeTab === 'chat' }"
              >
                <!-- Botões lado a lado quando há 2 botões -->
                <div v-if="temDoisBotoes" class="d-flex gap-2">
                  <!-- Botão Iniciar Mentoria (apenas para admins) -->
                  <button
                    v-if="$user?.system_admin && mentoria.status === 'AGUARDANDO'"
                    class="btn btn-success btn-sm flex-fill"
                    @click="iniciarMentoria"
                    :disabled="processandoAcao"
                  >
                    <span v-if="processandoAcao" class="spinner-border spinner-border-sm me-1"></span>
                    <i v-else class="fas fa-play me-1"></i>
                    Iniciar
                  </button>

                  <!-- Botão Cancelar -->
                  <button
                    v-if="mentoria.status === 'AGUARDANDO' || mentoria.status === 'EM_ANDAMENTO'"
                    class="btn btn-outline-danger btn-sm flex-fill"
                    @click="cancelarMentoria"
                    :disabled="processandoAcao"
                  >
                    <span v-if="processandoAcao" class="spinner-border spinner-border-sm me-1"></span>
                    <i v-else class="fas fa-times me-1"></i>
                    Cancelar
                  </button>

                  <!-- Botão Finalizar -->
                  <button
                    v-if="mentoria.status === 'EM_ANDAMENTO'"
                    class="btn btn-primary btn-sm flex-fill"
                    @click="finalizarMentoria"
                    :disabled="processandoAcao"
                  >
                    <span v-if="processandoAcao" class="spinner-border spinner-border-sm me-1"></span>
                    <i v-else class="fas fa-check me-1"></i>
                    Finalizar
                  </button>

                  <!-- Botão Reabrir (para mentorias canceladas ou finalizadas) -->
                  <button
                    v-if="mentoria && (mentoria.status === 'CANCELADA' || mentoria.status === 'FINALIZADA')"
                    class="btn btn-outline-warning btn-sm flex-fill"
                    @click="reabrirMentoria"
                    :disabled="processandoAcao"
                  >
                    <span v-if="processandoAcao" class="spinner-border spinner-border-sm me-1"></span>
                    <i v-else class="fas fa-redo me-1"></i>
                    Reabrir
                  </button>
                </div>

                <!-- Botão único ocupando largura total -->
                <div v-else class="d-grid">
                  <!-- Botão Iniciar Mentoria (apenas para admins) -->
                  <button
                    v-if="$user?.system_admin && mentoria.status === 'AGUARDANDO'"
                    class="btn btn-success btn-sm"
                    @click="iniciarMentoria"
                    :disabled="processandoAcao"
                  >
                    <span v-if="processandoAcao" class="spinner-border spinner-border-sm me-1"></span>
                    <i v-else class="fas fa-play me-1"></i>
                    Iniciar Mentoria
                  </button>

                  <!-- Botão Finalizar -->
                  <button
                    v-if="mentoria.status === 'EM_ANDAMENTO'"
                    class="btn btn-primary btn-sm"
                    @click="finalizarMentoria"
                    :disabled="processandoAcao"
                  >
                    <span v-if="processandoAcao" class="spinner-border spinner-border-sm me-1"></span>
                    <i v-else class="fas fa-check me-1"></i>
                    Finalizar Mentoria
                  </button>

                  <!-- Botão Cancelar -->
                  <button
                    v-if="mentoria.status === 'AGUARDANDO' || mentoria.status === 'EM_ANDAMENTO'"
                    class="btn btn-outline-danger btn-sm"
                    @click="cancelarMentoria"
                    :disabled="processandoAcao"
                  >
                    <span v-if="processandoAcao" class="spinner-border spinner-border-sm me-1"></span>
                    <i v-else class="fas fa-times me-1"></i>
                    Cancelar Mentoria
                  </button>

                  <!-- Botão Reabrir (para mentorias canceladas ou finalizadas) -->
                  <button
                    v-if="mentoria && (mentoria.status === 'CANCELADA' || mentoria.status === 'FINALIZADA')"
                    class="btn btn-outline-warning btn-sm"
                    @click="reabrirMentoria"
                    :disabled="processandoAcao"
                  >
                    <span v-if="processandoAcao" class="spinner-border spinner-border-sm me-1"></span>
                    <i v-else class="fas fa-redo me-1"></i>
                    Reabrir Mentoria
                  </button>
                </div>
              </div>
            </div>

            <!-- Lado Direito - Sistema de Mensagens -->
            <div
              class="mentoria-chat-side"
              :class="{ 'd-none d-lg-grid': activeTab === 'info' }"
            >
              <!-- Header do Chat/Galeria -->
              <div class="mentoria-chat-header p-3 bg-white border-bottom">
                <h6 class="mb-0 text-primary">
                  <i :class="currentView === 'chat' ? 'fas fa-comments' : 'fas fa-images'" class="me-2"></i>
                  {{ currentView === 'chat' ? 'Conversas da Mentoria' : 'Selecionar Imagens' }}
                </h6>
                <!-- Botão para voltar ao chat quando estiver na galeria -->
                <button
                  v-if="currentView !== 'chat'"
                  class="btn btn-outline-secondary btn-sm"
                  @click="backToChat"
                >
                  <i class="fas fa-arrow-left me-1"></i>
                  Voltar ao Chat
                </button>
              </div>

              <!-- Conteúdo Principal - Chat ou Galeria -->
              <div class="mentoria-chat-content">
                <!-- Área de Mensagens (Chat) -->
                <div v-if="currentView === 'chat'" class="mensagens-container p-3" ref="mensagensContainer">
                  <div v-if="mensagens.length === 0" class="text-center text-muted py-5">
                    <i class="fas fa-comment-slash fa-2x mb-3"></i>
                    <p>Nenhuma mensagem ainda. Inicie a conversa!</p>
                  </div>

                  <div v-for="mensagem in mensagens" :key="mensagem.id" class="mensagem-item mb-3" :class="{ 'nova-mensagem': mensagem.isNovaMensagem }">
                    <!-- Mensagem do Sistema -->
                    <div v-if="mensagem.tipo === 'SISTEMA'" class="mensagem-sistema text-center">
                      <div class="sistema-badge">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ mensagem.mensagem }}
                      </div>
                      <small class="text-muted d-block mt-1">
                        {{ $filters.dateTime(mensagem.created_at) }}
                      </small>
                    </div>

                    <!-- Mensagem de Usuário -->
                    <div v-else class="mensagem-usuario" :class="{ 'mensagem-direita': isMinhaMsg(mensagem.remetente_id) }">
                      <div
                        class="mensagem-card"
                        :class="{
                          'mensagem-minha': isMinhaMsg(mensagem.remetente_id),
                          'mensagem-outro': !isMinhaMsg(mensagem.remetente_id)
                        }"
                      >
                        <div class="mensagem-header d-flex align-items-center mb-2">
                          <div class="remetente-info d-flex align-items-center">
                            <i
                              v-if="isMentor(mensagem.remetente_id)"
                              class="fas fa-handshake-angle me-2"
                              title="Mentor"
                            ></i>
                            <i
                              v-else
                              class="fas fa-user me-2"
                              title="Solicitante"
                            ></i>
                            <span class="fw-bold">{{ mensagem.remetente?.nome || 'Usuário' }}</span>
                          </div>
                          <small class="text-muted ms-auto">
                            {{ $filters.dateTime(mensagem.created_at) }}
                          </small>
                        </div>
                        <div class="mensagem-conteudo">
                          <!-- Mensagem de texto -->
                          <div v-if="mensagem.mensagem" class="mensagem-texto">
                            {{ mensagem.mensagem }}
                          </div>

                          <!-- Imagens referenciadas -->
                          <div v-if="(mensagem.imagens_com_observacoes && mensagem.imagens_com_observacoes.length > 0) || (mensagem.imagens && mensagem.imagens.length > 0)" class="mensagem-imagens mt-2">
                            <div class="imagens-grid" v-viewer="{url: 'data-source'}">
                              <div
                                v-for="imagemRef in (mensagem.imagens_com_observacoes || mensagem.imagens)"
                                :key="`img-${imagemRef.id}-${Math.random()}`"
                                class="imagem-referenciada"
                                :class="{ 'nova-mensagem': mensagem.isNovaMensagem }"
                              >
                                <img
                                  :src="imagemRef.url_preview || imagemRef.url"
                                  :data-source="imagemRef.url_original || imagemRef.url"
                                  :alt="imagemRef.description || imagemRef.descricao || 'Imagem'"
                                  @error="onImageError"
                                />
                                <div v-if="imagemRef.observation" class="imagem-observacao">
                                  <i class="fas fa-comment me-1"></i>
                                  {{ imagemRef.observation }}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Galeria de Seleção de Imagens -->
                <ImageGallerySelector
                  v-else-if="currentView === 'gallery'"
                  :imagens="pacienteImagens"
                  :loading="carregandoImagens"
                  @cancel="backToChat"
                  @proceed="proceedToObservations"
                />

                <!-- Etapa de Observações -->
                <ImageObservationsStep
                  v-else-if="currentView === 'observations'"
                  :selectedImages="selectedImages"
                  :sending="enviandoImagens"
                  @back="backToGallery"
                  @send="sendImagesMessage"
                  @update-selected-images="updateSelectedImages"
                />
              </div>

              <!-- Área de Digitação (apenas no chat) -->
              <div v-if="currentView === 'chat'" class="mentoria-chat-footer p-3 bg-light border-top">
                <!-- Balão temporário da IA -->
                <div v-if="mensagemRefinadaIA" ref="mensagemIAPreview" class="mensagem-ia-preview">
                  <div class="mensagem-ia-header d-flex align-items-center mb-2">
                    <i class="fas fa-sparkles text-primary me-2"></i>
                    <span class="fw-bold text-primary">Mensagem refinada por IA</span>
                  </div>
                  <textarea
                    v-model="mensagemRefinadaIA"
                    class="mensagem-ia-conteudo p-3 bg-white border rounded form-control"
                    rows="6"
                    placeholder="Edite a mensagem refinada..."
                  ></textarea>
                  <div class="mensagem-ia-acoes d-flex gap-2 mt-2">
                    <button
                      type="button"
                      class="btn btn-sm btn-outline-danger"
                      @click="cancelarMensagemIA"
                      :disabled="processandoIA"
                    >
                      <i class="fas fa-times me-1"></i>
                      Cancelar
                    </button>
                    <button
                      type="button"
                      class="btn btn-sm btn-outline-primary"
                      @click="gerarNovaMensagemIA"
                      :disabled="processandoIA"
                    >
                      <span v-if="processandoIA" class="spinner-border spinner-border-sm me-1"></span>
                      <i v-else class="fas fa-sync-alt me-1"></i>
                      Gerar outra
                    </button>
                    <button
                      type="button"
                      class="btn btn-sm btn-primary"
                      @click="enviarMensagemRefinada"
                      :disabled="processandoIA || enviandoMensagem"
                    >
                      <span v-if="enviandoMensagem" class="spinner-border spinner-border-sm me-1"></span>
                      <i v-else class="fas fa-paper-plane me-1"></i>
                      Enviar mensagem
                    </button>
                  </div>
                </div>

                <!-- Form de digitação (oculto quando há mensagem refinada) -->
                <form v-if="!mensagemRefinadaIA" @submit.prevent="enviarMensagem" class="d-flex gap-2 align-items-end">
                  <div class="flex-grow-1 position-relative">
                    <textarea
                      ref="mensagemTextarea"
                      v-model="novaMensagem"
                      class="form-control textarea-auto-resize"
                      placeholder="Digite sua mensagem..."
                      rows="2"
                      :disabled="enviandoMensagem || enviandoImagens"
                      @keydown.ctrl.enter="enviarMensagem"
                      @input="ajustarAlturaTextarea"
                    ></textarea>
                    <!-- Botão de IA dentro do textarea (sempre visível para system_admin) -->
                    <button
                      v-if="$user?.system_admin"
                      type="button"
                      class="btn-ia-refinar"
                      :class="{ 'btn-ia-ativo': podeRefinarMensagem }"
                      @click="refinarMensagemComIA"
                      :disabled="processandoIA || !podeRefinarMensagem"
                      :title="podeRefinarMensagem ? 'Refinar mensagem com IA' : 'Digite pelo menos 2 palavras para refinar'"
                    >
                      <span v-if="processandoIA" class="spinner-border spinner-border-sm spinner-ia"></span>
                      <img v-else src="@/assets/img/icons/ai.svg" class="icone-ia" alt="IA" />
                    </button>
                    <small class="text-muted d-block mt-1">Ctrl + Enter para enviar</small>
                  </div>
                  <div class="d-flex gap-2 botoes-acao-container">
                    <!-- Botão para enviar imagens -->
                    <button
                      type="button"
                      class="btn btn-outline-primary btn-enviar-imagens"
                      @click="openImageGallery"
                      :disabled="enviandoMensagem || enviandoImagens || carregandoImagens"
                      title="Enviar imagens"
                    >
                      <span v-if="carregandoImagens" class="spinner-border spinner-border-sm"></span>
                      <i v-else class="fas fa-images"></i>
                    </button>
                    <!-- Botão para enviar mensagem -->
                    <button
                      type="submit"
                      class="btn btn-primary btn-enviar-mensagem"
                      :disabled="!novaMensagem.trim() || enviandoMensagem || enviandoImagens"
                    >
                      <span v-if="enviandoMensagem" class="spinner-border spinner-border-sm me-2"></span>
                      <i v-else class="fas fa-paper-plane me-2"></i>
                      Enviar
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-else class="modal-body text-center py-5">
          <div class="spinner-border text-primary" role="status"></div>
          <p class="mt-3 text-muted">Carregando mentoria...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getMentoria,
  enviarMensagem as enviarMensagemService,
  enviarMensagemComImagens,
  marcarMensagensComoLidas,
  iniciarMentoria as iniciarMentoriaService,
  finalizarMentoria as finalizarMentoriaService,
  cancelarMentoria as cancelarMentoriaService,
  reabrirMentoria as reabrirMentoriaService,
  refinarMensagemComIA as refinarMensagemComIAService
} from '@/services/mentoriasService';
import { getImagensPorPaciente } from '@/services/imagensService';
import { closeModal } from '@/utils/modalHelper';
import cSwal from '@/utils/cSwal.js';
import ImageGallerySelector from './ImageGallerySelector.vue';
import ImageObservationsStep from './ImageObservationsStep.vue';

export default {
  name: 'MentoriaModal',
  components: {
    ImageGallerySelector,
    ImageObservationsStep
  },
  emits: ['mentoria-atualizada'],
  data() {
    return {
      mentoria: null,
      mensagens: [],
      novaMensagem: '',
      enviandoMensagem: false,
      processandoAcao: false,
      mentoriaId: null,
      intervalId: null,
      textareaResizeIntervalId: null, // Interval para verificar resize do textarea
      // Estados para a funcionalidade de imagens
      currentView: 'chat', // 'chat', 'gallery', 'observations'
      selectedImages: [],
      enviandoImagens: false,
      pacienteImagens: [], // Imagens carregadas do paciente
      carregandoImagens: false,
      // Estados para a funcionalidade de IA
      mensagemRefinadaIA: null,
      mensagemOriginalIA: '',
      processandoIA: false,
      // Estado para controle de tabs em mobile
      activeTab: 'chat', // 'info' ou 'chat'
    };
  },
  computed: {
    podeRefinarMensagem() {
      // Verifica se há pelo menos 2 palavras (separadas por espaço)
      const palavras = this.novaMensagem.trim().split(/\s+/).filter(p => p.length > 0);
      return palavras.length >= 2;
    },
    temDoisBotoes() {
      // Verifica se há exatamente 2 botões visíveis
      if (!this.mentoria) return false;

      let count = 0;

      // Botão Iniciar (apenas para admins em status AGUARDANDO)
      if (this.$user?.system_admin && this.mentoria.status === 'AGUARDANDO') {
        count++;
      }

      // Botão Finalizar (status EM_ANDAMENTO)
      if (this.mentoria.status === 'EM_ANDAMENTO') {
        count++;
      }

      // Botão Cancelar (status AGUARDANDO ou EM_ANDAMENTO)
      if (this.mentoria.status === 'AGUARDANDO' || this.mentoria.status === 'EM_ANDAMENTO') {
        count++;
      }

      // Botão Reabrir (status CANCELADA ou FINALIZADA)
      if (this.mentoria.status === 'CANCELADA' || this.mentoria.status === 'FINALIZADA') {
        count++;
      }

      return count === 2;
    },
    mensagensNaoLidas() {
      // Placeholder para contador de mensagens não lidas
      // Pode ser implementado futuramente se houver essa funcionalidade
      return 0;
    }
  },
  methods: {
    async abrirMentoria(mentoriaId) {
      this.mentoriaId = mentoriaId;
      this.mentoria = null;
      this.mensagens = [];
      // Sempre abre no chat por padrão
      this.activeTab = 'chat';

      try {
        const data = await getMentoria(mentoriaId);
        if (data) {
          this.mentoria = data;
          this.mensagens = data.mensagens || [];

          // Marcar mensagens como lidas
          await marcarMensagensComoLidas(mentoriaId);

          // Scroll para o final das mensagens
          this.$nextTick(() => {
            this.scrollToBottom();
          });

          // Iniciar polling inteligente para novas mensagens
          this.startPollingInteligente();
        } else {
          cSwal.cError('Erro ao carregar mentoria');
        }
      } catch (error) {
        console.error('Erro ao carregar mentoria:', error);
        cSwal.cError('Erro ao carregar mentoria');
      }
    },

    async enviarMensagem() {
      if (!this.novaMensagem.trim() || this.enviandoMensagem) return;

      this.enviandoMensagem = true;

      try {
        const sucesso = await enviarMensagemService(this.mentoriaId, this.novaMensagem.trim());

        if (sucesso) {
          this.novaMensagem = '';
          // Garante que está na tab do chat
          this.activeTab = 'chat';
          // Buscar novas mensagens imediatamente após enviar
          await this.buscarNovasMensagens();
        } else {
          cSwal.cError('Erro ao enviar mensagem');
        }
      } catch (error) {
        console.error('Erro ao enviar mensagem:', error);
        cSwal.cError('Erro ao enviar mensagem');
      } finally {
        this.enviandoMensagem = false;
      }
    },

    async recarregarMensagens() {
      if (!this.mentoriaId) return;

      try {
        const data = await getMentoria(this.mentoriaId);
        if (data) {
          // Atualizar tanto as mensagens quanto os dados da mentoria
          this.mentoria = data;
          this.mensagens = data.mensagens || [];

          await marcarMensagensComoLidas(this.mentoriaId);
        }
      } catch (error) {
        console.error('Erro ao recarregar mensagens:', error);
      }
    },

    isMinhaMsg(remetenteId) {
      // Verifica se a mensagem é minha (do usuário logado)
      const meuDentistaId = this.$user?.dentista?.id;
      return remetenteId === meuDentistaId;
    },

    isMentor(remetenteId) {
      // Verifica se o remetente é um mentor (não é o solicitante da mentoria)
      return remetenteId !== this.mentoria?.solicitante_id;
    },

    getStatusClass(status) {
      const classes = {
        'AGUARDANDO': 'badge-warning',
        'EM_ANDAMENTO': 'badge-primary',
        'FINALIZADA': 'badge-success',
        'CANCELADA': 'badge-danger'
      };
      return classes[status] || 'badge-secondary';
    },

    getStatusText(status) {
      const texts = {
        'AGUARDANDO': 'Aguardando',
        'EM_ANDAMENTO': 'Em Andamento',
        'FINALIZADA': 'Finalizada',
        'CANCELADA': 'Cancelada'
      };
      return texts[status] || status;
    },

    podeInteragir() {
      // Verifica se o usuário pode interagir com a mentoria
      const meuDentistaId = this.$user?.dentista?.id;
      return this.$user?.system_admin || this.mentoria?.solicitante_id === meuDentistaId;
    },

    async iniciarMentoria() {
      if (!this.mentoriaId || this.processandoAcao) return;

      this.processandoAcao = true;

      try {
        const sucesso = await iniciarMentoriaService(this.mentoriaId);

        if (sucesso) {
          cSwal.cSuccess('Mentoria iniciada com sucesso!');
          await this.recarregarMensagens();
          this.$emit('mentoria-atualizada');
        } else {
          cSwal.cError('Erro ao iniciar mentoria');
        }
      } catch (error) {
        console.error('Erro ao iniciar mentoria:', error);
        cSwal.cError('Erro ao iniciar mentoria');
      } finally {
        this.processandoAcao = false;
      }
    },

    async finalizarMentoria() {
      if (!this.mentoriaId || this.processandoAcao) return;

      // Confirmar ação
      cSwal.cConfirm('Deseja realmente finalizar esta mentoria?', async () => {
        this.processandoAcao = true;

        try {
          const sucesso = await finalizarMentoriaService(this.mentoriaId);

          if (sucesso) {
            cSwal.cSuccess('Mentoria finalizada com sucesso!');
            await this.recarregarMensagens();
            this.$emit('mentoria-atualizada');
          } else {
            cSwal.cError('Erro ao finalizar mentoria');
          }
        } catch (error) {
          console.error('Erro ao finalizar mentoria:', error);
          cSwal.cError('Erro ao finalizar mentoria');
        } finally {
          this.processandoAcao = false;
        }
      });
    },

    async cancelarMentoria() {
      if (!this.mentoriaId || this.processandoAcao) return;

      // Confirmar ação
      cSwal.cConfirm('Deseja realmente cancelar esta mentoria?', async () => {
        this.processandoAcao = true;

        try {
          const sucesso = await cancelarMentoriaService(this.mentoriaId);

          if (sucesso) {
            cSwal.cSuccess('Mentoria cancelada com sucesso!');
            await this.recarregarMensagens();
            this.$emit('mentoria-atualizada');
          } else {
            cSwal.cError('Erro ao cancelar mentoria');
          }
        } catch (error) {
          console.error('Erro ao cancelar mentoria:', error);
          cSwal.cError('Erro ao cancelar mentoria');
        } finally {
          this.processandoAcao = false;
        }
      });
    },

    async reabrirMentoria() {
      if (!this.mentoriaId || this.processandoAcao) return;

      // Confirmar ação
      cSwal.cConfirm('Deseja realmente reabrir esta mentoria? Ela voltará ao status "Aguardando".', async () => {
        this.processandoAcao = true;

        try {
          const sucesso = await reabrirMentoriaService(this.mentoriaId);

          if (sucesso) {
            cSwal.cSuccess('Mentoria reaberta com sucesso!');
            await this.recarregarMensagens();
            this.$emit('mentoria-atualizada');
          } else {
            cSwal.cError('Erro ao reabrir mentoria');
          }
        } catch (error) {
          console.error('Erro ao reabrir mentoria:', error);
          cSwal.cError('Erro ao reabrir mentoria');
        } finally {
          this.processandoAcao = false;
        }
      });
    },



    abrirPaciente() {
      if (this.mentoria?.paciente?.clinica?.slug && this.mentoria?.paciente?.id_ficha) {
        this.$router.push({
          name: "PacienteClinica",
          params: {
            clinica_slug: this.mentoria.paciente.clinica.slug,
            id_ficha: this.mentoria.paciente.id_ficha,
          }
        });
        // Fechar o modal
        closeModal('modalMentoria');
      }
    },

    scrollToBottom() {
      const container = this.$refs.mensagensContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },

    startPollingInteligente() {
      // Polling a cada 5 segundos para novas mensagens
      this.intervalId = setInterval(() => {
        this.buscarNovasMensagens();
      }, 5000);
    },

    stopPolling() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },

    async buscarNovasMensagens() {
      if (!this.mentoriaId || this.mensagens.length === 0) {
        // Se não há mensagens ainda, fazer reload completo
        await this.recarregarMensagens();
        return;
      }

      try {
        // Pegar o ID da última mensagem
        const ultimaMensagemId = this.mensagens[this.mensagens.length - 1].id;

        // Buscar mensagens mais recentes que a última
        const data = await getMentoria(this.mentoriaId);

        if (data && data.mensagens) {
          // Filtrar apenas mensagens novas
          const mensagensNovas = data.mensagens.filter(msg => msg.id > ultimaMensagemId);

          if (mensagensNovas.length > 0) {
            // Adicionar apenas as mensagens novas
            mensagensNovas.forEach(novaMensagem => {
              // Marcar como nova para animação
              novaMensagem.isNovaMensagem = true;
              this.mensagens.push(novaMensagem);

              // Remover flag após animação
              setTimeout(() => {
                novaMensagem.isNovaMensagem = false;
              }, 300);
            });

            // Scroll para o final
            this.$nextTick(() => {
              this.scrollToBottom();
            });

            console.log(`${mensagensNovas.length} nova(s) mensagem(ns) adicionada(s)`);
          }
        }
      } catch (error) {
        console.error('Erro ao buscar novas mensagens:', error);
        // Em caso de erro, fazer reload completo como fallback
        await this.recarregarMensagens();
      }
    },



    fecharModal() {
      this.stopPolling();
      this.mentoria = null;
      this.mensagens = [];
      this.novaMensagem = '';
      this.mentoriaId = null;
      // Reset dos estados de imagem
      this.currentView = 'chat';
      this.selectedImages = [];
      this.enviandoImagens = false;
      this.pacienteImagens = [];
      this.carregandoImagens = false;
      // Reset dos estados de IA
      this.mensagemRefinadaIA = null;
      this.mensagemOriginalIA = '';
      this.processandoIA = false;
      // Reset da tab ativa
      this.activeTab = 'chat';
    },

    // Métodos para funcionalidade de imagens
    async openImageGallery() {
      if (!this.mentoria?.paciente?.id) {
        cSwal.cError('Paciente não identificado.');
        return;
      }

      // Evita múltiplas chamadas simultâneas
      if (this.carregandoImagens) {
        return;
      }

      // Carregar imagens do paciente
      this.carregandoImagens = true;
      this.currentView = 'gallery'; // Muda para galeria imediatamente para mostrar loading

      try {
        if (!this.mentoria?.paciente?.id) {
          cSwal.cError('Paciente não disponível.');
          this.backToChat();
          return;
        }

        const imagens = await getImagensPorPaciente(this.mentoria.paciente.id);

        if (!imagens || imagens.length === 0) {
          cSwal.cInfo('Este paciente ainda não possui imagens cadastradas.');
          this.backToChat(); // Volta para o chat se não há imagens
          return;
        }

        this.pacienteImagens = imagens;
        this.selectedImages = [];

      } catch (error) {
        console.error('Erro ao carregar imagens:', error);
        cSwal.cError('Erro ao carregar imagens do paciente.');
        this.backToChat(); // Volta para o chat em caso de erro
      } finally {
        this.carregandoImagens = false;
      }
    },

    backToChat() {
      this.currentView = 'chat';
      this.selectedImages = [];
      this.pacienteImagens = []; // Limpa as imagens carregadas
    },

    backToGallery() {
      this.currentView = 'gallery';
    },

    proceedToObservations(selectedImages) {
      this.selectedImages = selectedImages;
      this.currentView = 'observations';
    },

    updateSelectedImages(updatedImages) {
      this.selectedImages = updatedImages;
    },

    onImageError(event) {
      console.error('Erro ao carregar imagem:', event.target.src);
      event.target.style.display = 'none';
    },

    async sendImagesMessage(imagesWithObservations) {
      if (!imagesWithObservations || imagesWithObservations.length === 0) {
        cSwal.cError('Nenhuma imagem selecionada');
        return;
      }

      this.enviandoImagens = true;

      try {
        // Prepara a mensagem com referências às imagens
        const mensagemTexto = imagesWithObservations.length === 1
          ? 'Compartilhou 1 imagem'
          : `Compartilhou ${imagesWithObservations.length} imagens`;

        // Cria o objeto de mensagem com imagens
        const mensagemData = {
          mentoria_id: this.mentoriaId,
          mensagem: mensagemTexto,
          imagens: imagesWithObservations.map(img => ({
            id: img.id,
            url: img.url,
            url_preview: img.url_preview,
            url_original: img.url_original,
            description: img.descricao || '',
            observation: img.observation || '',
            data: img.data
          }))
        };

        const sucesso = await this.enviarMensagemComImagens(mensagemData);

        if (sucesso) {
          cSwal.cSuccess('Imagens enviadas com sucesso!');
          this.backToChat();
          // Garante que está na tab do chat
          this.activeTab = 'chat';
          // Buscar novas mensagens imediatamente após enviar imagens
          await this.buscarNovasMensagens();
        } else {
          cSwal.cError('Erro ao enviar imagens');
        }
      } catch (error) {
        console.error('Erro ao enviar imagens:', error);
        cSwal.cError('Erro ao enviar imagens');
      } finally {
        this.enviandoImagens = false;
      }
    },

    async enviarMensagemComImagens(mensagemData) {
      try {
        console.log('Enviando mensagem com imagens:', mensagemData);

        // Usar o novo serviço que suporta imagens
        const sucesso = await enviarMensagemComImagens(
          mensagemData.mentoria_id,
          mensagemData.mensagem,
          mensagemData.imagens
        );

        return sucesso;
      } catch (error) {
        console.error('Erro ao enviar mensagem com imagens:', error);
        return false;
      }
    },

    // Métodos para funcionalidade de IA
    async refinarMensagemComIA() {
      if (!this.novaMensagem.trim() || this.processandoIA) return;

      this.processandoIA = true;
      this.mensagemOriginalIA = this.novaMensagem;

      try {
        const resultado = await refinarMensagemComIAService(this.novaMensagem.trim());

        if (resultado && resultado.mensagem_refinada) {
          this.mensagemRefinadaIA = resultado.mensagem_refinada;

          // Scroll para o início do preview da IA
          this.$nextTick(() => {
            this.scrollToIAPreview();
          });
        } else {
          cSwal.cError('Erro ao refinar mensagem com IA');
        }
      } catch (error) {
        console.error('Erro ao refinar mensagem:', error);
        cSwal.cError('Erro ao refinar mensagem com IA');
      } finally {
        this.processandoIA = false;
      }
    },

    async gerarNovaMensagemIA() {
      if (!this.mensagemOriginalIA || this.processandoIA) return;

      this.processandoIA = true;

      try {
        const resultado = await refinarMensagemComIAService(this.mensagemOriginalIA);

        if (resultado && resultado.mensagem_refinada) {
          this.mensagemRefinadaIA = resultado.mensagem_refinada;

          // Scroll para o início do preview da IA
          this.$nextTick(() => {
            this.scrollToIAPreview();
          });
        } else {
          cSwal.cError('Erro ao gerar nova mensagem');
        }
      } catch (error) {
        console.error('Erro ao gerar nova mensagem:', error);
        cSwal.cError('Erro ao gerar nova mensagem');
      } finally {
        this.processandoIA = false;
      }
    },

    cancelarMensagemIA() {
      this.mensagemRefinadaIA = null;
      this.mensagemOriginalIA = '';

      // Restaurar a altura do textarea
      this.$nextTick(() => {
        const textarea = this.$refs.mensagemTextarea;
        if (textarea) {
          textarea.style.height = 'auto';
        }
      });
    },

    async enviarMensagemRefinada() {
      if (!this.mensagemRefinadaIA || this.enviandoMensagem) return;

      // Substituir a mensagem original pela refinada
      this.novaMensagem = this.mensagemRefinadaIA;
      this.mensagemRefinadaIA = null;
      this.mensagemOriginalIA = '';

      // Enviar a mensagem
      await this.enviarMensagem();
    },

    scrollToIAPreview() {
      const preview = this.$refs.mensagemIAPreview;
      if (preview) {
        preview.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    },

    ajustarAlturaTextarea() {
      const textarea = this.$refs.mensagemTextarea;
      if (!textarea) return;

      // Resetar altura para calcular corretamente
      textarea.style.height = 'auto';

      // Calcular altura baseada no conteúdo
      const scrollHeight = textarea.scrollHeight;
      const lineHeight = parseInt(window.getComputedStyle(textarea).lineHeight) || 24;
      const minRows = 2;
      const maxRows = 8;
      const minHeight = lineHeight * minRows;
      const maxHeight = lineHeight * maxRows;

      // Só aumenta a altura se o conteúdo ultrapassar o mínimo
      if (scrollHeight > minHeight) {
        const newHeight = Math.min(scrollHeight, maxHeight);
        textarea.style.height = newHeight + 'px';
      } else {
        // Mantém altura mínima
        textarea.style.height = minHeight + 'px';
      }
    }
  },

  mounted() {
    // Escutar evento de fechamento do modal
    const modalElement = this.$refs.modalMentoria;
    if (modalElement) {
      modalElement.addEventListener('hidden.bs.modal', () => {
        this.fecharModal();
      });
    }

    // Iniciar interval para verificar e ajustar altura do textarea periodicamente
    // Isso garante que o textarea se ajuste mesmo quando o texto é colado ou preenchido programaticamente
    this.textareaResizeIntervalId = setInterval(() => {
      this.ajustarAlturaTextarea();
    }, 1000); // Verifica a cada 1 segundo
  },

  beforeUnmount() {
    // Limpar interval do textarea
    if (this.textareaResizeIntervalId) {
      clearInterval(this.textareaResizeIntervalId);
      this.textareaResizeIntervalId = null;
    }

    this.fecharModal();
  }
};
</script>

<style scoped>
/* ===== TABS PARA MOBILE ===== */
.mentoria-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  flex-shrink: 0;
  z-index: 100;
}

.mentoria-tab {
  flex: 1;
  padding: 0.875rem 1rem;
  background: transparent;
  border: none;
  color: #6c757d;
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.mentoria-tab:hover {
  background: rgba(0, 0, 0, 0.03);
  color: #495057;
}

.mentoria-tab.active {
  color: #0d6efd;
  background: white;
  font-weight: 600;
}

.mentoria-tab.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #0d6efd, #0a58ca);
  border-radius: 3px 3px 0 0;
}

.mentoria-tab i {
  font-size: 1rem;
}

.mentoria-tab .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Modal Dialog Sizing */
.mentoria-modal-dialog {
  max-width: 1200px;
  max-height: 80vh;
  width: 90vw;
  height: 80vh;
  margin: 1.75rem auto;
  display: flex;
  align-items: center;
}

@media (min-width: 992px) and (max-width: 1399px) {
  .mentoria-modal-dialog {
    max-width: 1200px;
    max-height: 80vh;
    width: 90vw;
    height: 80vh;
  }
}

@media (min-width: 1400px) {
  .mentoria-modal-dialog {
    max-width: 1200px;
    max-height: 80vh;
    width: 85vw;
    height: auto;
  }
}

/* Força a altura máxima do modal */
.mentoria-modal-dialog .modal-content {
  max-height: 80vh;
  height: 100%;
  width: 100%;
}

/* Ajustes para mobile fullscreen */
@media (max-width: 991px) {
  .modal-fullscreen-lg-down {
    max-width: 100%;
    margin: 0;
    height: 100vh;
    max-height: 100vh;
  }

  .modal-fullscreen-lg-down .modal-content {
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }
}

.mentoria-modal {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mentoria-modal .modal-body {
  flex: 1;
  overflow: hidden;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* Container principal com CSS Grid */
.mentoria-container {
  display: grid;
  grid-template-columns: 42% 58%; /* 5/12 ≈ 42%, 7/12 ≈ 58% */
  height: 100%;
  overflow: hidden;
  flex: 1;
  min-height: 0;
}

/* Em mobile, cada lado ocupa 100% da largura */
@media (max-width: 991px) {
  .mentoria-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
  }

  /* Garante que cada lado ocupe toda a altura disponível */
  .mentoria-info-side,
  .mentoria-chat-side {
    height: 100%;
    width: 100%;
  }
}

/* ===== LADO ESQUERDO - INFORMAÇÕES ===== */
.mentoria-info-side {
  display: grid;
  grid-template-rows: auto auto 1fr auto;
  overflow: hidden;
  min-height: 0;
}

.mentoria-info-content {
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
}

/* ===== LADO DIREITO - CHAT ===== */
.mentoria-chat-side {
  display: grid;
  grid-template-rows: auto 1fr auto;
  overflow: hidden;
  min-height: 0;
}

.mentoria-chat-content {
  overflow: hidden;
  position: relative;
  min-height: 0;
}

.mentoria-chat-footer {
  max-height: 40vh;
  overflow-y: auto;
  flex-shrink: 0;
}

/* Ajustes específicos para mobile */
@media (max-width: 991px) {
  .mentoria-chat-footer {
    max-height: 35vh;
  }
}

/* Header do Paciente */
.paciente-header {
  border-radius: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 0.85rem 1rem;
}

.paciente-avatar .avatar-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 1.2rem;
}

/* Layout compacto para telas pequenas */
@media (max-height: 800px) {
  .paciente-header {
    padding: 0.6rem 0.85rem !important;
  }

  .paciente-avatar .avatar-circle {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .paciente-header h5 {
    font-size: 1rem !important;
    margin-bottom: 0.25rem !important;
  }

  .paciente-header p {
    font-size: 0.8rem !important;
  }

  .paciente-header .btn-sm {
    padding: 0.25rem 0.5rem !important;
    font-size: 0.75rem !important;
  }
}

/* Header do Mentor */
.mentor-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  color: #475569 !important;
  border-bottom: 1px solid #cbd5e1 !important;
}

.mentor-avatar .avatar-circle {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  font-size: 1.1rem;
  background: #3b82f6 !important;
  color: white !important;
}

/* Header do mentor compacto para telas pequenas */
@media (max-height: 800px) {
  .mentor-header {
    padding: 0.6rem !important;
  }

  .mentor-avatar .avatar-circle {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }

  .mentor-header h6 {
    font-size: 0.9rem !important;
    margin-bottom: 0.25rem !important;
  }

  .mentor-header p {
    font-size: 0.75rem !important;
  }

  .mentor-header small {
    font-size: 0.7rem !important;
  }

  .mentor-header .fw-bold.small {
    font-size: 0.75rem !important;
  }
}

.observacoes-box {
  overflow-y: auto;
  font-size: 0.9rem;
  line-height: 1.5;
  background: #f8f9fa !important;
  border: 1px solid #dee2e6 !important;
  transition: border-color 0.2s ease;
  min-height: 80px;
}

.observacoes-box:hover {
  border-color: #adb5bd !important;
}

/* Observações compactas para telas pequenas */
@media (max-height: 800px) {
  .observacoes-box {
    font-size: 0.8rem;
    line-height: 1.4;
    padding: 0.5rem !important;
    min-height: 60px;
  }
}

/* Container de mensagens */
.mensagens-container {
  overflow-y: auto;
  background: #f8f9fa;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.mensagem-item.nova-mensagem {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mensagem-card {
  padding: 10px 14px;
  border-radius: 12px;
  max-width: 85%;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.mensagem-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Mensagens mais compactas em telas médias e menores */
@media (max-width: 991px) {
  .mensagem-card {
    padding: 9px 12px;
  }

  .mensagem-usuario {
    margin-bottom: 9px;
  }
}

/* Mensagens do Sistema */
.mensagem-sistema {
  margin: 16px 0;
}

.sistema-badge {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  padding: 7px 14px;
  border-radius: 20px;
  font-size: 0.825rem;
  display: inline-block;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

@media (max-width: 991px) {
  .mensagem-sistema {
    margin: 12px 0;
  }

  .sistema-badge {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

@media (max-height: 700px) {
  .mensagem-sistema {
    margin: 10px 0;
  }

  .sistema-badge {
    padding: 5px 10px;
    font-size: 0.775rem;
  }
}

/* Layout das mensagens */
.mensagem-usuario {
  display: flex;
  margin-bottom: 10px;
}

.mensagem-direita {
  justify-content: flex-end;
}

/* Mensagens minhas (à direita) */
.mensagem-minha {
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  color: white;
  border-bottom-right-radius: 4px;
}

.mensagem-minha .mensagem-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 7px;
  margin-bottom: 7px;
}

@media (max-width: 991px) {
  .mensagem-minha .mensagem-header {
    padding-bottom: 5px;
    margin-bottom: 5px;
  }
}

@media (max-height: 700px) {
  .mensagem-minha .mensagem-header {
    padding-bottom: 5px;
    margin-bottom: 5px;
  }
}

.mensagem-minha .remetente-info i {
  color: rgba(255, 255, 255, 0.9);
}

.mensagem-minha .text-muted {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* Mensagens dos outros (à esquerda) */
.mensagem-outro {
  background: white;
  color: #333;
  border: 1px solid #e9ecef;
  border-bottom-left-radius: 4px;
}

.mensagem-outro .mensagem-header {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 7px;
  margin-bottom: 7px;
}

@media (max-width: 991px) {
  .mensagem-outro .mensagem-header {
    padding-bottom: 5px;
    margin-bottom: 5px;
  }
}

@media (max-height: 700px) {
  .mensagem-outro .mensagem-header {
    padding-bottom: 5px;
    margin-bottom: 5px;
  }
}

.mensagem-outro .remetente-info i {
  color: #6c757d;
}

.mensagem-conteudo {
  font-size: 0.875rem;
  line-height: 1.35;
  white-space: pre-wrap;
}

/* Conteúdo mais compacto em telas médias e menores */
@media (max-width: 991px) {
  .mensagem-conteudo {
    font-size: 0.85rem;
    line-height: 1.3;
  }

  .mensagem-header {
    font-size: 0.825rem;
  }

  .mensagem-header .fw-bold {
    font-size: 0.825rem;
  }

  .mensagem-header small {
    font-size: 0.725rem;
  }
}

/* Ajustes para telas com altura reduzida */
@media (max-height: 700px) {
  .mensagem-conteudo {
    font-size: 0.85rem;
    line-height: 1.3;
  }

  .mensagem-header {
    font-size: 0.825rem;
  }

  .mensagem-header .fw-bold {
    font-size: 0.825rem;
  }

  .mensagem-header small {
    font-size: 0.725rem;
  }

  .mensagem-card {
    padding: 9px 12px;
  }

  .mensagem-usuario {
    margin-bottom: 8px;
  }
}

.mensagem-input-area,
.mentoria-chat-footer {
  border-top: 2px solid #dee2e6;
}

.mensagem-input-area textarea {
  border-radius: 10px;
  border: 1px solid #ced4da;
  resize: none;
  transition: height 0.3s ease, background-color 0.2s ease, border-color 0.2s ease; /* Animação suave no resize */
  overflow-y: auto;
  line-height: 1.5;
  padding-right: 60px; /* Espaço para o botão de IA maior */
  background-color: #F8FFFF; /* Background mais claro */
}

.mensagem-input-area textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  background-color: #F8FFFF; /* Mantém a cor no foco */
}

/* Estilo específico para textarea com auto-resize */
.textarea-auto-resize {
  background-color: #F8FFFF !important; /* Background mais claro */
  transition: height 0.3s ease, background-color 0.2s ease, border-color 0.2s ease; /* Animação suave no resize */
}

.textarea-auto-resize:focus {
  background-color: #F8FFFF !important; /* Mantém a cor no foco */
}

/* Botão de IA dentro do textarea - sempre visível e fixo */
.btn-ia-refinar {
  position: absolute;
  right: 18px;
  bottom: 36px; /* Posição fixa, não usa % */
  background: transparent;
  border: none;
  padding: 0;
  cursor: not-allowed;
  z-index: 10;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

/* Estado inativo (desabilitado) */
.btn-ia-refinar:disabled .icone-ia {
  filter: brightness(0) saturate(100%) invert(80%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(95%) contrast(90%);
  /* Cinza claro */
  opacity: 0.65; /* Aumentado de 0.4 para 0.65 */
}

/* Estado ativo (pode refinar) */
.btn-ia-refinar.btn-ia-ativo {
  cursor: pointer;
}

.btn-ia-refinar.btn-ia-ativo .icone-ia {
  filter: brightness(0) saturate(100%) invert(42%) sepia(93%) saturate(2878%) hue-rotate(200deg) brightness(102%) contrast(101%);
  /* Azul vibrante #1a73e8 */
  opacity: 1;
  animation: pulseGlow 2.5s ease-in-out infinite;
}

.btn-ia-refinar.btn-ia-ativo:hover .icone-ia {
  transform: scale(1.2);
  filter: brightness(0) saturate(100%) invert(50%) sepia(93%) saturate(2878%) hue-rotate(200deg) brightness(110%) contrast(101%);
  /* Azul mais claro no hover */
}

.btn-ia-refinar.btn-ia-ativo:active .icone-ia {
  transform: scale(0.95);
}

/* Ícone de IA - SVG customizado */
.icone-ia {
  width: 28px;
  height: 28px;
  transition: all 0.3s ease;
}

/* Animação de brilho pulsante (só quando ativo) */
@keyframes pulseGlow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.75;
  }
}

/* Spinner da IA - sem fundo */
.spinner-ia {
  width: 28px;
  height: 28px;
  border-width: 2.5px;
  border-color: #1a73e8;
  border-right-color: transparent;
}

/* Container dos botões de ação - alinhamento dinâmico */
.botoes-acao-container {
  margin-bottom: 22px; /* Alinha com a base do textarea (considerando o small text) */
}

/* Balão temporário da IA */
.mensagem-ia-preview {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border: 2px solid #667eea;
  border-radius: 12px;
  padding: 16px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mensagem-ia-header {
  font-size: 0.9rem;
}

.mensagem-ia-conteudo {
  font-size: 0.95rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
  resize: vertical; /* Permite redimensionar verticalmente */
  min-height: 150px;
}

.mensagem-ia-conteudo:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  outline: none;
}

.mensagem-ia-acoes {
  justify-content: flex-end;
}

.mensagem-ia-acoes .btn {
  font-size: 0.85rem;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.mensagem-ia-acoes .btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-enviar-mensagem {
  min-height: 56px;
  height: auto;
  border-radius: 10px;
  font-weight: 500;
  padding: 12px 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 0.95rem;
}

.btn-enviar-mensagem:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.btn-enviar-imagens {
  min-height: 56px;
  width: 56px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: all 0.2s ease;
  padding: 0;
}

.btn-enviar-imagens:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
}

/* Estilos para mensagens com imagens */
.mensagem-texto {
  margin-bottom: 0;
}

.mensagem-imagens {
  margin-top: 0.75rem;
}

.imagens-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  max-width: 100%;
  width: 100%;
}

.imagem-referenciada {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.imagem-referenciada:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.imagem-referenciada img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.imagem-observacao {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 1rem 0.75rem 0.75rem;
  font-size: 0.85rem;
  line-height: 1.3;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Ajustes para o container principal */
.main-content {
  position: relative;
  overflow: hidden;
  width: 100% !important;
}

/* Animações para transições */
.mensagens-container {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Animação para o botão de imagens */
.btn-enviar-imagens {
  position: relative;
  overflow: hidden;
}

.btn-enviar-imagens::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.btn-enviar-imagens:hover::before {
  width: 100%;
  height: 100%;
}

/* Estilos para mensagens com imagens */
.mensagem-imagens {
  margin-top: 0.5rem;
  width: 100%;
}

/* Animação para imagens referenciadas - apenas para novas mensagens */
.imagem-referenciada.nova-mensagem {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Header responsivo */
.mensagens-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media (max-width: 768px) {
  .imagens-grid {
    grid-template-columns: 1fr;
  }

  .mensagens-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .mensagens-header h6 {
    text-align: center;
  }
}

/* Conteúdo do card com scroll */
.conteudo-card {
  overflow-y: auto;
  overflow-x: hidden;
}

.conteudo-card::-webkit-scrollbar {
  width: 6px;
}

.conteudo-card::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.conteudo-card::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 10px;
}

.conteudo-card::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Utilitário para truncar texto */
.min-w-0 {
  min-width: 0;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-section {
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.info-section h6 {
  font-size: 0.9rem;
  margin-bottom: 0.75rem;
}

.info-item {
  padding: 8px 0;
  transition: background-color 0.2s ease;
}

.info-item:not(:last-child) {
  border-bottom: 1px solid #f1f3f4;
}

.info-item:hover {
  background-color: rgba(0, 123, 255, 0.02);
  border-radius: 6px;
  margin: 0 -6px;
  padding: 8px 6px;
}

.info-item .form-label {
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.info-item .fw-bold {
  font-size: 0.85rem;
}

/* Card de detalhes compacto para telas pequenas */
@media (max-height: 800px) {
  .info-section {
    padding: 10px;
    margin-bottom: 10px !important;
  }

  .info-section h6 {
    font-size: 0.85rem !important;
    margin-bottom: 0.5rem !important;
  }

  .info-item {
    padding: 6px 0;
  }

  .info-item .form-label {
    font-size: 0.7rem !important;
  }

  .info-item .fw-bold {
    font-size: 0.8rem !important;
  }

  .info-item .row .col-7,
  .info-item .row .col-5 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}

.mensagens-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Botões de Ação */
.acoes-mentoria {
  margin-top: auto; /* Empurra os botões para o final */
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  flex-shrink: 0; /* Impede que os botões sejam comprimidos */
}

.acoes-mentoria .btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

.acoes-mentoria .btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Botões lado a lado - layout responsivo */
.acoes-mentoria .d-flex.gap-2 {
  gap: 0.5rem !important;
}

.acoes-mentoria .btn.flex-fill {
  min-width: 0; /* Permite que os botões encolham se necessário */
}

/* Ajustes para telas pequenas */
@media (max-height: 800px) {
  .acoes-mentoria {
    padding: 0.4rem 0.75rem !important;
  }

  .acoes-mentoria .btn {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }
}

/* Botões compactos para telas pequenas */
@media (max-height: 800px) {
  .acoes-mentoria {
    padding: 0.75rem !important;
  }

  .acoes-mentoria .btn {
    padding: 8px 16px;
    font-size: 0.85rem;
    border-radius: 6px;
  }

  .acoes-mentoria .d-grid {
    gap: 0.5rem !important;
  }

  .acoes-mentoria .btn i {
    font-size: 0.8rem;
  }

  .acoes-mentoria .spinner-border-sm {
    width: 0.8rem;
    height: 0.8rem;
  }
}

/* Status badges */
.badge-warning {
  background-color: #ffc107;
  color: #000;
}

.badge-primary {
  background-color: #0d6efd;
  color: #fff;
}

.badge-success {
  background-color: #198754;
  color: #fff;
}

.badge-danger {
  background-color: #dc3545;
  color: #fff;
}

/* Scrollbar personalizada */
.mensagens-container::-webkit-scrollbar {
  width: 6px;
}

.mensagens-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.mensagens-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.mensagens-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ajustes para containers principais */
.row.g-0.h-100 {
  height: 100% !important;
  min-height: 0;
}

.col-md-5 {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  overflow: hidden;
}

/* Para telas pequenas, adicionar scroll */
@media (max-height: 800px) {
  .col-md-5 {
    overflow-y: auto;
  }

  .conteudo-card {
    padding: 1rem !important;
  }
}

/* Para telas muito pequenas (menos de 600px de altura) */
@media (max-height: 670px) {
  .mentoria-modal-dialog {
    height: 98vh !important;
    max-height: 98vh !important;
  }

  .paciente-header {
    padding: 0.5rem !important;
  }

  .mentor-header {
    padding: 0.5rem !important;
  }

  .conteudo-card {
    padding: 0.75rem !important;
  }

  .info-section {
    padding: 10px;
    margin-bottom: 10px !important;
  }

  .info-section h6 {
    font-size: 0.85rem !important;
    margin-bottom: 10px !important;
  }

  .info-item {
    padding: 6px 0;
  }

  .observacoes-box {
    font-size: 0.75rem;
  }

  .acoes-mentoria {
    padding: 0.5rem !important;
  }

  .acoes-mentoria .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

.col-md-7 {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
  overflow: hidden;
}

/* Responsividade */
@media (max-width: 768px) {
  .mentoria-modal-dialog {
    width: 95vw;
    height: 95vh;
    max-width: none;
    max-height: none;
  }

  .mensagem-card {
    max-width: 95%;
  }

  .col-md-5, .col-md-7 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .paciente-header {
    padding: 1rem !important;
  }

  .paciente-avatar .avatar-circle {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  /* Em mobile, empilhar verticalmente */
  .row.g-0.h-100.flex-nowrap {
    flex-direction: column;
    flex-wrap: wrap;
  }

  .col-md-5 {
    max-height: 40%;
    overflow-y: auto;
  }

  .col-md-7 {
    flex: 1;
    min-height: 0;
  }
}
</style>
