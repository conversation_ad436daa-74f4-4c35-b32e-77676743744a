<template>
  <div class="global-layout-container">
    <!-- <PERSON><PERSON><PERSON>do principal da página -->
    <div class="global-layout-content">
      <slot />
    </div>

    <!-- Footer sutil e profissional -->
    <footer class="global-layout-footer" v-if="shouldShowFooter">
      <div class="footer-content">
        <p class="footer-copyright">© {{ currentYear }} LUMI Vision</p>
      </div>
    </footer>
  </div>
</template>

<script>
export default {
  name: "GlobalLayout",
  computed: {
    currentYear() {
      return new Date().getFullYear();
    },
    shouldShowFooter() {
      // Não mostrar footer na página de Início (ela já tem o próprio footer)
      return this.$route.name !== 'Inicio';
    }
  }
};
</script>

<style scoped>
.global-layout-container {
  min-height: calc(100vh - 48px); /* Descontar altura do TabNavigation */
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
}

/* Quando há tab navigation, ajustar altura - será controlado pelo CSS global */

.global-layout-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Permite que o conteúdo se ajuste */
}

/* Footer elegante baseado no da tela de Início */
.global-layout-footer {
  margin-top: auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 251, 255, 0.95) 100%);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(44, 130, 201, 0.15);
  padding: 0.8rem 0;
  position: relative;
  z-index: 10;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-copyright {
  font-size: 0.75rem;
  color: rgba(44, 130, 201, 0.8);
  margin: 0;
  font-weight: 500;
  letter-spacing: 0.3px;
  text-align: center;
}

/* Responsividade */
@media (max-width: 768px) {
  .global-layout-container {
    min-height: calc(100vh - 48px); /* TabNavigation mantém 48px em mobile */
  }

  .global-layout-footer {
    padding: 0.7rem 0;
  }

  .footer-copyright {
    font-size: 0.7rem;
  }
}

@media (max-width: 576px) {
  .global-layout-footer {
    padding: 0.6rem 0;
  }

  .footer-copyright {
    font-size: 0.65rem;
  }
}
</style>
