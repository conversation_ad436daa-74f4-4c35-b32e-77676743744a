☐ V2/V3?: Fazer dashboards analíticos de marketing
☐ Armazenar miniatura das imagens
☐ Preparar a ficha de avaliação inicial para permitir anonimo (armazenar formulário para ser vinculado a um paciente depois)
☐ Finalizar implementação de multi-idiomas
Limitar requests:
    ☐ Pacientes
    ☐ Profissionals
    ☐ Consultas de pacientes
Laravel - aprender:
    ☐ Como fazer paginação do lado do servidor
☐ Fazer botões Dia/Semana/Mês terem um slide de background entre eles
☐ Colocar campo de indicação no cadastro do paciente
Financeiro:
    Tela do Financeiro:
        ☐ Criar tabela de contas
        ☐ Criar sidenav com opções pertinentes ao financeiro
    Tela do Paciente:
        ☐ Criar tabela de contas
        ☐ Criar sidenav com opções pertinentes ao financeiro
        
Front-end:
    Desenvolver:
        Colocar a sidenav nas outras telas também:
            ☐ Finanças
    ☐ Excluir arquivos que não forem ser usados
Backend:
    Verificar os models e migrations:
        ☐ Formulários
        ☐ Dentistas
        ☐ Consultas
        ☐ Agenda
    Verificar as tabelas do banco de dados (comparar com tabelas da CECLOP):
        ☐ Formulários
        ☐ Dentistas
    Tabela pacientes:
        ☐ picture_url
        ☐ status            
Ideas:
    ☐ Dar opção para o paciente consultar o seu plano de tratamento